// vite.config.ts
import { defineConfig } from "file:///Users/<USER>/hangtian_projects/dvia-video-surveillance-system/node_modules/.pnpm/vite@5.1.5_@types+node@20.5.0_less@4.2.0/node_modules/vite/dist/node/index.js";
import path from "path";
import react from "file:///Users/<USER>/hangtian_projects/dvia-video-surveillance-system/node_modules/.pnpm/@vitejs+plugin-react@4.2.1_vite@5.1.5_@types+node@20.5.0_less@4.2.0_/node_modules/@vitejs/plugin-react/dist/index.mjs";
var __vite_injected_original_dirname = "/Users/<USER>/hangtian_projects/dvia-video-surveillance-system";
var vite_config_default = defineConfig({
  plugins: [react()],
  resolve: {
    // 配置路径别名
    alias: { "@src": path.resolve(__vite_injected_original_dirname, "./src") }
  },
  publicDir: path.resolve(__vite_injected_original_dirname, "public"),
  css: {},
  build: { outDir: "build", chunkSizeWarningLimit: process.env.NODE_ENV === "production" ? 5e3 : 1500 },
  define: { global: "globalThis" },
  server: {
    proxy: {
      // 选项写法
      "/api": {
        // target: "http://192.168.11.13:2236",
        target: "http://192.168.11.12:2236",
        changeOrigin: true
        // rewrite: (path) => path.replace(/^\/api/, ""),
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
