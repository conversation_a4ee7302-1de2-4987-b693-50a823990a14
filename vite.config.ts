import { defineConfig } from "vite";
import path from "path";
import react from "@vitejs/plugin-react";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    // 配置路径别名
    alias: { "@src": path.resolve(__dirname, "./src") },
  },
  publicDir: path.resolve(__dirname, "public"),
  css: {},
  build: { outDir: "build", chunkSizeWarningLimit: process.env.NODE_ENV === "production" ? 5000 : 1500 },
  define: { global: "globalThis" },
  server: {
    proxy: {
      // 选项写法
      "/api": {
        // target: "http://192.168.11.13:2236",
        target: "http://192.168.11.12:2236",
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api/, ""),
      },
    },
  },
});
