# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
/.temp
/.vscode
# testing
/coverage

# production
/build/**/*
/test

/build

/ais-frontend.tar.gz

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.eslintcache
debug.log

npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

clear-tags.sh

