FROM harbor.topvdn.com/common/alpine:3.12.0
# 拷贝程序
ADD ./ais-frontend.tar.gz /opt

FROM harbor.topvdn.com/common/nginx:1.18.0-alpine



LABEL app=ais-frontend
LABEL maintainer="Docker Maintainers <<EMAIL>>"

ENV TZ=Asia/Shanghai

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories \
  && apk --no-cache add tzdata \
  && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
  && echo "$TZ" > /etc/timezone

COPY --from=0 --chown=101:101 /opt /opt/frontend

# 拷贝nginx配置
COPY ./nginx.conf /etc/nginx/conf.d/vmc.conf


EXPOSE 80