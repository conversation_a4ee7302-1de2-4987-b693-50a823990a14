## 介绍

结构化资源服务的原生应用前端，属于新版本微应用架构的单机版本，不依赖后台，支持`docker`部署，利用`pnpm workespace` 和 `git submodule`管理工程，`packages`下 4 个微应用，分别是基座`basic-frontend`、登录`login`、静态资源`statics`、结构化存储服务应用`ais-app`,四个子工程对应的分支为`dev-jx`


## 工程初始化

1. 克隆仓库
2. 初始化子工程`git submodule init && git submodule update`；
3. 切换分支`pnpm branch`,会把所有的子工程切换为`dev-ais`分支；
4. 安装依赖`pnpm install`安装所有依赖；
5. 启动服务`pnpm start`,这里会启动所有子工程；


## 目录结构
```
├── Dockerfile 
├── README.md
├── packages
│   ├── basic-frontend  //基座
│   ├── login           //登陆
│   ├── statics         //静态资源
│   └── ais-app         //vmc应用
│   └── system-management   //系统设置应用
├── build.sh            //编译脚本
├── compress.sh         //压缩编译目录脚本
├── install.sh          //工程初始化脚本
├── onekey-submit.sh    //一键提交所以子工程
├── package.json
├── pnpm-lock.yaml
├── pnpm-workspace.yaml
└── ais-nginx.conf
```

#### Nginx配置

参考`ais-nginx.conf`模板文件


#### 部署
- 执行 `bash build.sh`，完成后得到`ais-frontend.tar.gz`压缩文件
- 执行`docker build -t ais-frontend:${version} .` 构建镜像
- 执行`docker run -it --rm -p 2234:2234 ais-frontend:${version}` 启动容器
