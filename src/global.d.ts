import * as H from 'history';
declare global {
  interface Window {
    _PLATFORM_TYPE: Number;
    _IS_RUN_MICRO_BASIC: boolean;
    _IS_RUN_MICRO_MODULE: boolean;
    _IS_ALLOW_ROUTE_RENDER: boolean;
    _HOT_FEATURES_: any[];
    _BSIC_HISTORY_: H.History;
    __WB_MANIFEST: string[];
    _APP_VERSION: string;
  }
  namespace NodeJS {
    interface ProcessEnv {
      ROUTE_PERFIX: string;
      APP_TYPE: 'VIDEO_CLOUD' | 'EYE_CLOUD' | 'DAG';
      APP_MANIFEST: string;
      CACHE_ID: string;
      VERSION: string;
    }
  }
}

export {};
