* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
ul,
li {
  list-style: none;
  margin: 0;
}
html,
body,
#root {
  width: 100%;
  height: 100%;
  overflow: hidden;
  color: var(--color);
}
.ant-tabs, .ant-tabs-content, .ant-tabs-tabpane{
  height: 100%;
}
.ellipsis {
  display: -webkit-box !important;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
:root {
  // --primary: #319bff;
  --primary: #165dff !important;
  --primary-light: #248ffa;
  --primary-dark: #2587e3;
  --secondary1: #1d4788;
  --secondary1-light: #1dbbff;
  --secondary1-dark: #0f3066;
  --secondary2: #D54941 !important;
  --secondary2-light: rgba(232, 177, 39, 0.3);
  --secondary2-dark: #2194e5;
  --secondary3: #24ca92;
  --secondary3-light: #f8b578;
  --secondary3-dark: #dc914c;
  --danger: #f32d37;
  --danger-light: #ff4d4f;
  --danger-dark: #dc1b2a;
  --warn: #fd8535;
  --warn-light: #ff9f5e;
  --warn-dark: #dc1b2a;
  --success: #35bd77;
  --success-light: #55c98d;
  --success-dark: #21965a;
  --icon: #62708b;
  --gray1: #ffffff;
  --gray2: #f3f3f3;
  --gray3: #eeeeee;
  --gray4: #e7e7e7;
  --gray5: #dcdcdc;
  --gray6: rgba(0, 0, 0, 0.6) !important;
  --gray7: #a6a6a6;
  --gray8: #8b8b8b;
  --gray9: #777777;
  --gray10: #5e5e5e;
  --gray11: #4b4b4b;
  --gray12: #383838;
  --background: #071631;
  --content-bg: linear-gradient(0deg, #0f2c5b, #0f2c5b), linear-gradient(0deg, #0f2e61, #0f2e61), #0f3066;
  --title-bg: linear-gradient(90deg, rgba(36, 143, 250, 0.65) -1.22%, rgba(36, 143, 250, 0) 100%);
  --bd: rgba(255, 255, 255, 0.3);
  --fs: 14px;
  --fs-small: 12px;
  --fs-large: 16px;
  --color: #ffffff;
  --color1: #ffffff;
  --color2: #fefefe;
  --color3: #eeeeee;
  --color4: #999999;
  --color5: #666666;
  --color6: #333333;
  --color7: #000000;
  --shadow1: 2px 0 10px 0 rgba(0, 0, 0, 0.06);
  --shadow2: 2px 0 10px 0 rgba(0, 0, 0, 0.06);
  --shadow3: 2px 0 10px 0 rgba(0, 0, 0, 0.06);
  --radius1: 4px;
  --radius2: 6px;
  --radius3: 14px;
  --header-color: #ffffff;
  --header-bg: #0f2e61;
  --header-height: 56px;
  --table-bg: #0f2e61;
  --table-head-bg: #1d4788;
  --table-bd: rgba(255, 255, 255, 0.3);
  --table-row-hover-color: #1d4788;
  --form-bg: #1d4788;
  --form-bd: rgba(255, 255, 255, 0);
  --form-active-bg: #e8b127;
  --nav-height: 56px;
  --nav-bg: #071531;
  --nav-sub-bg: rgba(255, 255, 255, 1);
  --nav-sub-bg2: rgba(241, 244, 247, 0.8);
  --nav-item-bg-selected: #ffffff;
  --nav-item-color: rgba(255, 255, 255, 0.6);
  --nav-icon-color: rgba(255, 255, 255, 0.4);
  --nav-icon-color-selected: #3495ef;
  --nav-item-bg: #0f2e61;
  --nav-item-color-selected: rgba(0, 0, 0, 0.9);
  --nav-sub-item-bg-selected: #e8b127;
  --nav-sub-item-color-selected: #ffffff;
  --nav-sub-item-color: rgba(255, 255, 255, 0.9);
  --nav-bd-color: rgba(0, 0, 0, 0);
  --tab-height: 48px;
  --tab-bg: #f3f3f3;
  --tab-item-bg: #348fe2;
  --tab-item-height: 48px;
  --tab-item-color: rgba(255, 255, 255, 0.6);
  --tab-item-hover-bg: #1b74c8;
  --tab-item-selected-bg: #1b74c8;
  --tab-item-selected-color: #ffffff;
  --drawer-title-height: 40px;
  --drawer-title-bg: #0f3066;
  --drawer-divider-color: rgba(219, 225, 234, 1);
  --drawer-footer-height: 50px;
  --drawer-footer-bg: #0f3066;
  --drawer-content-bg: #0f3066;
  --modal-title-height: 44px;
  --modal-title-bg: #1d4788;
  --modal-divider-color: rgba(236, 240, 246, 0.3);
  --modal-footer-height: 44px;
  --modal-footer-bg: #1d4788;
  --modal-content-bg: #0f3066;
  --card-image-radius: 4px;
  --card-image-bg: rgba(219, 225, 234, 1);
  --card-radius: 4px;
  --card-content-bg: rgba(255, 255, 255, 1);
  --card-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
  --card-bd-color: rgba(219, 225, 234, 1);
  --card-hover-bd-color: rgba(18, 122, 255, 1);
  --card-footer-bg: rgba(219, 225, 234, 1);
  --scrollbar-width: 5px;
  --scrollbar-track-bg: #2d68ac;
  --scrollbar-thumb-bg: #2d68ac;
  --scrollbar-thumb-hover-bg: #2d68ac;
  --scrollbar-track-shadow: 0 0 3px 0 rgba(255, 255, 255, 0) inset;
  --scrollbar-thumb-shadow: 0 0 3px 0 rgba(255, 255, 255, 0) inset;
}

// 滚动条整体宽度
::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
} // 滚动条滑槽样式
::-webkit-scrollbar-track {
  box-shadow: var(--scrollbar-track-shadow);
  border-radius: var(--scrollbar-width);
}
// 滚动条样式
::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-bg);
  border-radius: var(--scrollbar-width);
  box-shadow: var(--scrollbar-thumb-shadow);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-bg);
}

::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-bg);
} // 浏览器失焦的样式

.ant-tooltip .ant-tooltip-inner {
  background-color: rgba(0, 0, 0, 1);
}
.ant-tooltip .ant-tooltip-arrow:before {
  background-color: rgba(0, 0, 0, 1);
}

.ant-tree,
.ant-picker-calendar {
  background-color: transparent !important;
}
.ant-modal .ant-modal-content {
  // padding: 0;
  padding: 32px;
}

.modal-comfire .ant-modal-content {
  // padding: 20px;
}
.modal-comfire .ant-modal-confirm-paragraph {
  // padding: 20px;
  width: 465px;

}
.ant-table-wrapper .ant-table-thead > tr > th {
  background: #fff;
  color: rgba(0, 0, 0, 0.4);
  font-family: "PingFang SC";
  font-size: 14px;
  font-weight: 400;
  &::before {
    display: none;
  }
}
.ant-table-wrapper .ant-table-tbody > tr > td {
  color: rgba(0, 0, 0, 0.9);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
