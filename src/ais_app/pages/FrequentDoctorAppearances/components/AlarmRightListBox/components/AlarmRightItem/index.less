// .alarm-right-item {
//   margin-right: 40px;
//   width: 220px;
//   height: 217px;
//   margin-bottom: 24px;
.evidence-card-dir-item {
  // &:first-child {
  //   margin-left: 0px;
  // }
  // margin-left: 24px;
  margin-right: 9px;
  width: 215px;
  height: 217px;
  margin-bottom: 24px;
  position: relative;
  border-radius: 0px;
  background-color: #f3f3f3;
  border: 1px solid #dcdcdc;
  .processFlags{
    display: flex;
padding: 0px 4px;
align-items: center;
border-radius: 2px 0px;
background:  #E3F9E9;
color:  #2BA471;
/* Body/Small */
font-family: "PingFang SC";
font-size: 12px;
font-style: normal;
font-weight: 400;
line-height: 20px; 
position: absolute;
top: 8px;
left: 8px;
  }
  &:last-child {
    margin-right: 0px;
  }
  .evidence-card-dir-item-unread {
    display: inline-block;
    width: 6px;
    height: 6px;
    background: #e34d59;
    border-radius: 6px;
    position: relative;
    top: -12px;
    left: 0px;
  }
  .evidence-card-dir-item-file-num {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 170px;
    display: inline-block;
    color: rgba(0, 0, 0, 0.6);
    /* Body/Small */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
  .evidence-card-dir-item-file-hideFlag{
    display: inline-flex;
height: 24px;
padding: 0px 4px;
align-items: center;
border-radius: 2px 0px;
background: rgba(0, 0, 0, 0.40);
color: #F3F3F3;
position: absolute;
top: calc(50% - 10px);
right: 10px;
  }
  .evidence-card-item__filename {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 185px;
    color: #000;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 150% */
  }
  .file-name-overflow__name {
    max-width: 145px;
  }
  .file-name-overflow {
    font-size: 16px;
    font-weight: 500;
    color: #000;
  }
  .ant-card-cover {
    width: 100%;
    height: 220px;
    // padding: 8px 8px 0;
    position: relative;
    margin-top: 0px;
    margin-right: 0px;
    margin-left: 0px;
    border-radius: 30px 30px 0px 0px;
  }
  .ant-card-meta-description {
    display: flex;
    flex-direction: column;
    // align-items: center;
  }
  .ant-card-body {
    height: 100%;
    // padding: 10px 12px;
    padding: 8px !important;
    padding-top: 12px;
    background-color: #fff;
    border-radius: 0px;
    // border-top: 1px solid #DCDCDC;
  }

  .ant-card-meta-avatar {
    padding-right: 12px;
  }

  .evidence-card-item__cover {
    // width: 100%;
    // height: 100%;
    width: 204px;
    height: 115px;
    border-radius: 2px;
    object-fit: cover;
  }
  .evidence-card-item__cover_markers {
    width: 14px;
    height: 14px;
    object-fit: cover;
    // border-radius: 8px 8px 0 0;
    position: absolute;
    left: 12px;
    top: 12px;
    user-select: none;
    color: #436ff6;
  }
  .evidence-card-item__cover_hasDispute {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 60px;
    transform: rotate(45deg);
  }
  .evidence-card-item__cover--icon {
    height: 100%;
    display: flex;
    font-size: 52px;
    align-items: center;
    justify-content: center;
    background-color: #fff;
  }

  .evidence-card-item__comments {
    top: 24px;
    right: 24px;
    width: auto;
    position: absolute;

    .evidence-card-item__comments-icon {
      padding: 4px;
      font-size: 16px;
      border-radius: 3px;
      background-color: #fff;
      color: rgba(0, 0, 0, 0.4);
      border: 1px solid #dcdcdc;
    }
  }

  .evidence-card-item__icon {
    font-size: 20px;
    margin-top: 5px;
  }

  .ant-card-meta-title {
  }

  .evidence-card-item__classify {
    padding: 0 5px;
    margin-right: 8px;
    border-radius: 3px;
    white-space: nowrap;
    &--0 {
      color: #00a870;
      background-color: #e8f8f2;
    }

    &--1 {
      color: #436ff6;
      background-color: #ecf2fe;
    }
  }

  .evidence-card-item__uploader {
    flex: auto;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 185px;
    color: rgba(0, 0, 0, 0.6);
    /* Body/Small */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  .evidence-card-item__disproof {
    border-width: 0;
    color: rgba(0, 0, 0, 0.4);
  }

  .evidence-card-item__checkbox {
    display: none;
    position: absolute;
    right: 7px;
    bottom: 4px;
  }

  .evidence-card-item__type {
    left: 16px;
    bottom: 8px;
    width: auto;
    color: #fff;
    font-size: 12px;
    padding: 4px 8px;
    position: absolute;
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.6);
  }

  .ant-card-meta-description {
    & > .ant-row:not(:last-of-type) {
      margin-bottom: 8px;
    }
  }

  &:hover {
    .evidence-card-item__actions {
      display: flex;
    }

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
      content: " ";
      border-radius: 3px;
      background-color: rgba(0, 0, 0, 0.5);
    }
  }

  .evidence-card-item__actions {
    left: 0;
    bottom: 0;
    z-index: 2;
    width: 100%;
    padding: 16px;
    display: none;
    position: absolute;
    background-color: #fff;
    border-radius: 6px 6px 0 0;
  }

  .evidence-card-item__action-item {
    display: flex;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    color: rgba(0, 0, 0, 0.60);

    // &:nth-of-type(2n + 1) {
    //   border-right: 1px solid #d9d9d9;
    // }

    &:hover {
      color: var(--primary);
    }
  }

  .evidence-card-item__action-icon {
    font-size: 18px;
    margin-right: 6px;
  }

  &.evidence-card-item--batch {
    cursor: pointer;

    .evidence-card-item__actions,
    &:hover::before {
      display: none;
    }

    .evidence-card-item__checkbox {
      display: inline-flex;
    }
  }

  &.evidence-card-item--selected {
    border-color: var(--primary);
    box-shadow:
      0px 6px 30px 5px rgba(0, 0, 0, 0.05),
      0px 16px 24px 2px rgba(0, 0, 0, 0.04),
      0px 8px 10px -5px rgba(0, 0, 0, 0.08),
      0 0 0 1px var(--primary);
  }

  &.evidence-card-item--unselected {
    opacity: 0.6;
  }
}
// }
