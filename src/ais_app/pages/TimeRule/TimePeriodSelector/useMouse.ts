import React, { useRef, useEffect } from 'react';

const useMouse = ({ onSelect }: any) => {
    const mouseDownRef = useRef(false);

    const handleMouseDown = (e: any) => {
        mouseDownRef.current = true;
        let id = e.toElement.id
        if (id) {
            let indexS = e.toElement.id.split('-')
            onSelect('down', Number(indexS[1]), Number(indexS[2]))
        }
    };

    const handleMouseUp = (e: any) => {
        mouseDownRef.current = false;
        let id = e.toElement.id
        if (id) {
            let indexS = e.toElement.id.split('-')
            onSelect('up', Number(indexS[1]), Number(indexS[2]))
        }
    };

    useEffect(() => {
        document.addEventListener('mousedown', handleMouseDown);
        document.addEventListener('mouseup', handleMouseUp);

        return () => {
            document.removeEventListener('mousedown', handleMouseDown);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    }, []);

    return {
        mouseDownRef: mouseDownRef.current
    }
};

export default useMouse