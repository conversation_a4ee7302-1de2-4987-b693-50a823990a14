.TimePeriodSelector-box {
    width: 100%;

    .TimePeriodSelector-box-head {
        width: 100%;
        display: flex;
        align-items: center;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .TimePeriodSelector-box-head-item1 {
        width: 80px;
        height: 36px;
        border: 0px solid #E7E7E7;
        background-color: #fff;
        border-top-width: 1px;
        border-left-width: 1px;
        color: rgba(0, 0, 0, 0.40);
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .TimePeriodSelector-box-head-item {
        cursor: pointer;
        width: calc((100% - 80px) / 24);
        height: 36px;
        border: 0px solid #E7E7E7;
        background-color: #fff;
        border-top-width: 1px;
        border-left-width: 1px;
        color: rgba(0, 0, 0, 0.40);
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .TimePeriodSelector-box-body-row {
        display: flex;
    }
}