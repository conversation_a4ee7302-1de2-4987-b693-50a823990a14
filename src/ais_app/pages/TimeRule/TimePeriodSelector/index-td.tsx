import { Button } from 'antd';
import React, { useState } from 'react';

function Index(params: any) {

    // 删除数组中第一个匹配的元素，成功则返回位置索引，失败则返回 -1。
    // @ts-ignore
    Array.prototype.deleteElementByValue = function (varElement: any) {
        var numDeleteIndex = -1;
        for (var i = 0; i < this.length; i++) {
            // 严格比较，即类型与数值必须同时相等。
            if (this[i] === varElement) {
                this.splice(i, 1);
                numDeleteIndex = i;
                break;
            }
        }
        return numDeleteIndex;
    }

    // 表头
    const [header, setHeader] = useState([
        {
            name: "星期/时间",
        },
        {
            name: '0',
        },
        {
            name: '1',
        },
        {
            name: '2',
        },
        {
            name: '3',
        },
        {
            name: '4',
        },
        {
            name: '5',
        },
        {
            name: '周天',
        }
    ])

    // 数据
    const [data, setData]: any = useState({
        '星期/时间': ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24'],
        '周一': [],
        '周二': [],
        '周三': [],
        '周四': [],
        '周五': [],
        '周六': [],
        '周天': [],
    })
    // 
    const [isStart, setIsStart] = useState(0)
    // 类型
    const [type, setType]: any = useState('')
    // // 起始
    const [startItemIndex, setStartItemIndex] = useState(-1)
    const [startDataIndex, setStartDataIndex] = useState(-1)
    // 小
    const [minItemIndexs, setMinItemIndexs] = useState(-1)
    const [minDataIndexs, setMinDataIndexs] = useState(-1)
    // 大
    const [maxItemIndexs, setMaxItemIndexs] = useState(-1)
    const [maxDataIndexs, setMaxDataIndexs] = useState(-1)
    // 行下标
    const [rowIndexList, setRowIndexList]: any = useState([])
    // 列下标
    const [columnIndexList, setColumnIndexList]: any = useState([])
    return (
        <div style={{ width: '100%', color: 'black' }}>
            <div >
                <div style={{ display: 'flex' }}>
                    {/* <Button type="primary" onClick={() => {
                        if (type == 'slide') {
                            // 区域选择
                            let obj: any = {}
                            let headerList: any = []
                            let firstRow: any = []
                            for (let i = minDataIndexs; i <= maxDataIndexs; i++) {
                                obj[data['星期/时间'][i]] = []
                                if (firstRow.indexOf(data['星期/时间'][i]) == -1) {
                                    firstRow.push(data['星期/时间'][i])
                                }
                                for (let j = minItemIndexs; j <= maxItemIndexs; j++) {
                                    let dataObj: any = {}
                                    dataObj[header[j].name] = data[header[j].name][i]
                                    if (headerList.indexOf(header[j].name) == -1) {
                                        headerList.push(header[j].name)
                                    }
                                    obj[data['星期/时间'][i]].push(dataObj)
                                }
                            }
                            console.log(firstRow);
                            console.log(headerList);
                            console.log(obj);
                        } else if (type == 'row') {
                            // 几行选中
                            let obj: any = {}
                            let headerList: any = []
                            let firstRow: any = []
                            rowIndexList.map((item: any) => {
                                obj[data['星期/时间'][item]] = []
                                firstRow.push(data['星期/时间'][item])
                                header.map((headerItem, headerIndex) => {
                                    if (headerIndex != 0) {
                                        let dataObj: any = {}
                                        if (headerList.indexOf(headerItem.name) == -1) {
                                            headerList.push(headerItem.name)
                                        }
                                        dataObj[headerItem.name] = data[headerItem.name][item]
                                        obj[data['星期/时间'][item]].push(dataObj)
                                    }
                                })
                            })
                            console.log(firstRow);
                            console.log(headerList);
                            console.log(obj);
                        } else if (type == 'column') {
                            // 几列选中
                            let headerList: any = []
                            let firstRow: any = []
                            let obj: any = {}
                            data['星期/时间'].map((item: any, index: any) => {
                                obj[item] = []
                                firstRow.push(item)
                                columnIndexList.map((i: any) => {
                                    let dataObj: any = {}
                                    if (headerList.indexOf(header[i].name) == -1) {
                                        headerList.push(header[i].name)
                                    }
                                    dataObj[header[i].name] = data[header[i].name][index]
                                    obj[item].push(dataObj)
                                })
                            })
                            console.log(firstRow);
                            console.log(headerList);
                            console.log(obj);
                        }

                    }}>确定</Button> */}
                    {/* <Button type="primary" danger onClick={()=>{
                        setStartItemIndex(-1)
                        setRowIndexList([])
                        setColumnIndexList([])
                        setType('')
                    }}>重置</Button> */}
                </div>

                <div style={{ display: 'flex', textAlign: "center" }}>
                    {
                        header.map((item, index) => {
                            return <div style={{ minWidth: 100, border: "1px solid #ccc" }} onClick={() => {
                                setType('column')
                                setRowIndexList([])
                                if (columnIndexList.indexOf(index) != -1) {
                                    let obj: any = [...columnIndexList]
                                    obj.deleteElementByValue(index)
                                    setColumnIndexList(obj)
                                } else {
                                    let obj = [...columnIndexList]
                                    obj.push(index)
                                    setColumnIndexList(obj)
                                }
                            }}>{item.name}</div>
                        })
                    }
                </div>
                <div style={{ display: 'flex', textAlign: "center" }}>
                    {
                        header.map((item, itemIndex) => {
                            return <div>
                                {
                                    data[item.name].map((data: any, dataIndex: any) => {
                                        return <div onClick={() => {
                                            // 区间选取
                                            if (itemIndex != 0) {
                                                setType('slide')
                                                if (isStart == 0) {
                                                    setIsStart(1)
                                                    setStartItemIndex(itemIndex)
                                                    setStartDataIndex(dataIndex)
                                                    setMaxItemIndexs(itemIndex)
                                                    setMaxDataIndexs(dataIndex)
                                                    setMinItemIndexs(itemIndex)
                                                    setMinDataIndexs(dataIndex)
                                                } else {
                                                    setIsStart(0)
                                                }
                                            }
                                            // 行选取
                                            if (itemIndex == 0) {
                                                setType('row')
                                                setIsStart(1)
                                                setColumnIndexList([])
                                                if (rowIndexList.indexOf(dataIndex) != -1) {
                                                    let obj: any = [...rowIndexList]
                                                    obj.deleteElementByValue(dataIndex)
                                                    setRowIndexList(obj)
                                                } else {
                                                    let obj = [...rowIndexList]
                                                    obj.push(dataIndex)
                                                    setRowIndexList(obj)
                                                }
                                            }
                                        }} onMouseOver={() => {
                                            if (isStart) {
                                                if (itemIndex != 0) {
                                                    if (itemIndex > startItemIndex) {
                                                        setMinItemIndexs(startItemIndex)
                                                        setMaxItemIndexs(itemIndex)
                                                    } else {
                                                        setMaxItemIndexs(startItemIndex)
                                                        setMinItemIndexs(itemIndex)
                                                    }
                                                }
                                                if (dataIndex > startDataIndex) {
                                                    setMinDataIndexs(startDataIndex)
                                                    setMaxDataIndexs(dataIndex)
                                                }
                                                else {
                                                    setMaxDataIndexs(startDataIndex)
                                                    setMinDataIndexs(dataIndex)
                                                }
                                            }

                                        }} style={{
                                            minWidth: 100, border: "1px solid #ccc",
                                            backgroundColor: type == 'slide' ?
                                                (itemIndex >= minItemIndexs && itemIndex <= maxItemIndexs) && (dataIndex >= minDataIndexs && dataIndex <= maxDataIndexs) ? 'pink' : '' :
                                                type == 'row' ? rowIndexList.indexOf(dataIndex) != -1 ? 'pink' : '' :
                                                    type == 'column' ? columnIndexList.indexOf(itemIndex) != -1 ? 'pink' : '' : ''
                                        }}>{data}</div>
                                    })
                                }
                            </div>
                        })
                    }
                </div>
            </div>
        </div>
    )
}

export default Index
