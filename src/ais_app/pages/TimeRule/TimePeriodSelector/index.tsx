import { useState, useRef, useEffect } from "react";
import useMouse from './useMouse'
import "./index.less";
import { message } from "antd";

const weekCN = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

export default function TimePeriodSelector(props: any) {
    const { timeData, allData, scheduler, setScheduler, setAllData, disableData, disabled } = props
    const [header, setHeader] = useState([])
    const [data, setData]: any = useState([])

    // // 起始
    const [startIndex, setStartIndex]: any = useState([null, null])
    const [endIndex, setEndIndex]: any = useState([null, null])

    useEffect(() => {
        let headerC: any = []
        for (let i = 0; i < 25; i++) {
            if (i === 0) {
                headerC.push('星期/时间')
            } else {
                headerC.push(i - 1)
            }
        }

        setHeader(headerC)
    }, [])

    useEffect(() => {
        let dataC: any = []
        for (let i = 0; i < 7; i++) {
            let c: any = {}
            c.week = i + 1
            c.hour = []
            for (let f = 0; f < 24; f++) {
                let d: any = {}
                d.select = false
                d.value = f
                d.disable = false
                c.hour.push(d)
            }
            dataC.push(c)
        }
        if (timeData && timeData.length > 0) {
            for (let i = 0; i < timeData.length; i++) {
                for (let f = 0; f < timeData[i].hour.length; f++) {
                    if (dataC[i].hour[timeData[i].hour[f]]) {
                        dataC[i].hour[timeData[i].hour[f]].select = true
                    }
                }
            }
        }
        if (disableData && disableData.length > 0) {
            for (let i = 0; i < dataC.length; i++) {

                for (let f = 0; f < dataC[i].hour.length; f++) {
                    if (disableData[i].hour.includes(dataC[i].hour[f].value)) {

                    } else {
                        dataC[i].hour[f].disable = true
                    }
                }
            }
        }
        setData(dataC)
    }, [timeData])

    useEffect(() => {
        if (allData) {
            for (let i = 0; i < data.length; i++) {
                for (let f = 0; f < data[i].hour.length; f++) {
                    if (!data[i].hour[f].disable) {
                        data[i].hour[f].select = true
                    }
                }
            }

            setScheduler([...data])
            setData([...data])
        }
    }, [allData])

    useEffect(() => {
        let a = 0
        let dis = 0
        for (let i = 0; i < data.length; i++) {
            for (let f = 0; f < data[i].hour.length; f++) {
                if (data[i].hour[f].select) {
                    a += 1
                }
                if (data[i].hour[f].disable) {
                    dis += 1
                }
            }
        }
        let c = a + dis
        if (c === 168) {
            setAllData(true)
        } else {
            setAllData(false)
        }
    }, [data])
    useEffect(() => {
        if (disabled) {
            return
        }
        if (startIndex.some((item: any) => isNaN(item)) && endIndex.some((item: any) => isNaN(item))) {
            setStartIndex([null, null])
            setEndIndex([null, null])
            return
        }
        if (startIndex[0] !== null && startIndex[1] !== null && endIndex[0] !== null && endIndex[1] !== null) {
            if (startIndex[0] === endIndex[0] && startIndex[1] === endIndex[1]) {
                // 单点
                if (!data[startIndex[0]].hour[startIndex[1]].disable) {
                    if (data[startIndex[0]].hour[startIndex[1]].select) {
                        data[startIndex[0]].hour[startIndex[1]].select = false
                    } else {
                        data[startIndex[0]].hour[startIndex[1]].select = true
                    }
                }
            } else {
                // 区域选择
                let xStart = 0 // x轴起点
                let xEnd = 0
                let yStart = 0
                let yEnd = 0
                // 左上方开始绘点
                if (startIndex[0] <= endIndex[0] && startIndex[1] <= endIndex[1]) {
                    console.log('左上方开始绘点')
                    xStart = startIndex[1]
                    yStart = startIndex[0]
                    xEnd = endIndex[1]
                    yEnd = endIndex[0]
                }
                // 右下方开始绘点
                if (startIndex[0] >= endIndex[0] && startIndex[1] >= endIndex[1]) {
                    console.log('右下方开始绘点')
                    xStart = endIndex[1]
                    yStart = endIndex[0]
                    xEnd = startIndex[1]
                    yEnd = startIndex[0]
                }
                // 右上方开始绘点
                if (startIndex[0] <= endIndex[0] && startIndex[1] >= endIndex[1]) {
                    console.log('右上方开始绘点')
                    xStart = endIndex[1]
                    yStart = startIndex[0]
                    xEnd = startIndex[1]
                    yEnd = endIndex[0]
                }

                // 左下方开始绘点
                if (startIndex[0] >= endIndex[0] && startIndex[1] <= endIndex[1]) {
                    console.log('左下方开始绘点')
                    xStart = startIndex[1]
                    yStart = endIndex[0]
                    xEnd = endIndex[1]
                    yEnd = startIndex[0]
                }

                for (let i = yStart; i <= yEnd; i++) {
                    for (let f = xStart; f <= xEnd; f++) {
                        if (!data[i].hour[f].disable) {
                            if (data[i].hour[f].select) {
                                data[i].hour[f].select = false
                            } else {
                                data[i].hour[f].select = true
                            }
                        }
                    }
                }
            }
            setScheduler([...data])
            setData([...data])
            setStartIndex([null, null])
            setEndIndex([null, null])
        }
    }, [startIndex, endIndex])

    const onSelect = (type: any, index: any, index1: any) => {
        if (type === 'down') {
            setStartIndex([index, index1])
        }
        if (type === 'up') {
            setEndIndex([index, index1])
        }
    }

    const { mouseDownRef } = useMouse({ onSelect })

    return (
        <div className="TimePeriodSelector-box">
            <div className="TimePeriodSelector-box-head">
                {
                    header.map((item: any, index: any) => (
                        <div
                            key={index}
                            className={index === 0 ? "TimePeriodSelector-box-head-item1" : "TimePeriodSelector-box-head-item"}
                            style={{ borderRightWidth: header.length === index + 1 ? 1 : 0 }}
                        >
                            {item}
                        </div>
                    ))
                }
            </div>
            <div className="TimePeriodSelector-box-body">
                {
                    data.map((item: any, index: any) => (
                        <div key={index} className="TimePeriodSelector-box-body-row">
                            <div
                                className={"TimePeriodSelector-box-head-item1"}
                                style={{ borderBottomWidth: data.length === index + 1 ? 1 : 0 }}
                            >
                                {weekCN[item.week - 1]}
                            </div>

                            {
                                item.hour.map((item1: any, index1: any) => (
                                    <div
                                        id={`headItem-${index}-${index1}`}
                                        key={index1}
                                        className={"TimePeriodSelector-box-head-item"}
                                        style={{
                                            borderBottomWidth: data.length === index + 1 ? 1 : 0,
                                            borderRightWidth: item.hour.length === index1 + 1 ? 1 : 0,
                                            backgroundColor: item1.select ? '#165DFF' : item1.disable ? '#C5C5C5' : '#fff'
                                        }}
                                    >

                                    </div>
                                ))

                            }
                        </div>
                    ))
                }
            </div>
        </div>
    );
}
