import { useRef } from "react";
import { Table, Tree, Pagination, Drawer, Input, Form, Button } from 'antd';
import { useSize } from "ahooks";
import dayjs from "dayjs";
import TimeRuleDrawer from './TimeRuleDrawer'
import Authority from "@src/components/Authority";
import './index.less';

import useData from './useData';

export default function TimeRule() {
    const ref = useRef<HTMLDivElement>(null);
    const size = useSize(ref);

    const {
        data,
        params,
        totalCount,
        setParams,
        timeRuleDrawerOpen,
        setTimeRuleDrawerOpen,
        dataInfo,
        setDataInfo,
        onSubTimeRule,
        inputRef,
    } = useData()

    const columns: any = [
        {
            title: '序号',
            dataIndex: 'dataIndex',
            fixed: 'left',
            width: 81,
            render: (text: string, i: any, t: any) => {
                let sizeC = params.current - 1
                return <div style={{ color: 'rgba(0, 0, 0, 0.90)', fontSize: 14 }}>{sizeC * params.limit + t + 1 < params.limit ? `${sizeC * params.limit + t + 1}` : sizeC * params.limit + t + 1}</div>
            },
        },
        {
            title: '规则名称',
            dataIndex: 'ruleName',
            render: (text: string, i: any, t: any) => {
                return <div >{text}</div>
            },
        },
        {
            title: '更新时间',
            dataIndex: 'updatedTime',
            render: (text: string, i: any, t: any) => {
                return <div>{text ? dayjs(Number(text)).format("YYYY/MM/DD HH:mm:ss") : dayjs(Number(i.createdTime)).format("YYYY/MM/DD HH:mm:ss")}</div>
            },
        },
        {
            title: '操作',
            key: 'operation',
            fixed: 'right',
            width: 98,
            render: (res: any, i: any, t: any) => {
                return <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Authority code="30000452">
                        <a
                            style={{ fontSize: 14, color: '#165DFF' }}
                            onClick={() => {
                                setDataInfo(i)
                                setTimeRuleDrawerOpen(true)
                            }}
                        >
                            编辑
                        </a>
                    </Authority>
                </div >
            },
        },
    ];

    return (
        <div className="time-rule-page" ref={ref}>
            <div className='time-rule-page-center'>
                <div className='time-rule-page-title'>
                    <div className='time-rule-page-title-title'>
                        时间规则
                    </div>
                    <Input
                        allowClear
                        ref={inputRef}
                        value={params.keywords}
                        onChange={(e) => setParams({ ...params, keywords: e.target.value })}
                        style={{ width: 300 }}
                        placeholder={`请输入规则名称搜索`}
                    />
                    <Authority code="30000451">
                        <div className='time-rule-page-title-button' onClick={() => setTimeRuleDrawerOpen(true)}>新增规则</div>
                    </Authority>
                </div>
                <Table
                    rowKey="id"
                    // @ts-ignore
                    columns={columns}
                    dataSource={data}
                    scroll={{ x: 'max-content', y: size?.height && size.height - 242 }}
                    pagination={false}
                />

                {
                    data.length > 0 &&
                    <div style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end' }}>
                        <Pagination
                            current={params.current}
                            total={totalCount}
                            pageSize={params.limit}
                            showSizeChanger
                            showTotal={() => `共 ${totalCount} 项数据`}
                            showQuickJumper={true}
                            onChange={(page, size) => {
                                if (size === params.limit) {
                                    setParams({
                                        ...params,
                                        current: page,
                                        limit: size,
                                        offset: (page - 1) * size,
                                    });
                                } else {
                                    setParams({
                                        ...params,
                                        current: 1,
                                        offset: 0,
                                        limit: size,
                                    });
                                }
                            }}
                        />
                    </div>
                }
            </div>

            {
                timeRuleDrawerOpen &&
                <TimeRuleDrawer
                    open={timeRuleDrawerOpen}
                    setOpen={setTimeRuleDrawerOpen}
                    dataInfo={dataInfo}
                    setDataInfo={setDataInfo}
                    dataList={data}
                    onSub={onSubTimeRule}
                />
            }
        </div>
    );
}
