import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { treeHelper } from '@cloud-app-dev/vidc';
import Service from "@src/service/system";

const useData = () => {
    const inputRef: any = useRef(null)

    const [timeRuleDrawerOpen, setTimeRuleDrawerOpen] = useState(false)
    const [dataInfo, setDataInfo]: any = useState({})
    const [data, setData] = useState([])
    const [totalCount, setTotalCount] = useState(0)
    const [params, setParams]: any = useState({
        isPagination: true,
        current: 1,
        offset: 0,
        limit: 10,
        keywords: '',
    })

    useEffect(() => {
        getList()
    }, [params])

    const getList = () => {
        Service.device.timeRuleList({ ...params }).then((res) => {
            setData(res.data.list)
            setTotalCount(res.data.totalCount)
        })
    }

    const onSubTimeRule = (val: any) => {
        if (dataInfo.id) {
            Service.device.timeUpdateRule({ ...dataInfo, ...val }).then(() => {
                message.success('编辑时间规则成功', 1.5)
                setDataInfo({})
                setTimeRuleDrawerOpen(false)
                getList()
            }).catch((err) => {
                message.warning(err.data.message)
            })
        } else {
            Service.device.timeSaveRule({ parentId: params.orgId, ...val }).then(() => {
                message.success('新增时间规则成功', 1.5)
                setDataInfo({})
                setTimeRuleDrawerOpen(false)
                getList()
            }).catch((err) => {
                message.warning(err.data.message)
            })
        }
    }

    return {
        data,
        params,
        totalCount,
        setParams,
        timeRuleDrawerOpen,
        setTimeRuleDrawerOpen,
        dataInfo,
        setDataInfo,
        onSubTimeRule,
        inputRef,
    };
};

export default useData;
