import { useRef, useEffect, useState } from "react";
import { Drawer, Input, Form, Button, Switch, message } from "antd";
import dayjs from 'dayjs'
import TimePeriodSelector from '../TimePeriodSelector'

import "./index.less";

const timeDataList = [
    {
        "week": 1,
        "hour": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
    },
    {
        "week": 2,
        "hour": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
    },
    {
        "week": 3,
        "hour": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
    },
    {
        "week": 4,
        "hour": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
    },
    {
        "week": 5,
        "hour": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
    },
    {
        "week": 6,
        "hour": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
    },
    {
        "week": 7,
        "hour": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
    }
]

export default function TimeRuleDrawer({ open, setOpen, onSub, dataInfo, setDataInfo }: any) {
    const [form] = Form.useForm()
    const subBottomRef: any = useRef(null)
    const [allData, setAllData]: any = useState(false)
    const [scheduler, setScheduler]: any = useState([])
    const [timeData, setTimeData]: any = useState([])

    useEffect(() => {
        if (dataInfo.id) {
            form.setFieldsValue({ ...dataInfo })
        }
    }, [dataInfo])

    useEffect(() => {
        setTimeData(dataInfo.ruleConfig ? dataInfo.ruleConfig : timeDataList)
        setScheduler(dataInfo.ruleConfig ? dataInfo.ruleConfig : timeDataList)
    }, [dataInfo.scheduler])

    const onCancel = () => {
        setDataInfo({})
        setOpen(false)
    }

    const handleSubmit = (value: any) => {
        let schedulerC: any = []
        for (let i = 0; i < scheduler.length; i++) {
            let schedulerItem: any = { week: i + 1, hour: [] }
            let hourC = []
            for (let f = 0; f < scheduler[i].hour.length; f++) {
                if (typeof scheduler[i].hour[f] === 'object' && scheduler[i].hour[f] !== null) {
                    if (scheduler[i].hour[f].select) {
                        hourC.push(scheduler[i].hour[f].value)
                    }
                } else {
                    hourC.push(scheduler[i].hour[f])
                }
            }
            schedulerItem.hour = hourC
            schedulerC.push(schedulerItem)
        }

        if (schedulerC[0].hour.length === 0 && schedulerC[1].hour.length === 0 && schedulerC[2].hour.length === 0 && schedulerC[3].hour.length === 0 && schedulerC[4].hour.length === 0 && schedulerC[5].hour.length === 0 && schedulerC[6].hour.length === 0) {
            message.warning('请选择任务执行时间段')
            return
        }
        onSub({ ...value, ruleConfig: schedulerC })
    }

    return (
        <>
            <Drawer
                width={961}
                title={dataInfo.id ? "编辑时间规则" : "新增时间规则"}
                placement={'right'}
                closable={false}
                onClose={onCancel}
                open={open}
                keyboard={false}
                footer={<div className="editGantryCraneDrawer-footer">
                    <div onClick={() => onCancel()} className="editGantryCraneDrawer-clear">取消</div>
                    <div className="editGantryCraneDrawer-ok" onClick={() => subBottomRef.current.click()}>保存</div>
                </div>}
            >

                <Form
                    form={form}
                    name='loginForm'
                    onFinish={handleSubmit}
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                    className='editGantryCraneDrawer-layout-form'
                >
                    <Form.Item
                        className='editGantryCraneDrawer-form-item'
                        label="时间规则名称"
                        name="ruleName"
                        rules={[
                            { required: true, message: '请输入时间规则名称' },
                        ]}
                    >
                        <Input maxLength={20} placeholder="请输入时间规则名称，限制20字" />
                    </Form.Item>

                    <div className="EditDeviceDrawer-row">
                        <div className="EditDeviceDrawer-row-left" style={{ paddingRight: 0 }}>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <div className="EditDeviceDrawer-row-title">
                                    任务执行时间段
                                </div >
                                <Switch checked={allData} onChange={() => setAllData(!allData)} />
                                <div style={{ marginLeft: 4, color: 'rgba(0, 0, 0, 0.60)', fontSize: 12 }}>全部时间段</div>

                                <div className="EditDeviceDrawer-row-title-tip">
                                    <div className="title-tip">任务执行时段</div>
                                    <div className="title-tip1">任务暂停时段</div>
                                </div>
                            </div>

                            <div className="EditDeviceDrawer-row-tip">
                                <TimePeriodSelector
                                    allData={allData}
                                    setAllData={setAllData}
                                    timeData={timeData}
                                    scheduler={scheduler}
                                    setScheduler={setScheduler}
                                />
                            </div>
                        </div>
                    </div>
                    <Form.Item style={{ display: 'none' }}>
                        <Button ref={subBottomRef} htmlType='submit'>
                            提交
                        </Button>
                    </Form.Item>
                </Form>

            </Drawer>
        </>
    );
}
