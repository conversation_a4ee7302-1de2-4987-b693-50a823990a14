import { SocketEmitter } from '@cloud-app-dev/vidc';
import VideoLayout from '@src/components/VideoLayout'
import { useMount, useSafeState } from 'ahooks';
import AlarmLeft from './components/AlarmLeft';
import AlarmRight from './components/AlarmRight';
import './index.less';
export default function EventAlarm() {
  const [params, setParams] = useSafeState({});
  useMount(()=>{
    SocketEmitter.emit('clearMessage')
  })
  return (
    <div className="EventAlarm">
      <VideoLayout leftControl={<AlarmLeft setParams={setParams} paramsData={params} />} RightContent={<AlarmRight params={params} />} />
    </div>
  );
}
