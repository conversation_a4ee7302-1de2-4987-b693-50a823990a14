import { RecordItem } from "@cloud-app-dev/vidc/es/ScreenPlayer/interface";
import { useMemoizedFn } from "ahooks";
import { DataNode } from "antd/lib/tree";
import React, { useMemo, useState } from "react";

export type ListItem = {
  cid: string;
  accessType: string;
  lat: string;
  lng: string;
  localRecord: number;
  name: string;
  ptz: number;
  state: number;
  storageVideo: number;
  type: number;
};

export interface VideoItem extends RecordItem {
  name: string;
  ptz?: boolean;
}

export interface IParams {
  currentTreeItem?: ITreeItem;
  currentDeviceItem?: any;
  deviceCount: {
    totalCount: number;
    onlineCount: number;
  };
}

export interface IVideoContextProps {
  params: IParams;
  list: (VideoItem | undefined)[];
  selectItem: VideoItem;
  set: (item?: VideoItem, index?: number) => void;
  clear: () => void;
  get: (cid: string) => any;
  updateParams: (data: Partial<IParams>) => void;
}

export interface ITreeItem extends DataNode {
  id: string;
  groupName: string;
  onlineCount: number;
  totalCount: number;
  parentId: string;
  children?: ITreeItem[];
  name?: JSX.Element;
}

export const VideoContext = React.createContext<IVideoContextProps>({} as IVideoContextProps);

export function Provider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState({ params: {} as IParams });

  const updateParams = useMemoizedFn((data: Partial<IParams>) => {
    setState((old) => ({ ...old, params: { ...old.params, ...data } }));
  });
  const value = useMemo(() => ({ updateParams, params: state.params }) as IVideoContextProps, [state.params, updateParams]);

  return <VideoContext.Provider value={value}>{children}</VideoContext.Provider>;
}
