import TestTabsLayout from '@src/ais_app/components/TestTabsLayout';
import VideoLayout from '@src/components/VideoLayout'
import LeftLayout from './components/LeftLayout';
import RightContent from './components/RightContent';
import LoopTest from './components/LoopTest';
import DeviceList from './components/DeviceList';
import DeviceDir from './components/DeviceDir';
import './index.less';
import { useSafeState, useToggle, useUpdateEffect } from 'ahooks';
import { useEffect } from 'react';
import { useHistory } from '@cloud-app-dev/vidc';
export default function TestConfigContent() {
  const [currentKey, setCurrentKey] = useSafeState<number>(0);
  const [isEdit, { toggle: EditToggle, setLeft, setRight }] = useToggle(false);
  const [isEmpty, { setLeft: EmptyLeft, setRight: EmptyRight }] = useToggle(false);
  const history = useHistory();
  useEffect(() => {
    const state: any = history.location.state;
    if (state?.edit === 2) {
      setCurrentKey(1);
      setRight();
      EmptyRight();
    }else if(state?.edit === 1){
      setCurrentKey(1);
      setLeft();
    }
  }, [history.location]);
  useUpdateEffect(() => {
    if (currentKey === 0) {
      setLeft();
    }
  }, [currentKey]);
  return (
    <div className="TestConfigContent">
      <TestTabsLayout isEdit={isEdit} isEmpty={isEmpty} EditToggle={EditToggle} tabsName={['设备任务', '轮巡任务']} setCurrentKey={setCurrentKey} currentKey={currentKey}>
        {currentKey === 0 ? (
          <VideoLayout leftControl={<LeftLayout topContent={<DeviceDir />} midContent={<DeviceList />} />} RightContent={<RightContent />} />
        ) : (
          <LoopTest isEmptyflag={isEmpty} EditToggle={EditToggle} isEdit={isEdit} EmptyLeft={EmptyLeft} EmptyRight={EmptyRight} />
        )}
      </TestTabsLayout>
    </div>
  );
}
