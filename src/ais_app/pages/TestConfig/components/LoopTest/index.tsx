import { useRequest, useToggle } from 'ahooks';
import LoopEdit from '../LoopEdit';
import LoopView from '../LoopView';
import { SocketEmitter, cache } from '@cloud-app-dev/vidc';
import { isEmpty } from 'lodash-es';
import { queryPollingTask } from '@src/service/ais_app';
import LoopTestEmpty from '../../../../components/LoopTestEmpty';
import { useEffect } from 'react';

import './index.less';
interface looptesttype {
  isEmptyflag: boolean;
  isEdit: boolean;
  EmptyRight: () => void;
  EmptyLeft: () => void;
  EditToggle: () => void;
}
export default function LoopTest({ isEmptyflag, EditToggle, isEdit, EmptyLeft, EmptyRight }: looptesttype) {
  const [status, { toggle }] = useToggle(false);
  const { data } = useRequest(queryPollingTask, { refreshDeps: [isEdit, status] });
  const change = (v: boolean) => {
    toggle();
  };
  useEffect(() => {
    SocketEmitter.on('statusChange', change);
    return ()=>SocketEmitter.on('statusChange', change);
  },[]);
  useEffect(() => {
    if (isEmpty(data)) {
      EmptyRight();
      setRight();
    } else {
      EmptyLeft();
      setLeft();
      SocketEmitter.emit('changeTask', data);
    }
  }, [data]);
  const [isAdd, { setLeft, setRight }] = useToggle(false);
  return (
    <div className="LoopTest">
      {isEdit ? (
        <LoopEdit EditToggle={EditToggle} isAdd={isAdd} editData={data} />
      ) : !isEmptyflag ? (
        <LoopView data={data} />
      ) : (
        <LoopTestEmpty onOK={EditToggle} canEdit={cache.getCache('userInfo', 'session')?.userType == 1} />
      )}
    </div>
  );
}
