import { useRequest } from 'ahooks';
import {  treeHelper } from '@cloud-app-dev/vidc';
import { IconFont, useSimpleState } from '@cloud-app-dev/vidc';
import { Tree } from 'antd';
import GradientTitle from '@src/components/GradientTitle';
import { useContext, useEffect, useMemo, useRef } from 'react';
import { cloneDeep, size, uniq } from 'lodash-es';
import { ITreeItem, VideoContext } from '../../Context';
import TreeHeader from '../TreeHeader';
import { DataNode } from 'antd/lib/tree';
import { getGroups } from '../utils';
import './index.less';

function DeviceDir() {
  const { updateParams, params } = useContext(VideoContext);
  const [state, stateChange] = useSimpleState({
    keyword: '',
    treeData: [],
    expandedAll: false,
    expandedKeys: [] as string[],
    forceKey: Date.now(),
  });
  const ref = useRef<HTMLDivElement>(null);
  // 基于group的树数据
  const { data } = useRequest<ITreeItem[], any>(() => getGroups(), { refreshDeps: [state.forceKey] });
  const treeRes = useMemo(() => data ?? [], [data]);
  const treeMap = useMemo(() => getTreeMap(treeRes), [treeRes]);

  // 树搜索处理,匹配结果展开
  const onFilterChange = (name: string) => {
    const codes = getTreeIdWithKeyword(treeMap, name);
    stateChange({ keyword: name, expandedKeys: codes });
  };

  const setExpandAll = () => {
    let expandedKeys: any = [];
    if (!state.expandedAll) {
      expandedKeys = treeRes.map((v) => v.id);
    }
    stateChange({ expandedKeys, expandedAll: !state.expandedAll } as any);
  };

  // 树选中处理
  const onSelect = (item: ITreeItem) => {
    if (item.id === params.currentTreeItem?.id) {
      updateParams({ currentTreeItem: undefined });
    } else {
      const { children, ...data } = item;
      updateParams({ currentTreeItem: cloneDeep(data) });
    }
  };

  const treeDataTemp = useMemo(() => {
    return treeHelper.computTreeList(treeRes);
  }, [treeRes]);
  // 树数据处理，高亮关键字
  const treeData = useMemo(() => {
    const loop = (data: DataNode[]): DataNode[] =>
      data.map((item: any) => {
        const strName = item.groupName as string;
        const index = strName.indexOf(state.keyword);
        const beforeStr = strName.substring(0, index);
        const afterStr = strName.slice(index + state.keyword.length);
        const name =
          index > -1 ? (
            <span>
              {beforeStr}
              <span className="site-tree-search-value">{state.keyword}</span>
              {afterStr}
            </span>
          ) : (
            <span>{strName}</span>
          );
        if (item.children) {
          return { ...item, name, code: item.code, children: loop(item.children) };
        }

        return { ...item, name, code: item.code };
      });
    return loop(treeDataTemp as any);
  }, [state.keyword, treeDataTemp]);
  useEffect(() => {
    const { totalCount, onlineCount } = treeDataTemp.reduce(
      (p, n) => {
        p.onlineCount += n.onlineCount || 0;
        p.totalCount += n.totalCount || 0;
        return p;
      },
      { totalCount: 0, onlineCount: 0 }
    );
    updateParams({ deviceCount: { totalCount, onlineCount } });
  }, [treeDataTemp]);
  return (
    <div className="DeviceDir">
      <div style={{ padding: '16px 16px 0px' }}>
        <GradientTitle title="设备选择" />
      </div>
      <TreeHeader
        onKeywordChange={onFilterChange}
        setExpandAll={setExpandAll}
        total={params?.deviceCount?.totalCount ?? 0}
        onlineTotal={params?.deviceCount?.onlineCount ?? 0}
        expandAll={state.expandedAll}
      />
      <div className="dirContent">
        <div ref={ref as any}>
          <Tree
            switcherIcon={<IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" style={{ transform: 'rotate(90deg)' }} />}
            treeData={treeData}
            fieldNames={{ title: 'groupName', key: 'id' }}
            onExpand={(expandedKeys) => stateChange({ expandedKeys } as any)}
            expandedKeys={state.expandedKeys}
            titleRender={(nodeData) => <DirItem nodeData={nodeData as any} onSelect={onSelect} />}
          />
        </div>
      </div>
    </div>
  );
}

interface IDirItemProps {
  nodeData: ITreeItem;
  onSelect: (item: any) => void;
}

const DirItem = ({ nodeData, onSelect }: IDirItemProps) => {
  return (
    <div className="DirItem">
      <div className="left" onClick={() => onSelect(nodeData)}>
        {size(nodeData.children) > 0 ? <IconFont type="icon-shipinchakan_morenfenzu" /> : <IconFont type="icon-shipinchakan_fenzu" />}
        <div className="title">{nodeData?.name}</div>
        <div className="count">
          <span>{nodeData?.onlineCount ?? 0}</span>/{nodeData?.totalCount ?? 0}
        </div>
      </div>
    </div>
  );
};

export function getTreeCode(treeData: any[], arr = [] as string[]) {
  treeData.forEach((item: any) => {
    arr.push(item.code);
    if (Array.isArray(item.children) && item.children.length > 0) {
      getTreeCode(item.children, arr);
    }
  });
  return arr;
}

export function getTreeIdWithKeyword(treeMap: { [key: string]: ITreeItem }, keyword: string) {
  const arr = [] as string[];
  if (!keyword) {
    return arr;
  }
  const getParentCode = (code: string, arr2 = [] as string[]) => {
    if (treeMap[code]) {
      const item2 = treeMap[code];
      arr2.push(code);
      if (item2.parentId && treeMap[item2.parentId]) {
        getParentCode(item2.parentId);
      }
    }
    return arr2;
  };
  const data = Object.values(treeMap);
  data.forEach((item: ITreeItem) => {
    if (item.groupName.indexOf(keyword)) {
      arr.push(...getParentCode(item.id));
    }
  });

  return uniq(arr);
}

export function getTreeMap(treeData: ITreeItem[]) {
  const map = {} as any;
  treeData.forEach((item: ITreeItem) => {
    map[item.id] = item;
  });
  return map;
}

export default DeviceDir;
