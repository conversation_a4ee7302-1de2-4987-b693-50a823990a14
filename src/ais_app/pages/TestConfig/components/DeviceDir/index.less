.DeviceDir {
  height: 100%;
  display: flex;
  flex-direction: column;
  .dirContent {
    flex: 1;
    contain: strict;
    overflow: auto;
    padding: 0 16px 10px;
    color: #fff;
    .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected{
      background-color: rgba(255,255,255,0.2);
    }
    .ant-tree-switcher{
      line-height: 32px;
    }
    .DirItem {
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        flex: 1;
        display: flex;
        align-items: center;
        .title {
          margin-left: 6px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .count {
          margin-left: 6px;
          span {
            color: var(--secondary3);
          }
        }
      }
      .rigth {
        width: 20px;
        font-size: var(--fs-large);
        display: none;
      }
    }
    .DirItem:hover{
      .rigth {
        display: block;
      }
    }
    .ant-tree-treenode {
      width: 100%;
      padding: 0;
    }
    .ant-tree .ant-tree-node-content-wrapper {
      flex: 1;
      min-width: 190px;
    }
    .ant-tree-treenode:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    .ant-tree .ant-tree-node-content-wrapper:hover {
      background-color: transparent;
    }
    .site-tree-search-value {
      color: var(--danger);
    }
  }
}
