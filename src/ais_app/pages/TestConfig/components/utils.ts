import { Service ,cache} from '@cloud-app-dev/vidc';
export function getHeader() {
  return {
    Authorization: cache.getCache('token', 'session'),
  };
}
export function getGroups() {
  return Service.http({
    headers: getHeader(),
    url: '/api/dvia-app-scene-server/device/deviceBuzGroup/deviceBuzGroupList/query',
  }).then((res) => res.data ?? []);
}

export function getDevices(data: { groupId?: string; limit: number; offset: number; keywords?: string; type?: any; state?: any }) {
  return Service.http({
    headers: getHeader(),
    url: '/api/dvia-app-scene-server/device/systemDevice/deviceList/query',
    data,
    method: 'post',
  }).then((res) => res.data ?? []);
}
export const colorArr = [
  {
    color: '红色',
    value: 0,
  },
  {
    color: '橙色',
    value: 1,
  },
  {
    color: '黄色',
    value: 2,
  },
  {
    color: '绿色',
    value: 3,
  },
  {
    color: '青色',
    value: 4,
  },
  {
    color: '蓝色',
    value: 5,
  },
  {
    color: '紫色',
    value: 6,
  },
  {
    color: '黑色',
    value: 7,
  },
  {
    color: '灰色',
    value: 8,
  },
  {
    color: '白色',
    value: 9,
  },
];
