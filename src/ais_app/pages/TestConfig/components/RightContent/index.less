.right-content {
  width: 100%;
  height: 100%;
  position: relative;
  .btarea {
    width: 100%;
    height: 64px;
    position: absolute;
    bottom: 0;
    border-top: 1px solid var(--gray7);
    display: flex;
    align-items: center;
    justify-content: center;
    .btnok {
      width: 84px;
      height: 32px;
      // background: var(--primary-light);
      border-radius: var(--radius1);
      text-align: center;
      // color: var(--gray1);
    }
  }
  .RightContent {
    width: 100%;
    height: calc(100% - 64px);
    padding: 16px 0px 0px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .choseAlgorithm {
      padding: 0 24px;
      .choseAlgorithmContent {
        margin-top: 16px;
        .filterScene {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          .leftfilter {
            display: flex;
            align-items: center;
            .lable {
              font-weight: 500;
              color: var(--gray2);
            }
          }
          .rightfilter {
            display: flex;
            align-items: center;
            .viewconfiged {
              color: var(--primary-light);
              cursor: pointer;
            }
            .inputbox {
              margin-left: 8px;
              .ant-input-affix-wrapper {
                padding-left: 12px;
                width: 260px;
                border: none;
              }
              .ant-input {
                background-color: transparent;
                color: var(--gray1);
              }
            }
          }
        }
        .algorithms {
        }
      }
    }
  }
}
