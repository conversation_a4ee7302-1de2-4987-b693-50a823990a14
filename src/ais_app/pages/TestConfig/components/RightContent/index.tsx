import { useEffect, useMemo, useRef, useContext, useCallback } from 'react';
import { useSafeState, useSize, useUpdateEffect, useToggle, useMemoizedFn } from 'ahooks';
import { Row, Col, message, Button, Empty ,Input} from 'antd';
import { IconFont, SocketEmitter } from '@cloud-app-dev/vidc';
import { useForm } from 'antd/es/form/Form';
import { isEmpty, without,cloneDeep } from 'lodash-es';
import { noCachequeryAlgorithmList, saveAlarmTask, alarmTaskInfo, deleteAlarmTask, updateAlarmTask } from '@src/service/ais_app';
import GradientTitle from '@src/components/GradientTitle';
import OperatingBounced from '@src/components/OperatingBounced';
import Scenes from '@src/ais_app/components/Scenes';
import ChildTaskCarousel from '@src/ais_app/components/ChildTaskCarousel';
import ChildItemNode from '@src/ais_app/components/ChildTaskCarousel/ChildItemNode';
import { getConfig } from '../../AlgorithmConfig';
import AlgorithmConfig from '../AlgorithmConfig';
import { VideoContext } from '../../Context';
import warnImg from '@src/assets/image/warn.png';
import './index.less';

export default function RightContent() {
  const { params: contextParams, updateParams } = useContext(VideoContext);
  const ref = useRef<HTMLDivElement>(null);
  const size = useSize(ref);
  const [form] = useForm();
  const [configing, { toggle, setLeft, setRight }] = useToggle(false);
  const [params, setParams] = useSafeState<any>({ limit: 1000, offset: 1, algorithmType: null, algorithmNoteName: '' });
  const [data, setData] = useSafeState<any>([]);
  const [modalshow, { toggle: modalToggle }] = useToggle(false);
  const [currentAlgorithm, setCurrentAlgorithm] = useSafeState<any>();
  const [roi, setRoi] = useSafeState<number[][]>();
  const [showIsConfig, { toggle: IsToggle }] = useToggle(false);
  const [warnshow, { toggle: warnToggle }] = useToggle(false);
  const [deleteshow, { toggle: deleteToggle }] = useToggle(false);
  const [configedInfo, setConfigedInfo] = useSafeState<any>({});
  const [refsh, { toggle: rToggle }] = useToggle();
  const [edited, { setLeft: eLeft, setRight: eRight }] = useToggle(false);
  const currentDeviceItem = useMemo(() => contextParams?.currentDeviceItem ?? {}, [contextParams]);
  const IsConfiged = useMemo(() => {
    return currentDeviceItem?.algorithms && currentDeviceItem?.algorithms?.findIndex((v: any) => v === currentAlgorithm?.id) !== -1;
  }, [currentDeviceItem, currentAlgorithm]);
  const config = useMemo(() => {
    form.resetFields();
    return getConfig(currentAlgorithm?.algorithmName as string);
  }, [currentAlgorithm]);
  const ConfigData = useMemo(() => {
    const dArr = currentDeviceItem?.algorithms || [];
    if (!showIsConfig) {
      return data;
    }
    return data?.filter((item: any) => dArr?.findIndex((v: any) => v === item?.id) !== -1);
  }, [data, showIsConfig, currentDeviceItem]);
  const save = () => {
    form.validateFields().then((res) => {
      modalToggle();
      const params = res;
      const data = res.configJson.Analysis;
      res.configJson.Output?.Bbox?.forEach((v: number) => {
        if (v === 1) {
          params.configJson.Output.ImageBbox = true;
        }
        if (v === 2) {
          params.configJson.Output.RoiBbox = true;
        }
      });
      const [MaxWidth, MaxHeight] = data.Rule.AreaRois.Max || [];
      const [MinWidth, MinHeight] = data.Rule.AreaRois.Min || [];
      params.configJson.Analysis.Rule.AreaRois.MaxWidth = MaxWidth;
      params.configJson.Analysis.Rule.AreaRois.MaxHeight = MaxHeight;
      params.configJson.Analysis.Rule.AreaRois.MinWidth = MinWidth;
      params.configJson.Analysis.Rule.AreaRois.MinHeight = MinHeight;
      //感兴趣区域
      params.configJson.Analysis.Rois = roi;
      params.cid = currentDeviceItem?.cid;
      params.deviceName = currentDeviceItem?.name;
      params.algorithmId = currentAlgorithm?.id;
      params.algorithmName = currentAlgorithm?.algorithmNoteName;
      delete params.configJson.Output.Bbox;
      delete params.configJson.Analysis.Rule.AreaRois.Max;
      delete params.configJson.Analysis.Rule.AreaRois.Min;
      delete params.nickname;
      params.configJson.Analysis.Rule = [params.configJson.Analysis.Rule];
      if (IsConfiged) {
        updateAlarmTask({ ...params, id: configedInfo?.id }).then((res) => {
          if (res.code === 0) {
            message.success('更新成功');
            eLeft()
            SocketEmitter.emit('configRefsh', 'update');
          } else {
            message.success(res.message);
          }
        });
      } else {
        saveAlarmTask(params).then((res) => {
          if (res.code === 0) {
            message.success('应用成功');
            eLeft()
            updateParams({ currentDeviceItem: { ...currentDeviceItem, algorithms: [...(currentDeviceItem?.algorithms || []), currentAlgorithm?.id] } });
            SocketEmitter.emit('configRefsh', 'add');
          } else {
            message.success(res.message);
          }
        });
      }
    });
  };
  const deleteOk = () => {
    deleteToggle();
    deleteAlarmTask(configedInfo?.id).then((res) => {
      if (res.code === 0) {
        message.success('取消算法配置成功');
        rToggle();
        setLeft();
        const newAlgorithms = without(currentDeviceItem?.algorithms, currentAlgorithm?.id);
        updateParams({ currentDeviceItem: { ...currentDeviceItem, algorithms: newAlgorithms } });
        SocketEmitter.emit('configRefsh', 'delete');
      } else {
        message.warning(res.message);
      }
    });
  };
  const onOk = () => {
    eLeft()
    if (roi?.length === 0) {
      warnToggle();
    } else {
      modalToggle();
    }
  };
  //查询算法列表
  useUpdateEffect(() => {
    noCachequeryAlgorithmList(params).then((res) => setData(res));
  }, [params]);
  useEffect(() => {
    if (data) {
      setCurrentAlgorithm(data[0]);
    }
  }, [data]);
  useEffect(() => {
    if (IsConfiged) {
      setRight();
    } else {
      setLeft();
      const newconfig = {
        configed: false,
        configJson: {
          Analysis: {
            DetectInterval: config?.DetectInterval,
            Rule: {
              AreaRois: {
                MinDuration: config?.MinDuration,
                Min: config?.Min,
                Max: config?.Max,
                MinObjectNumber: config?.MinObjectNumber,
              },
              ColorRois: {
                DetectColor: config?.DetectColor,
              },
            },
          },
          Output: {
            Bbox: [1, 2],
          },
        },
      };
      form.setFieldsValue(newconfig);
    }
  }, [config, IsConfiged]);
  useUpdateEffect(() => {
    if (!currentDeviceItem || !currentAlgorithm) {
      return;
    }
    if (IsConfiged) {
      setRight();
      alarmTaskInfo(currentDeviceItem?.cid, currentAlgorithm?.id).then((res) => {
        if (res.code === 0) {
          setConfigedInfo(res?.data);
          const { configJson } = res?.data;
          const { Analysis } = configJson;
          const newconfig = {
            configed: true,
            configJson: {
              Analysis: {
                DetectInterval: Analysis?.DetectInterval,
                Rule: {
                  AreaRois: {
                    MinDuration: Analysis?.Rule[0]?.AreaRois?.MinDuration,
                    Min: [Analysis?.Rule[0]?.AreaRois?.MinWidth, Analysis?.Rule[0]?.AreaRois?.MinHeight],
                    Max: [Analysis?.Rule[0]?.AreaRois?.MaxWidth, Analysis?.Rule[0]?.AreaRois?.MaxHeight],
                    MinObjectNumber: Analysis?.Rule[0]?.AreaRois?.MinObjectNumber,
                  },
                  ColorRois: {
                    DetectColor: Analysis?.Rule[0]?.ColorRois?.DetectColor,
                  },
                },
              },
              Output: {
                Bbox: [configJson?.Output?.ImageBbox ? 1 : undefined, configJson?.Output?.RoiBbox ? 2 : undefined],
              },
            },
          };
          form.setFieldsValue(newconfig);
        }
      });
    } else {
      setLeft();
      setConfigedInfo({});
    }
  }, [currentDeviceItem, currentAlgorithm, refsh]);

  useEffect(() => {
    if (!configing) {
      if (IsConfiged) {
        deleteToggle();
      }
    }
  }, [configing]);
  const [preAthm, setPreAthm] = useSafeState<any>({});
  const levelPage = useCallback(() => {
    warnToggle();
    eLeft();
    if (preAthm?.cid) {
      updateParams({ currentDeviceItem: cloneDeep(preAthm) });
    } else {
      setCurrentAlgorithm(preAthm);
    }
  }, [preAthm]);
  const changeAthm = (item: any) => {
    if (edited) {
      warnToggle();
      setPreAthm(item);
    } else {
      setCurrentAlgorithm(item);
    }
  };
  const changeDevice = useMemoizedFn((item: any) => {
    if (edited) {
      warnToggle();
      setPreAthm(item);
    } else {
      updateParams({ currentDeviceItem: cloneDeep(item) });
    }
  });
  useEffect(() => {
    SocketEmitter.on('editChange', changeDevice);
    return () => SocketEmitter.on('editChange', changeDevice);
  }, []);
  return (
    <>
      {isEmpty(currentDeviceItem) ? (
        <Empty style={{ marginTop: '120px' }} description={'暂无设备'} />
      ) : (
        <div className="right-content">
          <div className="RightContent">
            <div className="choseAlgorithm">
              <GradientTitle title="算法选择" />
              <div className="choseAlgorithmContent">
                <Row className="filterScene" gutter={[0, 14]}>
                  <Col xl={24} xxl={17} className="leftfilter">
                    {/* <div className="lable">适用场景：</div> */}
                    <Scenes
                      onChange={(v) => {
                        setParams((old: any) => ({ ...old, algorithmType: v === 0 ? null : v }));
                      }}
                    />
                  </Col>
                  <Col xl={24} xxl={7} className="rightfilter" ref={ref} style={{ justifyContent: size && size?.width > 900 ? 'flex-start' : 'flex-end' }}>
                    <div className="viewconfiged" onClick={IsToggle}>
                      {showIsConfig ? '查看全部' : '查看已配置'}
                    </div>
                    <div className="inputbox">
                      <Input
                        placeholder="请输入算法名称"
                        onChange={(v) => {
                          setParams((old: any) => ({ ...old, algorithmNoteName: v.target.value }));
                        }}
                        className="vidc-Input"
                        allowClear={false}
                        suffix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" />}
                      />
                    </div>
                  </Col>
                </Row>
                <div className="algorithms">
                  <ChildTaskCarousel
                    list={ConfigData}
                    render={(item: any) => (
                      <ChildItemNode
                        isConfig={currentDeviceItem?.algorithms && currentDeviceItem?.algorithms?.findIndex((v: any) => v === item?.id) !== -1}
                        currentAlgorithm={currentAlgorithm as string}
                        changeAthm={changeAthm}
                        item={item}
                      />
                    )}
                  ></ChildTaskCarousel>
                </div>
              </div>
            </div>
            <AlgorithmConfig
              defaultRoi={configedInfo?.configJson?.Analysis?.Rois}
              config={config}
              form={form}
              AlgorithmName={currentAlgorithm?.algorithmNoteName || ''}
              toggle={toggle}
              eRight={eRight}
              deleteToggle={deleteToggle}
              configing={configing}
              setRoi={setRoi}
              cid={currentDeviceItem?.cid}
            />
            <OperatingBounced
              isShow={deleteshow}
              title="关闭确认"
              icon={<img src={warnImg} />}
              onCancel={deleteToggle}
              onOk={deleteOk}
              content="确认要给设备取消配置该算法任务吗？"
            ></OperatingBounced>
            <OperatingBounced
              title="提示"
              isShow={warnshow}
              icon={<img src={warnImg} />}
              onCancel={warnToggle}
              onOk={edited?levelPage:warnToggle}
              content={edited ? '离开页面，当前算法配置信息无法保存！' : 'ROI区域未设置，无法保存配置信息！'}
            ></OperatingBounced>
            <OperatingBounced
              isShow={modalshow}
              title={IsConfiged ? '修改确认' : '配置确认'}
              icon={IsConfiged ? <img src={warnImg} /> : <IconFont style={{ color: 'var(--primary)' }} type="icon-danchuang_peizhi" />}
              onCancel={modalToggle}
              onOk={save}
              content={IsConfiged ? '确认要将修改后的配置应用于任务吗？' : '确认要给设备配置该算法任务吗？'}
            ></OperatingBounced>
          </div>
          <div className="btarea">
            <Button className="btnok" type="primary" onClick={onOk} disabled={!configing}>
              应用
            </Button>
          </div>
        </div>
      )}
    </>
  );
}
