import {  useRequest,  useToggle,  useUpdateEffect } from 'ahooks';
import { useInfiniteScroll, useSimpleState,SocketEmitter } from '@cloud-app-dev/vidc';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { size ,cloneDeep} from 'lodash-es';
import { ListItem, VideoContext } from '../../Context';
import DeviceHeader from '../DeviceHeader';
import DeviceCard from '../DeviceCard';
import { getDevices } from '../utils';
import { queryAlgorithmList } from '@src/service/ais_app';

import './index.less';

const PAGE_SIZE = 20;

export default function DeviceList() {
  const {data:sflist} = useRequest(() => queryAlgorithmList({limit:1000,offset:0}))
  const { params, updateParams } = useContext(VideoContext);
  const [state, stateChange] = useSimpleState({ type: undefined, state: undefined, deviceTagId:undefined, keywords: undefined, forceKey: Date.now() });
  const ref = useRef<HTMLDivElement>(null);
  const [refsh,{toggle}] = useToggle(false)
  const groupId = useMemo(() => params.currentTreeItem?.id ?? '', [params]);
  useEffect(()=>{
    SocketEmitter.on('configRefsh',toggle)
    return ()=>SocketEmitter.on('configRefsh',toggle)
  },[])
  /**
   * 获取设备和总数
   * @param page
   * @param pageSize
   * @param oldlist
   * @returns
   */
  const getLoadMoreList = (page: number, pageSize: number, oldlist: any): Promise<{ list: ListItem[]; total: number }> => {
    const options = {
      buzGroupId:groupId,
      queryAlgorithm:true,
      offset: (page + 1) * pageSize,
      limit: pageSize,
      isPagination: true,
      sortField: 'name',
      sortOrder: 'desc',
      ...state,
      type: state.type ? [state.type] : undefined
    };
    return getDevices(options).then((result) => ({ list: [...oldlist, ...result.list || []], total: result.totalCount, totalPage: result.totalPage }));
  };

  /*
   * @desc 获取设备列表
   */
  const { data = { list: [] as ListItem[] } } = useInfiniteScroll<ListItem>(
    (d) => {
      // offset 0开始
      const page = d?.list.length ? Math.ceil(d.list.length / PAGE_SIZE) - 1 : -1;
      return getLoadMoreList(page, PAGE_SIZE, d ? d?.list : []);
    },
    {
      target: ref,
      reloadDeps: [state, groupId],
      isNoMore: (d) => (d ? size(d?.list) === +d?.total : false),
    }
  );
  useEffect(()=>{
    if(!params.currentDeviceItem){
      updateParams({ currentDeviceItem: cloneDeep(data?.list[0]) });
    }
  },[data?.list])
  const onSelect = (item:any) => {
    SocketEmitter.emit('editChange',item)
  }
  const [dataCopy,setDataCopy] = useState<any>([])
  useEffect(()=>{
    setDataCopy(data?.list)
  },[data])

  useUpdateEffect(()=>{
    if(params?.currentDeviceItem){
      setDataCopy((old:any) => {
        const index = old.findIndex((v:any)=>v?.cid===params?.currentDeviceItem?.cid)
        old[index] = params?.currentDeviceItem;
        return [...old]
      })
    }
  },[refsh])
  return (
    <div className="video-device-list">
      <DeviceHeader
        onKeywordChange={(val) => stateChange({ keywords: val } as any)}
        total={params?.currentTreeItem ? params?.currentTreeItem?.totalCount : params?.deviceCount?.totalCount}
        onlineTotal={params.currentTreeItem ? params.currentTreeItem?.onlineCount : params?.deviceCount?.onlineCount}
        type={state.type}
        state={state.state}
        deviceTagId={state.deviceTagId}
        onChange={stateChange}
      />
      <div className="device-items" ref={ref} style={{ padding: '0 16px' }}>
        {dataCopy?.map((item: ListItem,index:number) => (<DeviceCard sflist={sflist || []} item={item}  key={`${item.cid}-${index}`} onClick={onSelect} />))}
      </div>
    </div>
  );
}
