import { IconFont } from '@cloud-app-dev/vidc';
import { DeviceType } from '@src/ais_app/core/device';
import { useToggle } from 'ahooks';
import { Input, Popover, Radio, Form, DatePicker, Tooltip, } from 'antd';
import { useForm } from 'antd/es/form/Form';
import dayjs from 'dayjs';
import './index.less';

interface ITreeHeaderProps {
  onlineTotal: number;
  total: number;
  onKeywordChange: (val: string) => void;
  type?: number;
  state?: number;
  deviceTagId?:string
  onChange?: (options: any) => void;
}

function DeviceHeader({ onlineTotal, total, onKeywordChange, type, state,deviceTagId, onChange }: ITreeHeaderProps) {
  const [flag, { toggle }] = useToggle(false);
  const [form] = useForm()
  const clearKeyword = () => {
    onKeywordChange('');
    toggle();
  };
  const Change = () => {
   const params = form.getFieldsValue();
   params.startTime = params.startTime?.valueOf();
   params.endTime = params.endTime?.valueOf();
   onChange && onChange(params)
    
  }
  return (
    <div className="video-device-header">
      <div className="left">
        {/* <IconFont type="icon-renwupeizhi_shebeirenwu_shebei-1" style={{ marginRight: 10, fontSize: 20 }} /> */}
        <span className="left-title">设备列表</span>

        <span className="device-count">
          <i>{onlineTotal}</i>/{total}
        </span>
      </div>
      <div className="right">
        <span onClick={toggle}>
          <IconFont title="搜索" type="icon-renwupeizhi_shebeirenwu_sousuo" />
        </span>
        <span>
          <Popover
            content={
              <div className="handlecontent-devicetest">
                <Form form={form} layout="vertical" onValuesChange={Change}>
                  <Form.Item name="startTime" label="接入时间" style={{marginBottom:'12px'}}>
                    <DatePicker disabledDate={(currentDate)=>currentDate > dayjs()} format="YYYY-MM-DD HH:mm:ss" style={{ width: '100%' }} placeholder="开始时间" showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }} />
                  </Form.Item>
                  <Form.Item name="endTime">
                    <DatePicker disabledDate={(currentDate)=>currentDate < form.getFieldValue('startTime')} style={{ width: '100%' }} format="YYYY-MM-DD HH:mm:ss" placeholder="结束时间" showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }} />
                  </Form.Item>
                  <Form.Item name="type" label="类型">
                    <Radio.Group style={{width:'100%'}}>
                      <Radio value={undefined} style={{width:"30%"}}>全部</Radio>
                      {DeviceType.map((v) => (
                        <Tooltip key={v.code} title={v.name} placement="bottom"><Radio style={{width:"30%"}} value={v.code} key={v.code} children={v.name} /></Tooltip>
                      ))}
                    </Radio.Group>
                  </Form.Item>
                  <Form.Item name="state" label="状态">
                    <Radio.Group style={{width:'100%'}}>
                      <Radio style={{width:"30%"}} value={undefined}>全部</Radio>
                      <Radio  style={{width:"30%"}} value={1}>在线</Radio>
                      <Radio  style={{width:"30%"}} value={0}>离线</Radio>
                    </Radio.Group>
                  </Form.Item>
                  <Form.Item name="algorithmFlag" label="配置算法">
                    <Radio.Group style={{width:'100%'}}>
                      <Radio style={{width:"30%"}} value={undefined}>全部</Radio>
                      <Radio style={{width:"30%"}} value={1}>是</Radio>
                      <Radio  style={{width:"30%"}} value={0}>否</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Form>
              </div>
            }
            trigger="click"
            placement="bottom"
            overlayClassName="video-device-params-popover"
          >
            <IconFont title="筛选" type="icon-shipinchakan_shaixuan" style={{ fontSize: '16px' }} />
          </Popover>
        </span>
      </div>

      {flag && (
        <div className="searchinput">
          <Input
            placeholder="请输入设备名称"
            autoFocus
            style={{ fontSize: 'var(--fs-small)', height:32 }}
            onChange={(e) => onKeywordChange(e.target.value)}
            prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: '12px' }} />}
            suffix={
              <IconFont type="icon-renwupeizhi_shebeirenwu_sousuoguanbi" onClick={clearKeyword} style={{ cursor: 'pointer', fontSize: '12px' }} />
            }
          />
        </div>
      )}
    </div>
  );
}

export default DeviceHeader;
