.video-device-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  font-size: var(--fs);
  position: relative;
  .left-title {
    color: rgba(0, 0, 0, 0.9);
    /* Title/Medium */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
  }
  .left,
  .right {
    display: flex;
    align-items: center;
  }
  .custom-render-box {
    height: 40px;
    line-height: 40px;
  }
  .device-count {
    padding-left: 8px;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    i {
      font-style: normal;
      color: var(--primary);
    }
  }

  .right {
    & > span {
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .searchinput {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--content-bg);
    padding: 10px 16px;
    z-index: 99;
  }
}
.handlecontent-devicetest {
  width: 250px;
  .ant-radio-wrapper > span:last-child {
    display: inline-block;
    width: calc(100% - 16px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.video-device-params-popover{
  .ant-popover-inner{
    padding: 16px;
  }
}