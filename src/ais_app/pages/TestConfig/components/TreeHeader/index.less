.video-tree-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  font-size: var(--fs);
  position: relative;
  .left-title {
    color: rgba(0, 0, 0, 0.9);
    /* Title/Medium */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
  }
  .left,
  .right {
    display: flex;
    align-items: center;
  }
  .custom-render-box {
    height: 40px;
    line-height: 40px;
  }
  .tree-device-count {
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    padding-left: 8px;
    i {
      font-style: normal;
      color: var(--primary);
    }
  }
  .tree-device-precent {
    display: inline-block;
    margin-left: 10px;
    background-color: rgba(46, 230, 168, 0.14);
    border-radius: 13px;
    color: var(--secondary3);
    font-size: var(--fs-small);
    padding: 1px 4px;
  }
  .right {
    & > span {
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .searchinput {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--content-bg);
    padding: 10px 16px;
    z-index: 99;
  }
}
