import { IconFont } from '@cloud-app-dev/vidc';
import { useToggle } from 'ahooks';
import { Input } from 'antd';
import { useMemo } from 'react';

import './index.less';

interface ITreeHeaderProps {
  onlineTotal: number;
  total: number;
  setExpandAll: () => void;
  onKeywordChange: (val: string) => void;
  expandAll: boolean;
}

function TreeHeader({ onlineTotal = 0, total = 0, expandAll,onKeywordChange, setExpandAll }: ITreeHeaderProps) {
  const precentTemp = useMemo(() => (onlineTotal / total) * 100 || 0, [onlineTotal, total]);
  const [flag, { toggle }] = useToggle(false);

  // 优化百分比显示
  const precent = useMemo(() => {
    const cell = Math.ceil(precentTemp);
    return cell === 100 && precentTemp < 100 ? 99 : cell;
  }, [precentTemp]);

  const clearKeyword = () => {
    onKeywordChange('');
    toggle();
  };

  return (
    <div className="video-tree-header">
      <div className="left">
        {/* <IconFont type="icon-shipinchakan_shebeimululiebiao" style={{ marginRight: 10, fontSize: 20 }} /> */}
        <span className='left-title'>系统分组</span>
        <span className="tree-device-count">
          <i>{onlineTotal}</i>/{total}
        </span>
        {/* <span className="tree-device-precent">{precent}%</span> */}
      </div>
      <div className="right">
        <span onClick={toggle}>
          <IconFont title="搜索" type="icon-renwupeizhi_shebeirenwu_sousuo" />
        </span>
        <span onClick={setExpandAll}>{expandAll ? <IconFont type="icon-renwupeizhi_shebeirenwu_zhankaicaidan" title="收起" /> : <IconFont style={{fontSize:'16px'}} type="icon-renwupeizhi_shebeirenwu_shouqicaidan" title="展开" />}</span>
      </div>

      {flag && (
        <div className="searchinput">
          <Input
            placeholder="请输入分组名称"
            autoFocus
            style={{ fontSize: 'var(--fs-small)', height:32  }}
            onChange={(e) => onKeywordChange(e.target.value)}
            prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: '12px' }} />}
            suffix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuoguanbi" onClick={clearKeyword} style={{ cursor: 'pointer', fontSize: '12px'}} />}
          />
        </div>
      )}
    </div>
  );
}

export default TreeHeader;
