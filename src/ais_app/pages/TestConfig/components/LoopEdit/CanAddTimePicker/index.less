.canAdd-timePicker{
    .itemrow~.itemrow{
        margin-top: 16px;
    }
    .itemrow{
        display: flex;
        align-items: center;
        .minusbtn,.addbtn{
            width: 32px;
            height: 32px;
            font-size: 20px;
            font-weight: 600;
            border: 1px dashed var(--gray6);
            border-radius: 4px;
            cursor: pointer;
            margin-left: 8px;
            text-align: center;
            line-height: 32px;
            span{
                color: var(--gray2);
                position: relative;
                top: -2px;
            }
        }
    }
}