import { useSafeState } from 'ahooks';
import { TimePicker } from 'antd';
import { cloneDeep, size } from 'lodash-es';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect } from 'react';
import './index.less';
export default function CanAddTimePicker({ value, onChange }: { value?: number[]; onChange?: ((v: Array<number | undefined>) => void) |undefined }) {
  const [timeDate, setTimeDate] = useSafeState<Array<number | undefined>>(value || [undefined]);
  const Minus = (i: number) => {
    const data = cloneDeep(timeDate);
    data.splice(i, 1);
    onChange && onChange(data);
  };
  const Add = () => {
    let data = cloneDeep(timeDate);
    data.push(undefined);
    onChange && onChange(data);
  };
  const change = (i: number, value: Dayjs | null) => {
    setTimeDate((old: any) => {
      const data = cloneDeep(old);
      data[i] = value && value.valueOf();
      onChange && onChange(data);
      return data;
    });
  };
  useEffect(()=>{
    if(!value){
      setTimeDate([undefined])
    }else{
      setTimeDate(value as any)
    }
  },[value])
  return (
    <div className="canAdd-timePicker">
      <ul>
        {timeDate?.map((v, i) => (
          <li key={i} className="itemrow">
            <div className="time">
              <TimePicker
                value={v ? dayjs(+v) : undefined}
                format = 'HH:00'
                showNow={false}
                onChange={(value) => {
                  change(i, value);
                }}
              />
            </div>
            {size(timeDate) > 1 && (
              <div
                className="minusbtn"
                onClick={() => {
                  Minus(i);
                }}
              >
                <span>-</span>
              </div>
            )}
            {i === 0 && size(timeDate) < 5 && (
              <div className="addbtn" onClick={Add}>
                <span>+</span>
              </div>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}
