.LoopEdit{
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    color: var(--gray7);
    .Content{
        width: 100%;
        height: 100%;
        padding: 16px 24px 80px;
        overflow: auto;
        .ant-form-item-label > label{
            width: 96px;
            display: flex;
            justify-content: flex-end;
        }
        .test-info{
            padding: 16px 0 0;
            width: 1100px;
        }
        .config-info{
            padding: 16px 0 0;
            margin: 0 -16px;
            .ant-checkbox-wrapper{
                margin: 8px 16px;
                width: 192px;
            }
            .scenes {
                display: flex;
                align-items: center;
                margin-left: 16px;
                margin-bottom: 8px;
                .currentItem.sceneItem{
                  border: 1px solid var(--secondary2);
                  color: var(--secondary2);
                }
                .sceneItem ~ .sceneItem {
                  margin-left: 8px;
                }
                .sceneItem {
                  padding: 6px 18px;
                  box-sizing: border-box;
                  border: 1px solid var(--gray7);
                  border-radius: var(--radius1);
                  color: var(--gray2);
                  font-size: var(--fs-small);
                  cursor: pointer;
                }
              }
        }
        .base-info{
            padding: 16px 0 0;
            .runtime{
                position: relative;
                .ant-form-item-control-input-content{
                    display: flex;
                }
                .ant-form-item:last-child{
                    position: absolute;
                    top: 0px;
                    left: 0px;
                    width: 30px;
                    height: 30px;
                    z-index: 2;
                }
                .addbtn{
                    margin-left: 8px;
                    background: var(--primary);
                    width: 30px;
                    height: 30px;
                    border: 1px dashed rgba(255, 255, 255, 0.35);
                    border-radius: 4px;
                    text-align: center;
                    line-height: 30px;
                    color: var(--gray1);
                    cursor: pointer;
                }
            }
        }
    }
    .btarea {
        width: 100%;
        height: 64px;
        position: absolute;
        bottom: 0;
        left: 0;
        border-top: 1px solid var(--gray7);
        background: var(--content-bg);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;
        .btns {
            width: 144px;
            display: flex;
            justify-content: space-between;
            .okbtn{
                background: var(--primary-light);
                border-radius: var(--radius1);
            }
            .cancel{
                border-radius: var(--radius1);
                border: 1px solid var(--gray7);
                color: var(--gray2);
            }
        }
      }
}