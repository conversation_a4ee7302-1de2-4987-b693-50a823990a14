import { Button, Form, Input, DatePicker, Checkbox, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import CanAddTimePicker from './CanAddTimePicker';
import warnImg from '@src/assets/image/warn.png';
import dayjs from 'dayjs';
import DeviceSelect from '../../../../components/DeviceAssign';
import { noCachequeryAlgorithmList ,savePollingTask, updatePollingTask ,queryDeviceList} from '@src/service/ais_app';
import Scenes from '../../../../components/Scenes';
import { useToggle, useUpdateEffect } from 'ahooks';
import GradientTitle from '@src/components/GradientTitle';
import OperatingBounced from '@src/components/OperatingBounced';
import { useSafeState } from 'ahooks';
import { useState } from 'react';
import { uuid } from '@cloud-app-dev/vidc';
import { algConfig } from '../../AlgorithmConfig';
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const CheckboxGroup = Checkbox.Group;

import './index.less';

export default function LoopEdit({ EditToggle, isAdd, editData }: { EditToggle: () => void; isAdd: boolean; editData: any }) {
  const [form] = useForm();
  const [params, setParams] = useSafeState<any>({ limit: 1000, offset: 1, algorithmType: null });
  const [data, setData] = useSafeState<any>();
  const [key,setKey] =useState(uuid())
  const [open,{toggle}] = useToggle(false)
  const [selectItems,setSelectItems] = useSafeState<any[]>([])
  useUpdateEffect(() => {
    if(!isAdd){
      queryDeviceList({cidList:editData?.cids}).then(res=>{
        setSelectItems(res?.data?.list)
        setKey(uuid())
      })
      form.setFieldsValue({
        ...editData,
        effectiveDate: [dayjs(editData?.effectiveStartTime), dayjs(editData?.effectiveEndTime)],
      });
    }
  }, [editData,isAdd]);
  //查询算法列表
  useUpdateEffect(() => {
    
    noCachequeryAlgorithmList(params).then((res) => {
      const arr = algConfig?.filter(v=>v.corvette)
      const result = res?.filter(item => arr?.findIndex(v=>v.aname === item.algorithmName) !== -1)
      setData(result)
    });
  }, [params]);
  const OnOk = () => {
    form.validateFields().then((res) => {
      const [startDate, endDate] = res?.effectiveDate;
      delete res.effectiveDate;
      const params = {
        cids: selectItems?.map(v=>v.cid),
        effectiveStartTime: startDate.set({'hour': 0, 'minute': 0,'second':0}).valueOf(),
        effectiveEndTime: endDate.set({'hour': 23, 'minute': 59,'second':59}).valueOf(),
        ...res,
      };
      if (!isAdd) {
        updatePollingTask({ id: editData?.id, ...params }).then((res) => {
          if (res.code === 0) {
            message.success('编辑轮巡任务成功');
            EditToggle();
          } else {
            if(res.message === '任务字数两超过400，请重新选择！'){
              toggle()
            }else{
              message.warning(res.message);
            }
          }
        });
      } else {
        savePollingTask(params).then((res) => {
          if (res.code === 0) {
            message.success('新增轮巡任务成功');
            EditToggle();
          } else {
            message.warning(res.message);
          }
        });
      }
    });
  };
  return (
    <>
      <div className="LoopEdit">
        <div className="Content">
          <Form form={form}>
            <GradientTitle title="基本信息" margin="0 0 16px">
              <div className="base-info">
                {
                  !isAdd && <Form.Item name="taskName" label="任务名称" rules={[{ required: true }]}>
                  <Input disabled placeholder="请输入" style={{ width: '400px' }} />
                </Form.Item>
                }
                <Form.Item name="effectiveDate" label="任务有效期" rules={[{ required: true }]}>
                  <RangePicker />
                </Form.Item>
                <Form.Item name="executionTime" label="执行时间" rules={[{ required: true }]}>
                  <CanAddTimePicker />
                </Form.Item>
                <Form.Item name="taskDescribe" label="任务描述" rules={[{ max: 360 }]}>
                  <TextArea rows={3} placeholder="请输入" />
                </Form.Item>
              </div>
            </GradientTitle>
            <GradientTitle title="算法配置" margin="0 0 16px">
              <div className="config-info">
                <Scenes
                  onChange={(v) => {
                    setParams((old: any) => ({ ...old, algorithmType: v === 0 ? null : v }));
                  }}
                />
                <Form.Item name="algorithmIds">
                  <CheckboxGroup style={{ width: '100%' }}>
                    {data?.map((v: any, i: number) => (
                      <Checkbox value={v?.id} key={i}>
                        {v?.algorithmNoteName}
                      </Checkbox>
                    ))}
                  </CheckboxGroup>
                </Form.Item>
              </div>
            </GradientTitle>
            <GradientTitle title="任务范围" margin="0 0 16px">
              <div className="test-info">
                <DeviceSelect key={key} selectItems={selectItems} onSelectChange={(v:any)=>{
                  setSelectItems(v)
                }}/>
              </div>
            </GradientTitle>
          </Form>
        </div>
        <div className="btarea">
          <div className="btns">
            <Button className="cancel" onClick={EditToggle}>
              取消
            </Button>
            <Button type="primary" className="okbtn" onClick={OnOk}>
              确定
            </Button>
          </div>
        </div>
      </div>
      <OperatingBounced isShow={open} onCancel={toggle} onOk={toggle} content="任务子数量（设备数*算法数）超过400，请重新选择！" icon={<img src={warnImg}/>} />
    </>
  );
}
