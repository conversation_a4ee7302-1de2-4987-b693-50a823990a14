.LoopView{
    width: 100%;
    height: 100%;
    padding: 16px 24px;
    overflow: auto;
    .config-info{
        padding: 16px 0px 0px;
        .calcs{
            margin-top: 12px;
            margin-left: 98px;
            height: max-content;
            ul{
                display: flex;
                flex-wrap: wrap;
                margin: -8px;
                .calc-item{
                    margin: 8px; 
                    width: 180px;
                    height: 28px;
                    background-color: rgba(255, 255, 255, 0.1);
                    line-height: 28px;
                    text-align: center;
                    color: var(--gray2);
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    padding: 0px 8px;
                }
            }
        }
        .info-item{
            display: flex;
            .label{
                width: 98px;
                text-align: end;
                color: var(--gray8);
            }
            .value{
                color: var(--gray2);
                display: flex;
                align-items: center;
                span{
                    color: var(--secondary2);
                }
            }
        }
    }
    .base-info{
        padding: 16px 0px 0px;
        ul{
            list-style: none;
            .info-item~.info-item{
                margin-top: 16px;
            }
            .info-item{
                display: flex;
                .label{
                    width: 98px;
                    text-align: end;
                    color: var(--gray8);
                }
                .value{
                    flex: 1;
                    color: var(--gray2);
                    display: flex;
                    align-items: center;
                    .time-box~.time-box{
                        margin-left: 8px;
                    }
                    .time-box{
                        width: 80px;
                        height: 28px;
                        background-color: rgba(255, 255, 255, 0.1);
                        text-align: center;
                        line-height: 28px;
                    }
                }
            }
        }
    }
}