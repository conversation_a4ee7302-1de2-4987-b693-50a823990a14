import { IconFont } from '@cloud-app-dev/vidc';
import GradientTitle from '@src/components/GradientTitle';
import { DateToFormat, getAlgorithmNameById } from '@src/ais_app/utils';
import { queryDeviceList,queryAlgorithmList } from '@src/service/ais_app';
import { useSafeState,useRequest } from 'ahooks';
import { size } from 'lodash-es';
import { useEffect } from 'react';
import './index.less';
import DeviceIcon from '@src/ais_app/components/DeviceIcon';
export default function LoopView({ data }: { data: any }) {
  const {data:sflist} = useRequest(() => queryAlgorithmList({limit:1000,offset:0}))
  const baseType = [
    {
      label: '任务名称：',
      fname: data?.taskName || '-',
    },
    {
      label: '任务创建人：',
      fname: data?.createdBy || '-',
    },
    {
      label: '创建时间：',
      fname: DateToFormat(data?.createdTime, 'YYYY-MM-DD HH:mm:ss'),
    },
    {
      label: '最后更新时间：',
      fname: DateToFormat(data?.updatedTime, 'YYYY-MM-DD HH:mm:ss'),
    },
    {
      label: '任务状态：',
      fname: data?.taskState === 1 ? '运行中' : '暂停中',
    },
    {
      label: '任务有效期：',
      fname: [data?.effectiveStartTime, data?.effectiveEndTime],
    },
    {
      label: '执行时间：',
      fname: data?.executionTime || '-',
    },
    {
      label: '任务描述：',
      fname: data?.taskDescribe || '-',
    },
  ];
  const [diviceData, setDeviceData] = useSafeState<any[]>([]);
  useEffect(() => {
    const params = {
      cidList: data?.cids,
    };
    //调接口
    queryDeviceList(params).then((res) => {
      if (res.code === 0) {
        setDeviceData(res.data.list || []);
      }
    });
  }, [data]);
  return (
    <div className="LoopView">
      <GradientTitle title="基本信息" margin="0 0 16px">
        <div className="base-info">
          <ul>
            {baseType.map((v, i) => (
              <li key={i} className="info-item">
                <div className="label">{v.label}</div>
                {v.label !== '任务有效期：' && v.label !== '执行时间：' && <div className="value">{v.fname}</div>}
                {v.label === '任务有效期：' && (
                  <div className="value">
                    {DateToFormat(v.fname[0], 'YYYY-MM-DD HH:mm:ss')}
                    <IconFont type="icon-jiantou_qiehuanyou" style={{ margin: '0 16px' ,fontSize:'18px',color:'var(--gray6)',transform: 'rotateX(180deg)'}} />
                    {DateToFormat(v.fname[1], 'YYYY-MM-DD HH:mm:ss')}
                  </div>
                )}
                {v.label === '执行时间：' && (
                  <div className="value">
                    {data?.executionTime?.map((item: string, index: number) => (
                      <div key={index} className="time-box">
                        {DateToFormat(item, 'HH:mm')}
                      </div>
                    ))}
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      </GradientTitle>
      <GradientTitle title="算法配置" margin="0 0 16px">
        <div className="config-info">
          <ul>
            <li className="info-item">
              <div className="label">算法选择：</div>
              <div className="value">
                共 <span>{size(data?.algorithmIds) ?? 0}</span> 个算法
              </div>
            </li>
          </ul>
          <div className="calcs">
            <ul>
              {data?.algorithmIds?.map((v: number, i: number) => (
                <li key={i} className="calc-item">
                  {getAlgorithmNameById(sflist||[],v)}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </GradientTitle>
      <GradientTitle title="任务范围">
        <div className="config-info">
          <ul>
            <li className="info-item">
              <div className="label">设备选择：</div>
              <div className="value">
                共 <span>{size(data?.cids) ?? 0}</span> 个设备
              </div>
            </li>
          </ul>
          <div className="calcs">
            <ul>
              {data?.cids?.map((v: number, i: number) => (
                <li key={i} className="calc-item">
                  <DeviceIcon state={diviceData?.find((item) => item.cid === v)?.state} type={diviceData?.find((item) => item.cid === v)?.type}/> {diviceData?.find((item) => item.cid === v)?.name}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </GradientTitle>
    </div>
  );
}
