import './index.less';
import DeviceIcon from '@src/ais_app/components/DeviceIcon';
import { VideoContext } from '../../Context';
import { useMemo, useContext } from 'react';
import { getAlgorithmNameById } from '@src/ais_app/utils';
import { size } from 'lodash-es';
export default function DeviceCard({ sflist,item, onClick }: {sflist:any[], item: any; onClick: (item: any) => void }) {
  const { params } = useContext(VideoContext);
  const selected = useMemo(() => params.currentDeviceItem?.cid === item?.cid, [params]);
  return (
    <div
      className={`${selected && 'selectDevicetest'} DeviceCardtest`}
      onClick={() => {
        onClick(item);
      }}
    >
      <div className="top">
        <div className="leftcontent">
          <div className="videoIcon">
            <DeviceIcon state={item.state} type={item.type} />
          </div>
          <div className="videoTitle" title={item?.name}>
            {item?.name}
          </div>
        </div>
        <div className="calcCount">{size(item?.algorithms)}</div>
      </div>
      <div className="calcs">
        {item?.algorithms?.slice(0,4)?.map((v:number, i:number) => (
          <div className="calcItem" key={i} title={getAlgorithmNameById(sflist || [],v)}>
            {getAlgorithmNameById(sflist || [],v)}
          </div>
        ))}
        {size(item?.algorithms) === 0 && <div style={{ marginLeft: '3px',color:'var(--gray8)' }}>暂无配置算法</div>}
        {size(item?.algorithms) > 4 && <div style={{ marginLeft: '3px' }}>...</div>}
      </div>
    </div>
  );
}
