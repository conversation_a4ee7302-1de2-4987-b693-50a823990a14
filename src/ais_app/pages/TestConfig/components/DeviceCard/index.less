.DeviceCardtest:hover {
  background: #E8F3FF;
  border: 2px solid var(--secondary2);
  padding: 7px 5px;
  .top .cardHandle{
    display: block;
  }
}
.selectDevicetest.DeviceCardtest{
  background:  #E8F3FF;
  border: 2px solid var(--secondary2);
  padding: 7px 5px;
  .top .leftcontent .videoTitle{
    color: var(--secondary3);
  }
}
.DeviceCardtest {
  width: 100%;
  height: 70px;
  margin-bottom: 8px;
  border: 1px solid var(--gray7);
  border-radius: 6px;
  color: var(--gray2);
  background-color: transparent;
  padding: 8px 6px;
  cursor: pointer;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .calcCount{
      width: 22px;
      height: 22px;
      background: rgba(29, 187, 255, 0.2);
      border: 1px solid #1DBBFF;
      color: #1DBBFF;
      border-radius: 50%;
      text-align: center;
      line-height: 20px;
    }
    .leftcontent{
      display: flex;
      align-items: center;
      width: 90%;
      .videoIcon {
        width: 20px;
        height: 20px;
        font-size: 18px;
        display: flex;
        align-items: center;
      }
      .videoTitle {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-left: 10px;
        line-height: 20px;
      }
    }
  }
  .calcs {
    margin-top: 8px;
    display: flex;
    align-items: center;
    .calcItem~.calcItem{
      margin-left: 4px;
    }
    .calcItem {
      padding: 2px 6px;
      width: 60px;
      color: var(--gray2);
      background-color: rgba(255, 255, 255, 0.15);
      font-size: var(--fs-small);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
