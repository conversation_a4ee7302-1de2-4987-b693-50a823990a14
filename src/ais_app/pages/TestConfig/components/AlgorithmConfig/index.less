.AlgorithmConfig {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  .configCenter {
    width: 100%;
    height: max-content;
    position: relative;
    .Content {
      width: 100%;
      height: 100%;
      padding: 16px 0px 16px;
      color: var(--gray1);
      .configinfobox {
        padding: 0 24px;
        .interested-area {
          background: var(--content-bg);
          margin-top: 16px;
          .fullscreen-box{
            color: var(--primary-light);
            cursor: pointer;
            width: max-content;
          }
          .draw-area {
            height: max-content;
            .roibox {
              padding: 16px 0px;
              .ROI{
                width: 100%;
                height: 100%;
                position: relative;
                .refshImg{
                  position: absolute;
                  cursor: pointer;
                  left: 50%;
                  top: 50%;
                  transform: translate(-32px,-32px);
                  z-index: 10;
                }
                .rect-mask {
                  span {
                    height: 29px;
                    line-height: 28px;
                    position: absolute;
                    background: rgba(0, 0, 0, 0.8);
                    border-radius: 2px;
                    font-size: 12px;
                    padding: 0 8px;
                    user-select: none;
                  }
                }
                canvas{
                  position: absolute;
                  left: 0;
                  top: 0;
                  width: 100%;
                  height: 100%;
                }
                .img-box{
                  width: 100%;
                  height: max-content;
                  min-height: 300px;
                  background-color: rgb(175, 175, 175);
                  img{
                    width: 100%;
                    object-fit: contain;
                  }
                }
              }
            }
            .infobox {
              padding: 16px;
              .roi-area-handle{
                .header{
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  .title{
                    display: flex;
                    align-items: center;
                    .icon{
                      width: 4px;
                      height: 16px;
                      background-color: var(--primary-light);
                    }
                    .titletext{
                      margin-left: 10px;
                      font-weight: 500;
                      color: var(--gray2);
                    }
                  }
                  .handle{
                    display: flex;
                    align-items: center;
                    .clear-btn{
                      cursor: pointer;
                      color: var(--primary-light);
                      span{
                        margin-left: 2px;
                      }
                    }
                    .add-btn{
                      margin-left: 16px;
                      .ant-btn-primary{
                        background: var(--primary-light);
                        border-radius: var(--radius1);
                      }
                    }
                  }
                }
                .tips{
                  margin-top: 12px;
                  font-size: var(--fs-small);
                  color: var(--gray8);
                }
                .areas{
                  margin-top: 12px;
                  .area-item{
                    display: flex;
                    align-items: center;
                    .name{
                      color: var(--gray2);
                    }
                    .line{
                      flex: 1;
                      border: 1px dashed var(--danger);
                    }
                    .delete-btn{
                      cursor: pointer;
                      margin-left: 16px;
                      color: var(--primary-light);
                    }
                  }
                }
              }
            }
          }
        }
        div .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
          border: none;
        }
        .ant-form-item-label > label .ant-form-item-tooltip {
          color: rgba(255, 255, 255, 0.35);
        }
        .ant-form-item {
          margin-bottom: 0px;
        }
        .baseconfig {
          display: flex;
          align-items: center;
          position: relative;
          .formitembox ~ .formitembox {
            margin-left: 120px;
          }
          .formitembox:last-child {
            cursor: pointer;
            position: absolute;
            right: 0;
          }
          .formitembox{
            .configinfo-box{
              // overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
        .moresettingBox {
          width: 100%;
          margin-top: 14px;
          background-color: rgba(255, 255, 255, 0.05);
          padding: 20px 16px;
          .ant-select:not(.ant-select-customize-input) .ant-select-selector{
            border-radius: var(--radius1);
          }
          .chosecolor {
            
          }
        }
      }
    }
  }
}
.color-box{
  width: 16px;
  height: 16px;
  display: inline-block;
  margin-right: 8px;
  position: relative;
  top:4px;
}