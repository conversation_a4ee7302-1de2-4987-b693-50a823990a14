import { Row, Col, Form, Select, InputNumber, Checkbox, Button, FormInstance } from 'antd';
import GradientTitle from '@src/components/GradientTitle';
import { IconFont } from '@cloud-app-dev/vidc';
import useDrawROI from '@src/ais_app/components/useDrawROI';
import OneAngle from '@src/ais_app/components/OneAngle';
import { coverImage } from '@src/service/ais_app';
import { useToggle, useFullscreen, useSize, useSafeState } from 'ahooks';
import { useEffect, useRef, useMemo } from 'react';
import { colorArr } from '../utils';
import { polygon } from '@turf/helpers';
import centerOfMass from '@turf/center-of-mass';
import './index.less';

const { Option } = Select;
const CheckboxGroup = Checkbox.Group;
export default function AlgorithmConfig({
  config,
  AlgorithmName,
  form,
  setRoi,
  configing,
  toggle,
  deleteToggle,
  defaultRoi,
  eRight,
  cid,
}: {
  AlgorithmName:string;
  eRight:() => void;
  deleteToggle:() => void;
  toggle: () => void;
  configing: boolean;
  config: any;
  form: FormInstance;
  setRoi: (v: any) => void;
  cid: string;
  defaultRoi: [number, number][][];
}) {
  const ref = useRef(null);
  const roiRef = useRef(null);
  const size = useSize(roiRef);
  const [isFullscreen, { enterFullscreen, exitFullscreen }] = useFullscreen(ref);
  const [moresetshow, { toggle: moreToggle }] = useToggle(false);
  const [imgUrl, setImgUrl] = useSafeState<string>('');
  const [rImg,{toggle:rImgToggle}] = useToggle(false)
  const defaultAreas: [number, number][][] = useMemo(() => {
    const { width = 0, height = 0 } = size || {};
    const Roidata = defaultRoi?.map((v: any) => {
      return v?.PolygonArea?.map((m: any) => {
        return [m.X * width, m.Y * height];
      });
    });
    return Roidata || [];
  }, [size, defaultRoi]);

  const { open, close, clear, areas, status, deleteArea,updateAreas } = useDrawROI(roiRef, { row: 18, rowItems: 32, defaultAreas });
  useEffect(() => {
    coverImage(cid).then((res) => {
      if (res.code === 0) {
        setImgUrl(res.data?.url);
      }else{
        setImgUrl('');
      }
    });
  }, [cid,rImg]);
  useEffect(() => {
    clear();
  }, [config, cid,configing]);
  useEffect(()=>{
    const { width = 0, height = 0 } = size || {};
    const roiParams = areas.map((oneItem) => ({ PolygonArea: oneItem.map((v) => ({ X: (v[0] / width).toFixed(4), Y: (v[1] / height).toFixed(4) })) }));
    setRoi(roiParams);
    close();
  },[areas])
  // 获取多边形中心点
  const centerPoints = useMemo(
    () =>
      areas
        .map((v) => [...v, v[0]])
        .map((v2) => centerOfMass(polygon([v2])))
        .map((v) => v.geometry.coordinates),
    [areas]
  );
  const InfoTip = ({ value, onChange }: { value?: boolean; onChange?: any }) => {
    const newValue = useMemo(() => value, [value]);
    return <div className="configinfo-box">{newValue ? '已配置' : '未配置'}</div>;
  };
  return (
    <div className="AlgorithmConfig">
      <GradientTitle title="算法配置" checked={form.getFieldValue('configed')?form.getFieldValue('configed'):configing} onChange={!form.getFieldValue('configed') ? toggle:deleteToggle} margin={'0 16px'} />
      <div className="configCenter">
        <div className="Content">
          <div className="configinfobox">
            <Form form={form} disabled={!configing} onValuesChange={eRight}>
              <div className="baseconfig">
                <div className="formitembox">
                  <Form.Item name="configed" initialValue={false} label="任务配置" rules={[{ required: true }]}>
                    <InfoTip />
                  </Form.Item>
                </div>
                <div className="formitembox">
                  <Form.Item name={['configJson', 'Analysis', 'DetectInterval']} label="分析频率" tooltip="分析频率" rules={[{ required: true }]}>
                    <Select style={{ width: 240 }} placeholder="请选择分析频率">
                      <Option value={-1}>I帧</Option>
                      <Option value={300}>5分钟</Option>
                    </Select>
                  </Form.Item>
                </div>
                <div className="formitembox">
                  <Form.Item
                    name={['configJson', 'Analysis', 'Rule', 'AreaRois', 'MinDuration']}
                    label="场景持续时间"
                    tooltip="用于定义结构化目标在画面中出现的时长，超过预设时长后，才视为一次告警事件。"
                    rules={[{ required: true }]}
                  >
                    <Select style={{ width: 240 }} placeholder="请选择场景持续时间">
                      <Option value={1}>1秒</Option>
                      <Option value={600}>10分钟</Option>
                      <Option value={900}>15分钟</Option>
                      <Option value={1800}>30分钟</Option>
                      <Option value={3600}>60分钟</Option>
                    </Select>
                  </Form.Item>
                </div>
                <div className="formitembox">
                  <div className="morettingbtn" onClick={moreToggle}>
                    更多高级设置
                    <span>
                      <IconFont style={{ marginLeft: '5px', transform: moresetshow ? '' : 'rotate(180deg)' }} type="icon-renwupeizhi_shebeirenwu_xiala" />
                    </span>
                  </div>
                </div>
              </div>
              <div className="moresettingBox" style={{ display: moresetshow ? 'block' : 'none' }}>
                <Row gutter={[0, 16]}>
                  <Col span={8}>
                    <Form.Item
                      name={['configJson', 'Analysis', 'Rule', 'AreaRois', 'Min']}
                      label="目标最小像素"
                      tooltip="用于过滤尺寸较小的结构化目标。低于预设宽高的结构化目标，将被当作一次无效告警。"
                    >
                      <OneAngle />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name={['configJson', 'Analysis', 'Rule', 'AreaRois', 'Max']}
                      label="目标最大像素"
                      tooltip="主要为了防范因画面被遮挡导致的算法误报。高于预设宽高的结构化目标，将被当作一次无效告警。"
                    >
                      <OneAngle />
                    </Form.Item>
                  </Col>
                  {config?.hasOwnProperty('MinObjectNumber') && (
                    <Col span={8}>
                      <Form.Item
                        name={['configJson', 'Analysis', 'Rule', 'AreaRois', 'MinObjectNumber']}
                        label="目标最小数量"
                        tooltip="针对人员聚集、车辆拥堵等算法，目标数量少于预设数值，将被当作一次无效告警。"
                      >
                        <InputNumber />
                      </Form.Item>
                    </Col>
                  )}

                  {config?.hasOwnProperty('DetectColor') && (
                    <Col span={8} className="chosecolor">
                      <Form.Item
                        name={['configJson', 'Analysis', 'Rule', 'ColorRois', 'DetectColor']}
                        label="参考色"
                        tooltip="针对裸土未苫盖算法，建议选择裸土的颜色；针对水位超标算法，建议选择水位线的颜色。"
                      >
                        <Select style={{ width: 240 }} placeholder="请选择参考色" mode="multiple">
                          {colorArr.map((v, i) => (
                            <Option key={i} value={v.value}>
                              <div className="color-box" style={{ background: v.color }}></div>
                              {v.color}{' '}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  )}
                  <Col span={8}>
                    <Form.Item
                      name={['configJson', 'Output', 'Bbox']}
                      label="原图叠加边框"
                      tooltip="原图叠加框	在输出的原图上，设置叠加结构化目标框或感兴趣区域框。不勾选叠加选项，对外告警时将直接输出原始图片。"
                    >
                      <CheckboxGroup>
                        <Checkbox value={1}>叠加结构化目标框</Checkbox>
                        <Checkbox value={2}>叠加感兴趣区域</Checkbox>
                      </CheckboxGroup>
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            </Form>
            <div className="interested-area" ref={ref} style={{ padding: isFullscreen ? '36px' : '0' }}>
              <Form.Item name="nickname" label="感兴趣区域(ROI)" tooltip="可绘制感兴趣的目标区域" rules={[{ required: true }]}>
                {isFullscreen ? (
                  <div className="fullscreen-box" onClick={exitFullscreen}>
                    <IconFont type="icon-shishiyulan_yuanhuamian" /> 退出全屏
                  </div>
                ) : (
                  <div className="fullscreen-box" onClick={enterFullscreen}>
                    <IconFont type="icon-renwupeizhi_shebeirenwu_quanping" /> 全屏
                  </div>
                )}
              </Form.Item>
              <Row className="draw-area">
                <Col span={16} className="roibox">
                  <div className="ROI">
                    <div className="img-box">
                      <img src={imgUrl} alt="" />
                    </div>
                    {
                      imgUrl === '' && <Button type="primary" className='refshImg' onClick={rImgToggle}>刷新</Button>
                    }
                    <canvas ref={roiRef} />
                    <div className="rect-mask">
                      {centerPoints.map(([x, y], i) => (
                        <span style={{ left: x, top: y }} key={i}>
                          区域{i + 1}
                        </span>
                      ))}
                    </div>
                  </div>
                </Col>
                <Col span={8} className="infobox">
                  <div className="roi-area-handle">
                    <div className="header">
                      <div className="title">
                        <div className="icon"></div>
                        <div className="titletext">{AlgorithmName}—ROI区域</div>
                      </div>
                      <div className="handle">
                        <div className="clear-btn" onClick={()=>{clear();eRight();}}>
                          <IconFont type="icon-renwupeizhi_shebeirenwu_qingkong" />
                          <span>清空区域</span>
                        </div>
                        <div className="add-btn">
                          {status === 'open' ? (
                            <Button type="primary" onClick={close}>
                              取消
                            </Button>
                          ) : (
                            <Button type="primary" onClick={()=>{open();eRight();}} disabled={!configing}>
                              添加
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="tips">( Tips：敲击“Esc”终止绘制；连接首尾点、双击鼠标 或 敲击“回车键(Enter) ”闭合绘制。)</div>
                    <ul className="areas">
                      {areas?.map((v, i) => (
                        <li key={i} className="area-item">
                          <div className="name">区域{i + 1}：</div>
                          <div className="line"></div>
                          <div
                            className="delete-btn"
                            onClick={() => {
                              deleteArea(v);
                              eRight();
                            }}
                          >
                            删除
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
