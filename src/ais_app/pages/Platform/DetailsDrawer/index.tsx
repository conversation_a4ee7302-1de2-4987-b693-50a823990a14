import { useRequest, useSafeState, useToggle, useUpdateEffect, useDebounce, useMemoizedFn, useSize, useFullscreen } from "ahooks";
import { useEffect, useContext, useMemo, useRef, useCallback } from "react";
import { Select, Input, Button, Col, message, Divider, Form, Space, Radio, Switch, Checkbox, Row, InputNumber } from "antd";
import Drawer from "@src/components/Drawer";
import { IconFont } from "@cloud-app-dev/vidc";
import { platformPage, apPlatformInfoTest } from "@src/service/ais_app";
import "./index.less";
const FormItem = Form.Item;
export default ({ row, isShow, toggleD, onFinish, platformTypeList }: any) => {
  const [form] = Form.useForm();
  const platformType = Form.useWatch("platformType", form);
  const devicePlatformSource = Form.useWatch("devicePlatformSource", form);
  const platformAccessAddress = Form.useWatch("platformAccessAddress", form);
  const [devicePlatformList, setDevicePlatformList] = useSafeState([]);
  const [isLoading, { toggle }] = useToggle(false);
  useEffect(() => {
    if (platformType && !row.id) {
      form.setFieldsValue({
        devicePlatformSource: 810,
        manufacturer: platformType === 2 ? "VISA" : "DIG",
        localRecordEnable: ["xx"],
      });
    }
  }, [platformType]);
  useEffect(() => {
    if (isShow) {
      platformPage({ limit: 99999 })
        .then((result) => {
          if (result.code === 0) {
            const list = result.data?.list || [];
            setDevicePlatformList(list.map((item: any) => ({ label: item.name, value: item.id })));
          }
        })
        .catch((err) => {});
    }
  }, [isShow]);
  useEffect(() => {
    const { subordinateDomain, info, accessEnabled, platformAccessAddress, name, platformType, devicePlatformSource, devicePlatformId } = row;
    form.setFieldsValue({
      ...subordinateDomain,
      localRecordEnable: subordinateDomain?.localRecordEnable ? ['xx','localRecordEnable']:["xx"],
      ...info,
      accessEnabled,
      platformAccessAddress,
      name,
      platformType,
      devicePlatformSource,
      devicePlatformId,
    });
  }, [row,isShow]);
  return (
    <Drawer
      closable={false}
      open={isShow}
      width={570}
      onClose={toggleD}
      forceRender
      className="details-edit-drawer"
      onOk={() => onFinish(form)}
      title={
        <div className="userform-wrapper-title">
          <div>接入配置</div>
          <div>
            <IconFont type="icon-guanbi" onClick={toggleD} />
          </div>
        </div>
      }
      getContainer={() => document.body}
    >
      <div className="details-edit-drawer-title">
        <IconFont type="icon-caret-right-small" className="icon" />
        基本信息
      </div>
      <Form form={form} layout="vertical" className="search-form">
        <FormItem name="name" label="接入名称" rules={[{ required: true, message: "请输入接入名称" }]}>
          <Input placeholder="请输入接入名称" className="user-filter-btn vidc-Input" allowClear={false} maxLength={15} showCount={true} />
        </FormItem>
        <FormItem name="platformType" label="功能类型" rules={[{ required: true, message: "请选择功能类型" }]}>
          <Select placeholder="请选择功能类型" className="user-filter-btn" onChange={() => {}} allowClear={false} options={platformTypeList} />
        </FormItem>
        {platformType ? (
          <>
            {platformType == 2 ? null : (
              <FormItem name="devicePlatformSource" label="接入类型" rules={[{ required: true, message: "请选择接入类型" }]}>
                <Radio.Group>
                  <Radio value={810}>国标</Radio>
                  <Radio value={800}>非国标</Radio>
                </Radio.Group>
              </FormItem>
            )}
            {/* info */}
            <FormItem name="manufacturer" label="接入厂商" rules={[{ required: true, message: "请选择接入厂商" }]}>
              <Radio.Group>
                {platformType == 1 ? <Radio value={"DIG"}>新接入</Radio> : null}
                {platformType == 2 ? (
                  <>
                    <Radio value={"VISA"}>VISA</Radio>
                    <Radio value={"CIE"}>CIE</Radio>
                  </>
                ) : null}
              </Radio.Group>
            </FormItem>
            <FormItem name="devicePlatformId" label="关联服务" rules={[{ required: true, message: "请选择关联服务" }]}>
              <Select placeholder="请选择关联服务" className="user-filter-btn" allowClear={false} options={devicePlatformList} />
            </FormItem>
            {devicePlatformSource === 810 ? (
              <Row style={{ alignItems: "flex-end", marginBottom: 24,flexFlow: "nowrap" }}>
                <Col span={16}>
                  <FormItem style={{ marginBottom: 0 }} name="ip" label="平台IP/端口" rules={[{ required: true, message: "请输入平台IP" }]}>
                    <Input placeholder="请输入平台IP" className="user-filter-btn vidc-Input" allowClear={false} />
                  </FormItem>
                </Col>
                <Row style={{ alignItems: "center", flexFlow: "nowrap" }}>
                <span style={{ margin: "0px 16px" }}>:</span>
                <Col style={{
                  flex:'auto',
                  maxWidth:"max-content"
                }} span={9}>
                  <FormItem
                    className="form-sipPort"
                    style={{ display: "flex", marginBottom: 0 }}
                    name="sipPort"
                    rules={[{ required: true, message: "请输入端口" }]}
                  >
                      <Input placeholder="请输入端口" className="user-filter-btn vidc-Input" allowClear={false} />
                  </FormItem>
                </Col>
                </Row>
              </Row>
            ) : null}

            {devicePlatformSource === 810 ? (
              <>
                <FormItem name="localRecordEnable" label="数据接入">
                  <Checkbox.Group>
                    <Checkbox value={"xx"} disabled>
                      实时视频
                    </Checkbox>
                    <Checkbox value={"localRecordEnable"}>边缘录像</Checkbox>
                  </Checkbox.Group>
                </FormItem>
                <FormItem name="code" label="国标编码" rules={[{ required: true, message: "请输入接入名称" }]}>
                  <Input placeholder="请输入接入名称" className="user-filter-btn vidc-Input" allowClear={false} />
                </FormItem>
                <FormItem name="password" label="注册密码">
                  <Input placeholder="请输入接入名称" className="user-filter-btn vidc-Input" allowClear={false} />
                </FormItem>
                <FormItem name="authType" label="鉴权方式" rules={[{ required: true, message: "请选择鉴权方式" }]}>
                  <Radio.Group>
                    <Radio value={0}>不鉴权</Radio>
                    <Radio value={1}>数字摘要认证</Radio>
                    <Radio value={2}>数字证书（双向）</Radio>
                    <Radio value={3}>数字证书（单向）</Radio>
                  </Radio.Group>
                </FormItem>
                <FormItem name="coordinateSystem" label="经纬度坐标系" rules={[{ required: true, message: "请选择经纬度坐标系" }]}>
                  <Radio.Group>
                    <Radio value={0}>wgs-84</Radio>
                    <Radio value={1}>gcj-02(高德)</Radio>
                  </Radio.Group>
                </FormItem>
                <Divider />
                <div className="details-edit-drawer-title">
                  <IconFont type="icon-caret-right-small" className="icon" />
                  媒体服务信息
                </div>
                <FormItem name="mediaTransportProtocol" label="媒体传输协议" rules={[{ required: true, message: "请选择媒体传输协议" }]}>
                  <Radio.Group>
                    <Radio value={0}>RTP</Radio>
                    <Radio value={1}>TCP_passive</Radio>
                    <Radio value={2}>TCP_active</Radio>
                  </Radio.Group>
                </FormItem>
                <div style={{ display: "flex", alignItems: "center" }}>
                  <FormItem
                    style={{ marginRight: 8, width: "100%" }}
                    name="mediaRtcpInterval"
                    label="rtcp发送间隔"
                    rules={[{ required: true, message: "请选择rtcp发送间隔" }]}
                  >
                    <InputNumber style={{ marginRight: 8, width: "100%" }} placeholder="请输入rtcp发送间隔" className="user-filter-btn vidc-Input" />
                  </FormItem>
                  <div style={{ marginTop: 5 }}>秒</div>
                </div>
              </>
            ) : null}
            {platformType === 2 ? (
              <>
                <FormItem name="appkey" label="appkey" rules={[{ required: true, message: "请输入appkey" }]}>
                  <Input placeholder="请输入appkey" className="user-filter-btn vidc-Input" allowClear={false} />
                </FormItem>
                <FormItem name="appID" label="appID" rules={[{ required: true, message: "请输入appID" }]}>
                  <Input placeholder="请输入appID" className="user-filter-btn vidc-Input" allowClear={false} />
                </FormItem>
                <FormItem name="url" label="url" rules={[{ required: true, message: "请输入url" }]}>
                  <Input placeholder="请输入url" className="user-filter-btn vidc-Input" allowClear={false} />
                </FormItem>
                <FormItem name="taskMaxCount" label="最大分析任务数" rules={[{ required: true, message: "请输入最大分析任务数" }]}>
                  <Input placeholder="请输入最大分析任务数" className="user-filter-btn vidc-Input" allowClear={false} />
                </FormItem>
                <FormItem name="kafkaServerUrl" label="kafkaServerUrl" rules={[{ required: true, message: "请输入kafkaServerUrl" }]}>
                  <Input placeholder="请输入kafkaServerUrl" className="user-filter-btn vidc-Input" allowClear={false} />
                </FormItem>
              </>
            ) : null}
            <Divider />
            <div className="details-edit-drawer-title">
              <IconFont type="icon-caret-right-small" className="icon" />
              访问配置
            </div>
            <FormItem name="platformAccessAddress" label="平台访问地址">
              <Input placeholder="请输入平台访问地址" className="user-filter-btn vidc-Input" allowClear={false} />
            </FormItem>
            <FormItem name="accessEnabled" label="是否可访问">
              <Switch size="small" disabled={!platformAccessAddress?.length} />
            </FormItem>
            {
              platformType === 2 ?  <Button
              loading={isLoading}
              type="primary"
              style={{ marginBottom: 24 }}
              onClick={() => {
                toggle();
                const {
                  devicePlatformId,
                  kafkaServerUrl,
                  url,
                  appID,
                  appkey,
                  mediaRtcpInterval,
                  mediaTransportProtocol,
                  coordinateSystem,
                  authType,
                  password,
                  code,
                  localRecordEnable,
                  sipPort,
                  ip,
                  devicePlatformSource,
                  algorithmManufacturer,
                  accessEnabled,
                  platformAccessAddress,
                  platformType,
                  taskMaxCount,
                  name,
                } = form.getFieldsValue();
                let params: any = {
                  accessEnabled,
                  platformAccessAddress,
                  name,
                  platformType,
                  devicePlatformSource,
                  devicePlatformId,
                  info: {
                    algorithmManufacturer,
                  },
                };
                if (platformType === 1) {
                  let subordinateDomain = {
                    ip,
                    sipPort,
                    code,
                    password,
                    authType,
                    coordinateSystem,
                    mediaTransportProtocol,
                    mediaRtcpInterval,
                    localRecordEnable: localRecordEnable?.length ? localRecordEnable.includes("localRecordEnable") : false,
                  };
                  params.subordinateDomain = subordinateDomain;
                }
                if (platformType === 2) {
                  const info = {
                    appkey,
                    appID,
                    url,
                    taskMaxCount,
                    kafkaServerUrl,
                  };
                  params.info = { ...params.info, ...info };
                }
                if (row.id) {
                  params = { ...row, ...params, subordinateDomain: { ...row.subordinateDomain, ...params.subordinateDomain } };
                }
                apPlatformInfoTest(params)
                  .then((res) => {
                    if (res.code === 0) {
                      return message.success(res.message);
                    }
                    message.warning(res.message);
                  })
                  .finally(() => {
                    toggle();
                  });
              }}
            >
              <IconFont type="icon-test" />
              测试
            </Button>:null
            }
           
          </>
        ) : null}
      </Form>
    </Drawer>
  );
};
