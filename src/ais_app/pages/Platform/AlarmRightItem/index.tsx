import { useMemo, useState, useEffect } from "react";
import { Switch, Card, Row, Col, Input, Checkbox, AutoComplete, Modal, message } from "antd";
import { IconFont } from "@cloud-app-dev/vidc";
import dir_file from "@src/assets/image/background.png";
import {
  apPlatformInfoQuery,
  apPlatformInfoSave,
  apPlatformInfoEnabled,
  apPlatformInfoDelete,
  apPlatformInfoInfo,
  apPlatformUpdate,
  apPlatformInfoCardList,
} from "@src/service/ais_app";
import Authority from "@src/components/Authority";
import "./index.less";

export default function AlarmRightItem({ item, run, toggleShow, setRow }: any) {
  const [isBatch, SetIsBatch] = useState(false);
  const [isChecked, SetIsChecked] = useState(false);
  const selectStatusClassname = ""; // 选中'AlarmRightItem-card-item--selected'
  return (
    // <div className="alarm-right-item">
    <Card className={`AlarmRightItem-card-dir-item ${isBatch ? "AlarmRightItem-card-item--batch" : ""} ${selectStatusClassname}`} cover={null}>
      <Card.Meta
        avatar={null}
        title={null}
        description={
          <>
            <Row align="middle" wrap={false} style={{ display: "flex", alignItems: "center", width: "100%", borderRadius: 2, overflow: "hidden" }}>
              {/* <img alt="" src={dir_file} className="AlarmRightItem-card-item__cover" /> */}
              <div className="AlarmRightItem-card-item__cover">{item.name}</div>
            </Row>
            <Row align="middle" wrap={false}>
              <div className="AlarmRightItem-card-item__filename">{item.name}</div>
            </Row>
            <Row
              style={{
                fontSize: 12,
              }}
              justify="space-between"
              align="middle"
            >
              <Col>
                <span
                  style={item.state === 1 ? {} : { color: "rgba(0, 0, 0, 0.90)", background: "#F3F3F3", borderColor: "#dcdcdc" }}
                  className="AlarmRightItem-card-dir-item-file-type"
                >
                  {item.state === 1 ? "正常" : "异常"}
                </span>
              </Col>
              <Checkbox checked={isChecked} className="AlarmRightItem-card-item__checkbox" />
            </Row>
          </>
        }
      />

      <Row className="AlarmRightItem-card-item__actions" gutter={[0, 16]}>
      <Authority code="30000317">
        <Col
          onClick={() => {
            apPlatformInfoInfo({
              id: item.id,
            }).then((res) => {
              if (res.code === 0) {
                setRow(res.data);
                toggleShow();
              } else {
                message.warning(res.message);
              }
            });
          }}
          className="AlarmRightItem-card-item__action-item"
          span={12}
        >
          <IconFont type="icon-browse" className="AlarmRightItem-card-item__action-icon" />
          <span style={{ marginLeft: 8 }} className="AlarmRightItem-card-item__action-label">
            详情配置
          </span>
        </Col>
        </Authority>
        <Authority code="30000318">
        <Col
          style={{ paddingLeft: 16 }}
          onClick={() => {
            Modal.confirm({
              title: `提示`,
              icon: <IconFont type="icon-tishi" style={{ color: "#FFA000" }} />,
              content: "是否删除该平台配置？",
              okText: "确定",
              onOk: () => {
                apPlatformInfoDelete({
                  id: item.id,
                }).then((res) => {
                  if (res.code === 0) {
                    message.success("删除成功");
                    run();
                  }
                },(err)=>{
                  message.warning(err?.data?.message);
                });
              },
            });
          }}
          className="AlarmRightItem-card-item__action-item"
          span={12}
        >
          <IconFont type="icon-delete" style={{ fontSize: 15 }} className="AlarmRightItem-card-item__action-icon" />
          <span style={{ marginLeft: 8 }} className="AlarmRightItem-card-item__action-label">
            删除
          </span>
        </Col>
      
        <Col className="AlarmRightItem-card-item__action-item" span={12} onClick={() => {}}>
          {/* <IconFont type="icon-shipinchakan_liebiaomoshi" className="AlarmRightItem-card-item__action-icon" /> */}
          <Switch
            size="small"
            value={item.enabled}
            onClick={() => {
              Modal.confirm({
                title: `提示`,
                icon: <IconFont type="icon-tishi" style={{ color: "#FFA000" }} />,
                content: `是否${item.enabled ? "关闭" : "开启"}该服务?`,
                okText: "确定",
                cancelText: "取消",
                onOk: () => {
                  apPlatformInfoEnabled({
                    id: item.id,
                    enabled: !item.enabled,
                  }).then((res) => {
                    if (res.code === 0) {
                      message.success(item.enabled ? "关闭成功" : "开启成功");
                      run();
                    }
                  },(err)=>{
                    message.warning(err?.data?.message);
                  });
                },
              });
            }}
          />
          <span style={{ marginLeft: 8 }} className="AlarmRightItem-card-item__action-label">
            {item.enabled ? "开启" : "关闭"}
          </span>
        </Col>
        </Authority>
        <Authority code="30000319">
          {item.accessEnabled ? (
            <Col
              onClick={() => {
                window.open(item?.platformAccessAddress);
              }}
              className="AlarmRightItem-card-item__action-item"
              span={12}
              style={{ paddingLeft: 16 }}
            >
              <IconFont type="icon-fangwen" style={{ fontSize: 15 }} className="AlarmRightItem-card-item__action-icon" />
              <span style={{ marginLeft: 8 }} className="AlarmRightItem-card-item__action-label">
                访问
              </span>
            </Col>
          ) : null}
        </Authority>
      </Row>
    </Card>
    // </div>
  );
}
