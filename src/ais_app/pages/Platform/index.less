.platform-wrapper {
  height: 100%;
  padding: 16px;
  .platform-content {
    width: calc(100%);
    height: 100%;
    background: var(--content-bg);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 24px;
    padding-right: 0px;
    overflow-y: hidden;
    overflow-y: auto;
    .platform-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #000;
      font-family: "PingFang SC";
      font-size: 24px;
      font-style: normal;
      font-weight: 600;
      line-height: 32px;
      margin-bottom: 24px;
      padding-right: 24px;
      .platform-time {
        color: rgba(0, 0, 0, 0.6);
        text-align: center;
        /* Body/Medium */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        display: flex;
        align-items: center;
      }
    }
    .platform-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 24px;
    }
    .platform-body{
      margin-top: 24px;
  }
  }
  .table-status-online {
    color: #2ba471;
    background: #e3f9e9;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    display: inline-flex;
    padding: 2px 8px;
    align-items: center;
    border-radius: 3px;
    border: 1px solid #2ba471;
  }
  .table-status-Offline {
    color: rgba(0, 0, 0, 0.9);
    background: #f3f3f3;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    display: inline-flex;
    padding: 2px 8px;
    align-items: center;
    border-radius: 3px;
    border: 1px solid #dcdcdc;
  }
}
