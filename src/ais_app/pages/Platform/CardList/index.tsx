import AlarmRightItem from "../AlarmRightItem";
import "./index.less";

export default ({dataList,run,toggleShow,setRow}:any) => {
  return (
    <div className="alarm_card_list">  
      {dataList?.map((item:any, index:number) => {
        return (
          <div className="alarm_card_item" key={index}>
            <div className="alarm_card_item_name">{item.name}</div>
            <div className="alarm_card_item_list">
            {item?.list?.map((item:any, index:number) => {
              return <AlarmRightItem setRow={setRow} toggleShow={toggleShow} key={index} item={item} run={run}></AlarmRightItem>;
            })}
            </div>
          </div>
        );
      })}
    </div>
  );
};
