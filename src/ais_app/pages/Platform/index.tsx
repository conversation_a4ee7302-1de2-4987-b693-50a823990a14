import { Select, Table, Switch, Button, Space, Form, message, Input, Modal } from "antd";
import { IconFont } from "@cloud-app-dev/vidc";
import type { ColumnsType } from "antd/es/table";
import { useEffect, useRef } from "react";
import { useToggle, useRequest, useSafeState, useThrottleEffect, useSize, useAntdTable } from "ahooks";
import CardList from "./CardList";
import DetailsDrawer from "./DetailsDrawer";
import {
  apPlatformInfoQuery,
  apPlatformInfoSave,
  apPlatformInfoEnabled,
  apPlatformInfoDelete,
  apPlatformInfoInfo,
  apPlatformUpdate,
  apPlatformInfoCardList,
} from "@src/service/ais_app";
import dayjs from "dayjs";
import Authority from "@src/components/Authority";
import "./index.less";
const FormItem = Form.Item;
const platformTypeList = [
  {
    label: "视频监控数据服务",
    value: 1,
  },
  {
    label: "算法服务",
    value: 2,
  },
  // {
  //   label: "存储服务",
  //   value: 3,
  // },
];
export default () => {
  const [form] = Form.useForm();
  const ref = useRef<HTMLDivElement>(null);
  const size: any = useSize(ref);
  const [tableHeight, setTableHeight] = useSafeState<number>(0);
  const [time, setTime] = useSafeState<any>("-");
  const [row, setRow] = useSafeState<any>({});
  const [isCardView, { toggle: toggleCardView }] = useToggle(false);
  const [isShow, { toggle: toggleShow }] = useToggle(false);
  const { data, run }: any = useRequest(() => {
    if (!isCardView) return Promise.resolve({});
    return apPlatformInfoCardList({
      ...form.getFieldsValue(),
    });
  });
  useEffect(() => {
    if (isCardView) {
      run();
    }
  }, [isCardView]);
  useThrottleEffect(
    () => {
      setTableHeight(size && size);
      // setTableHeight((size && size?.height - 128) || 0);
    },
    [size],
    {
      wait: 300,
    },
  );
  const getTableData = ({ current, pageSize }: { current: number; pageSize: number }, formData: Object): Promise<any> => {
    let params: any = {
      limit: pageSize,
      offset: (current - 1) * pageSize,
      isPagination: true,
      ...formData,
    };

    return apPlatformInfoQuery(params)
      .then((res) => {
        setTime(dayjs().format("YYYY-MM-DD HH:mm:ss"));
        return {
          list: res?.data?.list,
          total: res?.data?.totalCount,
        };
      })
      .catch((err) => {
        message.warning(err.data.message);
        return {
          list: [],
          total: 0,
        };
      });
  };
  const { tableProps, search } = useAntdTable(getTableData, {
    defaultPageSize: 10,
    form,
  });
  const searchForm = (
    <Form form={form} layout="inline" className="search-form">
      <FormItem name="keywords">
        <Input
          placeholder="请输入平台名称"
          className="user-filter-btn vidc-Input"
          onChange={isCardView ? run : search.submit}
          allowClear={false}
          style={{ width: "346px", borderRadius: "4px" }}
          suffix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ cursor: "pointer", fontSize: "12px" }} />}
        />
      </FormItem>
      <FormItem name="platformType" label="功能类型">
        <Select
          placeholder="请选择"
          style={{ width: "145px" }}
          className="user-filter-btn"
          onChange={isCardView ? run : search.submit}
          options={platformTypeList}
          allowClear={true}
        />
      </FormItem>
      <FormItem name="state" label="服务状态">
        <Select
          placeholder="请选择"
          style={{ width: "145px" }}
          className="user-filter-btn"
          onChange={isCardView ? run : search.submit}
          allowClear={false}
          options={[
            {
              label: "全部",
              value: null,
            },
            {
              label: "正常",
              value: 1,
            },

            {
              label: "异常",
              value: 0,
            },
          ]}
        />
      </FormItem>
    </Form>
  );
  const columns: ColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "key",
      key: "key",
      width: 88,
      fixed: "left",
      render: (_, __, index) => index + 1,
    },
    {
      title: "平台名称",
      ellipsis: true,
      dataIndex: "name",
      width: 134,
    },
    {
      title: "功能类型",
      ellipsis: true,
      dataIndex: "platformType",
      width: 144,
      render: (res) => {
        const platformTypeName = platformTypeList.find((item) => item.value === res)?.label;
        return platformTypeName || "-";
      },
    },
    {
      title: "服务状态",
      ellipsis: true,
      dataIndex: "state",
      width: 180,
      render: (res) => {
        return (
          <div
            className={res === 1 ? `table-status-online` : `table-status-Offline `}
            style={{ color: res === 1 ? "#2BA471" : "rgba(0, 0, 0, 0.90)", background: res === 1 ? "#E3F9E9" : "#F3F3F3" }}
          >
            {res === 1 ? "正常" : "异常"}
          </div>
        );
      },
    },
    {
      title: "接入通道数量",
      ellipsis: true,
      dataIndex: "channelNum",
      width: 135,
      render: (res) => res || "-",
    },
    {
      title: "内网IP/端口",
      ellipsis: true,
      width: 135,
      dataIndex: "ip",
      render: (res, item) => {
        const data = (item?.subordinateDomain?.ip || "") + (item?.subordinateDomain?.sipPort ? ":" + item?.subordinateDomain?.sipPort : "");
        return data || "-";
      },
    },
    {
      title: "更新时间",
      ellipsis: true,
      width: 135,
      dataIndex: "updatedTime",
      render: (res) => (res ? dayjs(res).format("YYYY-MM-DD HH:mm:ss") : "-"),
    },
    {
      title: "操作",
      width: 180,
      dataIndex: "description",
      fixed: "right",
      render: (text: string, item: any) => (
        <Space>
          <Authority code="30000318">
          <Switch
            size="small"
            checked={item.enabled}
            onClick={() => {
              Modal.confirm({
                title: `提示`,
                icon: <IconFont type="icon-tishi" style={{ color: "#FFA000" }} />,
                content: `是否${item.enabled ? "关闭" : "开启"}该服务?`,
                okText: "确定",
                cancelText: "取消",
                onOk: () => {
                  apPlatformInfoEnabled({
                    id: item.id,
                    enabled: !item.enabled,
                  }).then((res) => {
                    if (res.code === 0) {
                      message.success(item.enabled ? "关闭成功" : "开启成功");
                      search.submit();
                    }else{
                      message.warning(res?.message);
                    }
                  },(err:any)=>{
                    message.warning(err?.data?.message);
                  })
                },
              });
            }}
          />
          </Authority>
          <Authority code="30000317">
          <a
            onClick={() => {
              apPlatformInfoInfo({
                id: item.id,
              }).then((res) => {
                if (res.code === 0) {
                  setRow(res.data);
                  toggleShow();
                } else {
                  message.warning(res.message);
                }
              });
            }}
          >
            接入配置
          </a>
          </Authority>
          <Authority code="30000318">
          <a
            onClick={() => {
              Modal.confirm({
                title: `提示`,
                icon: <IconFont type="icon-tishi" style={{ color: "#FFA000" }} />,
                content: "是否删除该平台配置？",
                okText: "确定",
                onOk: () => {
                  apPlatformInfoDelete({
                    id: item.id,
                  }).then((res) => {
                    if (res.code === 0) {
                      message.success("删除成功");
                      search.submit();
                    }
                  },(err)=>{
                    message.warning(err?.data?.message);
                  });
                },
              });
            }}
          >
            删除
          </a>
          </Authority>
          <Authority code="30000319">
            {item.accessEnabled ? (
              <a
                onClick={() => {
                  window.open(item?.platformAccessAddress);
                }}
              >
                访问
              </a>
            ) : null}
          </Authority>
        </Space>
      ),
    },
  ];
  const onFinish = (formHandler: any) => {
    const reslovePath = row.id ? apPlatformUpdate : apPlatformInfoSave;
    formHandler.validateFields().then((val: any) => {
      const {
        devicePlatformId,
        taskMaxCount,
        url,
        appID,
        appkey,
        mediaRtcpInterval,
        mediaTransportProtocol,
        coordinateSystem,
        authType,
        password,
        code,
        localRecordEnable,
        sipPort,
        ip,
        devicePlatformSource,
        algorithmManufacturer,
        accessEnabled,
        platformAccessAddress,
        platformType,
        manufacturer,
        name,
        kafkaServerUrl,
      } = val;
      let params: any = {
        accessEnabled,
        platformAccessAddress,
        name,
        platformType,
        devicePlatformSource,
        devicePlatformId,
        info: {
          algorithmManufacturer,
          manufacturer,
        },
      };
      if (platformType === 1) {
        let subordinateDomain = {
          ip,
          sipPort,
          code,
          password,
          authType,
          coordinateSystem,
          mediaTransportProtocol,
          mediaRtcpInterval,
          localRecordEnable: localRecordEnable?.length ? localRecordEnable.includes("localRecordEnable") : false,
        };
        params.subordinateDomain = subordinateDomain;
      }
      if (platformType === 2) {
        const info = {
          appkey,
          appID,
          url,
          taskMaxCount,
          kafkaServerUrl,
        };
        params.info = { ...params.info, ...info };
      }
      if (row.id) {
        params = { ...row, ...params, subordinateDomain: { ...row.subordinateDomain, ...params.subordinateDomain } };
      }
      reslovePath(params)
        .then((res) => {
          if (res.code === 0) {
            message.success("操作成功");
            closeHandler();
            search.submit();
          } else {
            message.warning(res.message);
          }
        })
        .catch((err)=>{
          message.warning(err?.data?.message);
        });
    });
  };
  const closeHandler = () => {
    toggleShow();
    setRow({});
  };
  return (
    <div className="platform-wrapper">
      <div className="platform-content">
        <div className="platform-title">
          <div>平台管理</div>
          <div className="platform-time">
            <IconFont
              onClick={() => {
                search.submit();
                message.success("刷新成功");
              }}
              type="icon-refresh"
              style={{ fontSize: 16, cursor: "pointer", marginRight: 8 }}
            />
            最后更新：{time ? dayjs(time).format("YYYY-MM-DD HH:mm:ss") : "-"}
          </div>
        </div>
        <div className="platform-header">
          {searchForm}
          <Space>
          <Authority code="30000316">
            <Button
              onClick={() => {
                setRow({});
                toggleShow();
              }}
              type="primary"
            >
              <IconFont type="icon-add" />
              平台接入
            </Button>
            </Authority>
            <Button onClick={toggleCardView}>
              <IconFont type={!isCardView ? "icon-kapian" : "icon-liebiao-1"} />
            </Button>
          </Space>
        </div>
        <div className="platform-body" ref={ref}>
          {isCardView ? (
            <CardList setRow={setRow} toggleShow={toggleShow} dataList={data.data} run={run} />
          ) : (
            <Table
              // scroll={{ y: size?.height && size.height - 110 }}
              columns={columns}
              rowKey="id"
              {...tableProps}
              // dataSource={[1, 2, 3, 4, 5, 6, 7, 8, 9, 3, 3, 45,1,1]}
            />
          )}
        </div>
      </div>
      {isShow ? <DetailsDrawer platformTypeList={platformTypeList} row={row} toggleD={closeHandler} isShow={isShow} onFinish={onFinish} /> : null}
    </div>
  );
};
