import { useRef, useState, useEffect } from "react";
import { Divider, Input, Tooltip } from "antd";
import { useHistory } from '@cloud-app-dev/vidc';
import { SearchOutlined } from '@ant-design/icons'
import Temporarily from '../../components/Temporarily'

import AslgorithmicRuleDrawer from './AslgorithmicRuleDrawer'
import ModelManagementBg from '../../../assets/image/ModelManagement-bg.png'
import ModelManagementTab1 from '../../../assets/image/modelManagement-tab1.png'
import ModelManagementTab2 from '../../../assets/image/modelManagement-tab2.png'
import imglogin from '../../../assets/image/imglogin.png'

import useData from "./useData";

import "./index.less";

export default function AslgorithmicRule() {
    const histroy = useHistory()

    const {
        inputRef,
        sectKey,
        setSectKey,
        data,
        data1,
        anchor,
        setAnchor,
        aslgorithmicRuleDrawerOpen,
        setAslgorithmicRuleDrawerOpen,
        dataInfo,
        setDataInfo,
        getModelInfo,
    } = useData()

    const ModelItem = (key: any, type: any, item: any) => {
        return <div className='modelManagement-list-item' style={{ cursor: type === 0 ? 'initial' : 'pointer' }} key={key} onClick={() => {
            if (type === 1) {
                getModelInfo(item)
            }
        }}>
            {
                item.modelImageUrl ?
                    <img className="item-img" src={item.icon} />
                    :
                    <div className="modelManagement-list-img-box">
                        <img className="item-img" src={imglogin} />
                    </div>
            }
            <div className="item-body">
                <div className="item-body-title">
                    {
                        type === 0 ?
                            <div className="item-body-title-tag" style={{ width: 40 }}>基础</div>
                            :
                            <div className="item-body-title-tag1" style={{ width: 50 }}>自定义</div>
                    }
                    <div className="item-body-title-text"><Tooltip title={item.algorithmNoteName}>{item.algorithmNoteName}</Tooltip></div>
                </div>
                <div className="item-body-tip" ><Tooltip title={item.algorithmDescribe}>{item.algorithmDescribe}</Tooltip></div>
            </div>
            <div className="item-post">版本：{item.algorithmVersion || 0}</div>
        </div>
    }

    const scrollToAnchor = (anchorName: string) => {
        if (anchorName) {
            if (document.getElementsByClassName('modelManagement-body-right')) {
                let scrcollDom = document.getElementsByClassName('modelManagement-body-right')[0]
                if (scrcollDom.scrollHeight === scrcollDom.clientHeight) {
                    // @ts-ignore
                    let dom2 = document.getElementById(`part-2`).clientHeight
                } else {
                    // 找到锚点
                    let anchorElement = document.getElementById(anchorName);
                    // 如果对应id的锚点存在，就跳转到锚点
                    if (anchorElement) {
                        anchorElement.scrollIntoView({ block: 'start', behavior: 'smooth' });
                    }
                }
            }
        }
    };

    const judgeDistanceTop = () => {
        if (document.getElementsByClassName('modelManagement-body-right')) {
            let dom = document.getElementById(`part-1`);
            let dom2 = document.getElementById(`part-2`);
            let scrcollDom = document.getElementsByClassName('modelManagement-body-right')[0]
            let scrollTop = scrcollDom.scrollTop;
            if (scrcollDom.scrollHeight === scrcollDom.clientHeight) {

            } else {
                if (dom2) {
                    if (scrollTop < dom2['offsetTop'] - 70) {
                        setAnchor(0);
                    } else {
                        setAnchor(1);
                    }
                }
            }

        }
    };

    useEffect(() => {
        const timer = setInterval(() => {
            judgeDistanceTop();
        }, 100);
        return () => {
            clearInterval(timer);
        };
    }, []);

    return (
        <div className="modelManagement" style={{ background: `url(${ModelManagementBg})` }}>
            <div className="modelManagement-head">
                <div className="modelManagement-head-text">算法规则</div>
                {/* <div className="modelManagement-head-right">
                    <Input
                        allowClear
                        ref={inputRef}
                        value={sectKey}
                        onChange={e => setSectKey(e.target.value)}
                        // onPressEnter={(e: any) => onPressEnter(e.target.value)}
                        prefix={<SearchOutlined />}
                        width={240}
                        placeholder={`请输入模型名称搜索`}
                    />
                </div> */}
            </div>

            <div className="modelManagement-body">
                <div className="modelManagement-body-left">
                    <div
                        onClick={() => {
                            scrollToAnchor(`part-1`);
                        }}
                        className={`modelManagement-body-left-tab ${anchor === 0 ? 'modelManagement-body-left-tab-active' : ''}`}
                    >
                        <img src={ModelManagementTab1} />
                        <div className="body-left-tab-text">
                            基础算法
                            <div className="body-left-tab-text-num">{data.length}</div>
                        </div>
                    </div>
                    <div
                        onClick={() => {
                            scrollToAnchor(`part-2`);
                        }}
                        className={`modelManagement-body-left-tab ${anchor === 1 ? 'modelManagement-body-left-tab-active' : ''}`}
                    >
                        <img src={ModelManagementTab2} />
                        <div className="body-left-tab-text">
                            自定义模型规则
                            <div className="body-left-tab-text-num">{data1.length}</div>
                        </div>
                    </div>
                </div>
                <div className="modelManagement-body-right">
                    <div id="part-1" className="modelManagement-body-right-title">
                        <div className="right-title-text">基础算法</div>
                        {/* <div className="right-title-tip">在重庆电信视觉大模型基础上，针对真实场景，收集大量数据对大模型进行精调，识别更精准</div> */}
                    </div>
                    {data.length === 0 ?
                        <div style={{ marginBottom: 100, display: 'flex', justifyItems: 'center', width: '100%' }}>
                            <Temporarily />
                        </div>
                        :
                        <div className='modelManagement-body-flex'>
                            {
                                data.map((item: any, index: any) => (
                                    ModelItem(index, 0, item)
                                ))}
                        </div>
                    }

                    <Divider style={{ margin: 0 }} />
                    <div id="part-2" className="modelManagement-body-right-title">
                        <div className="right-title-text">自定义模型规则</div>
                        {/* <div className="right-title-tip">基于视觉大模型OVD通用能力，零样本创建目标检测模型</div> */}
                    </div>
                    {!sectKey && data1.length === 0 ?
                        <div style={{ marginBottom: 100, display: 'flex', justifyItems: 'center', width: '100%' }}>
                            <Temporarily />
                        </div>
                        :
                        <div className='modelManagement-body-flex'>
                            {
                                // sectKey ?
                                //     null :
                                //     <div
                                //         onClick={() => histroy.push(`/aisApp/modelManagementDetails`, { type: 2, ctype: 'custom' })}
                                //         className="modelManagement-list-item list-item-add"
                                //     >
                                //         <img src={modelManagementAdd} />
                                //         <div className="item-add-title">新增目标检测模型</div>
                                //         <div className="item-add-tip">适用于单/多目标检测任务</div>
                                //     </div>
                            }
                            {data1.map((item: any, index: any) => (
                                ModelItem(index, 1, item)
                            ))}
                        </div>
                    }

                    <div style={{ height: 600 }}></div>
                </div>
            </div>

            {
                aslgorithmicRuleDrawerOpen &&
                <AslgorithmicRuleDrawer
                    open={aslgorithmicRuleDrawerOpen}
                    setOpen={setAslgorithmicRuleDrawerOpen}
                    dataInfo={dataInfo}
                    setDataInfo={setDataInfo}
                />
            }
        </div>
    );
}
