import { useRef, useState, useEffect } from 'react'
import { message } from 'antd'
import { treeHelper } from '@cloud-app-dev/vidc';
import Service from "@src/service/system";

const useData = () => {
    const inputRef: any = useRef(null)
    const [aslgorithmicRuleDrawerOpen, setAslgorithmicRuleDrawerOpen] = useState(false)
    const [dataInfo, setDataInfo]: any = useState({})
    const [data, setData] = useState([])
    const [data1, setData1] = useState([])
    const [sectKey, setSectKey]: any = useState(null)
    const [anchor, setAnchor] = useState(0);

    useEffect(() => {
        getModelList()
    }, [sectKey])

    const getModelList = () => {
        Service.device.queryAlgorithmList({ algorithmManufacturer: 'basic', limit: 1000 }).then((res: any) => {
            setData(res.data.list)
        })
        Service.device.queryAlgorithmList({ algorithmManufacturer: 'custom', limit: 1000 }).then((res: any) => {
            setData1(res.data.list)
        })
    }

    const getModelInfo = (item: any) => {
        Service.device.algorithmRuleInfo({ algorithmId: item.id }).then((res: any) => {
            const treeList = res.data.list.map((item: any) => ({ ...item }));
            const treeData = treeHelper.computTreeList(treeList);
            setDataInfo({ ...res.data, treeData: treeData, ...item })
            setAslgorithmicRuleDrawerOpen(true)
        }).catch((err) => {
            message.warning(err.data.message)
        })
    }

    return {
        inputRef,
        sectKey,
        setSectKey,
        data,
        setData,
        data1,
        setData1,
        anchor,
        setAnchor,
        aslgorithmicRuleDrawerOpen,
        setAslgorithmicRuleDrawerOpen,
        dataInfo,
        setDataInfo,
        getModelInfo,
    };
};

export default useData;
