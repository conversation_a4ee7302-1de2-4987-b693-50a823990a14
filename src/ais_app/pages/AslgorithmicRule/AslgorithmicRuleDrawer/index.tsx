import { useRef, useEffect } from "react";
import { Drawer, Input, Form, Button, Checkbox } from "antd";
import Temporarily from '../../../components/Temporarily'
import "./index.less";

export default function AslgorithmicRuleDrawer({ open, setOpen, dataInfo, setDataInfo }: any) {
    const [form] = Form.useForm()
    const subBottomRef: any = useRef(null)

    useEffect(() => {
        if (dataInfo.id) {
            form.setFieldsValue({ ...dataInfo, tagFontColor: `#${dataInfo.tagFontColor}`, tagBgColor: `#${dataInfo.tagBgColor}` })
        } else {
            form.setFieldsValue({ tagFontColor: '#165DFF', tagBgColor: '#BEDAFF' })
        }
    }, [dataInfo])

    const onCancel = () => {
        setDataInfo({})
        setOpen(false)
    }

    const handleSubmit = (value: any) => {

    }

    const itemStyle = (item: any, index: any) => {
        if (item.dataSource === 1) {
            return <div>{item.configName}: {item.configValue}</div>
        }
        if (item.dataSource === 2) {
            return <div>{item.configName}: {item.configValue}</div>
        }
        if (item.dataSource === 3) {
            return <div>{item.configName}</div>
        }
        if (item.dataSource === 4) {
            return <div>{item.configName}: {item.configValue}</div>
        }
    }

    console.log('asdnmjajsjdlkjaklsdjklasda', dataInfo)
    return (
        <>
            <Drawer
                width={400}
                title={dataInfo.algorithmNoteName}
                placement={'right'}
                closable={false}
                onClose={onCancel}
                open={open}
                keyboard={false}
                footer={null}
            // <div className="AslgorithmicRuleDrawer-footer">
            //     <div onClick={() => onCancel()} className="AslgorithmicRuleDrawer-clear">取消</div>
            //     <div className="AslgorithmicRuleDrawer-ok" onClick={() => subBottomRef.current.click()}>保存</div>
            // </div>}
            >

                <Form
                    form={form}
                    name='loginForm'
                    onFinish={handleSubmit}
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                    className='AslgorithmicRuleDrawer-layout-form'
                >
                    {
                        dataInfo.treeData.length === 0 ?
                            <div style={{ marginBottom: 100, display: 'flex', justifyItems: 'center', width: '100%' }}>
                                <Temporarily />
                            </div>
                            :
                            dataInfo.treeData.length > 0 && dataInfo.treeData[0].children.map((item: any, index: any) => (
                                <div key={index} style={{ marginBottom: 16 }}>
                                    <div style={{ fontSize: 16, marginBottom: 8, color: 'black', fontWeight: '500' }}>{item.configName}</div>
                                    {
                                        item.configName === '算法规则' ?
                                            <div className="AslgorithmicRuleDrawer-body-flex">
                                                {
                                                    item.children.length > 0 && item.children.map((item1: any, index1: any) => (
                                                        <div className="AslgorithmicRuleDrawer-body-flex-item" key={index1}>
                                                            <div style={{ paddingLeft: 16 }} className="flex-item-title">
                                                                {itemStyle(item1, index1)}
                                                            </div>
                                                            {
                                                                item1.children.length > 0 && item1.children.map((item2: any, index2: any) => (
                                                                    <div style={{ paddingLeft: 32, marginTop: 8 }} className="flex-item-title" key={index2}>
                                                                        {itemStyle(item2, index2)}
                                                                    </div>
                                                                ))
                                                            }
                                                        </div>
                                                    ))
                                                }
                                            </div>
                                            :
                                            <div>
                                                {
                                                    item.children.length > 0 && item.children.map((item1: any, index1: any) => (
                                                        <div key={index1}>
                                                            <div style={{ paddingLeft: 16, marginBottom: 8 }} className="flex-item-title">
                                                                {itemStyle(item1, index1)}
                                                            </div>
                                                        </div>
                                                    ))
                                                }
                                            </div>
                                    }
                                </div>
                            ))
                    }

                    <Form.Item style={{ display: 'none' }}>
                        <Button ref={subBottomRef} htmlType='submit'>
                            提交
                        </Button>
                    </Form.Item>
                </Form>

            </Drawer>
        </>
    );
}
