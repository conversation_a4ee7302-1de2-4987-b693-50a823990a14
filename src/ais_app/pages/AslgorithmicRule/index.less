.modelManagement {
    width: 100%;
    height: 100%;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    position: relative;
    padding: 16px;
    padding-bottom: 0px;
    padding-top: 64px;

    .modelManagement-head {
        width: 100%;
        position: absolute;
        top: 0px;
        left: 0px;
        display: flex;
        align-items: center;
        padding: 16px 24px;


        .modelManagement-head-text {
            flex: 1;
            color: #000;
            font-size: 24px;
            font-weight: 600;
            line-height: 32px;
        }

        .modelManagement-head-right {
            width: 240px;
        }
    }

    .modelManagement-body {
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.80);
        padding: 16px;
        padding-top: 0px;
        padding-right: 0px;
        height: 100%;
        display: flex;

        .modelManagement-body-left {
            flex: 0 0 120px;
            padding-top: 16px;
            width: 120px;
            margin-right: 32px;

            .modelManagement-body-left-tab {
                display: block;
                height: 120px;
                width: 120px;
                padding: 16px 0px;
                text-align: center;
                cursor: pointer;

                img {
                    width: 64px;
                    height: 64px;
                    margin-bottom: 4px;
                }

                .body-left-tab-text {
                    color: rgba(0, 0, 0, 0.60);
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 22px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .body-left-tab-text-num {
                    border-radius: 3px;
                    background: #F3F3F3;
                    color: rgba(0, 0, 0, 0.60);
                    font-size: 12px;
                    font-weight: 500;
                    line-height: 20px;
                    margin-left: 4px;
                    height: 16px;
                    padding: 0px 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            .modelManagement-body-left-tab-active {
                border-left: 2px solid #165DFF;
                background: linear-gradient(90deg, #E8F3FF 0%, #F7FBFF 100%);

                .body-left-tab-text {
                    color: #165DFF;
                }

                .body-left-tab-text-num {
                    background: #165DFF;
                    color: #FAFEFF;
                }
            }
        }

        .modelManagement-body-right {
            flex: 1;
            height: calc(100% - 0px);
            padding-right: 16px;
            overflow: auto;

            .modelManagement-body-right-title {
                display: flex;
                align-items: center;
                padding: 16px 0px;

                .right-title-text {
                    color: rgba(0, 0, 0, 0.90);
                    font-size: 20px;
                    font-weight: 600;
                    line-height: 28px;
                    margin-right: 16px;
                }

                .right-title-tip {
                    color: rgba(0, 0, 0, 0.60);
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20px;
                }
            }

            .modelManagement-body-flex {
                display: flex;
                flex: auto;
                flex-wrap: wrap;

                .list-item-add {
                    text-align: center;
                    padding-top: 30px;
                    .item-add-title {
                        color: rgba(0, 0, 0, 0.90);
                        font-size: 16px;
                        font-weight: 600;
                        line-height: 24px;
                        margin-top: 8px;
                        margin-bottom: 8px;
                    }
                    .item-add-tip {
                        color: rgba(0, 0, 0, 0.60);
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 20px;
                    }
                }

                .modelManagement-list-item {
                    position: relative;
                    cursor: pointer;
                    flex-shrink: 0;
                    border-radius: 8px;
                    border: 1px solid #DCDCDC;
                    position: relative;
                    margin-bottom: 16px;
                    margin-right: 16px;
                    height: 213px;
                    text-align: center;

                    @media screen and (min-width: 1700px){
                        width: calc(20% - 13px);

                        &:nth-child(5n+5) {
                            margin-right: 0px;
                        }

                    }

                    @media screen and (min-width: 1366px) and (max-width: 1700px) {
                        width: calc(25% - 12px);

                        &:nth-child(4n+4) {
                            margin-right: 0px;
                        }

                    }

                    @media screen and (max-width: 1366px) {
                        width: calc(33.33333% - 12px);

                        &:nth-child(3n+3) {
                            margin-right: 0px;
                        }

                    }

                    .modelManagement-list-img-box {
                        height: 145px;
                        background-color: #F3F3F3;
                        border-top-left-radius: 8px;
                        border-top-right-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        .item-img {
                            width: 68px;
                            height: 68px;
                        }
                    }

                    .item-img {
                        margin: auto;
                        height: 145px;
                        width: 100%;
                        border-top-left-radius: 8px;
                        border-top-right-radius: 8px;
                    }

                    .item-body {
                        text-align: left;
                        padding: 8px 16px;

                        .item-body-title {
                            display: flex;
                            align-items: center;

                            .item-body-title-text {
                                color: #000;
                                font-size: 16px;
                                font-style: normal;
                                font-weight: 600;
                                line-height: 24px;
                                margin-left: 8px;

                                overflow: hidden;
                                text-overflow: ellipsis;
                                display: -webkit-box;
                                -webkit-box-orient: vertical;
                                -webkit-line-clamp: 1;
                            }

                            .item-body-title-tag {
                                height: 20px;
                                padding: 0px 4px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                border-radius: 3px;
                                background-color: #E8F3FF;
                                color: #165DFF;
                                font-size: 12px;
                                font-weight: 400;
                                line-height: 20px;
                            }

                            .item-body-title-tag1 {
                                height: 20px;
                                padding: 0px 4px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                border-radius: 3px;
                                background-color: #FFF0ED;
                                color: #D54941;
                                font-size: 12px;
                                font-weight: 400;
                                line-height: 20px;
                            }
                        }

                        .item-body-tip {
                            margin-top: 4px;
                            color: rgba(0, 0, 0, 0.90);
                            font-size: 12px;
                            font-weight: 400;
                            line-height: 20px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 1;
                        }
                    }

                    .item-post {
                        position: absolute;
                        top: 120px;
                        left: 16px;
                        border-radius: 3px;
                        background: rgba(0, 0, 0, 0.60);
                        padding: 0px 6px;
                        height: 20px;
                        display: flex;
                        align-items: center;

                        color: #FFF;
                        font-size: 12px;
                        font-weight: 600;
                        line-height: 20px;
                    }

                    .item-post::before {
                        content: '•';
                        color: #fff;
                        margin-right: 8px;
                    }
                }
            }
        }
    }

}