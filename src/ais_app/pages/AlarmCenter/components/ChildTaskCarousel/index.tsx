import { Carousel } from 'antd';
import { useMemo, useRef } from 'react';
import { CarouselRef } from 'antd/lib/carousel';
import { IconFont } from '@cloud-app-dev/vidc';
import { chunk } from 'lodash-es';
import { ChildTaskCarouselType } from './index.d';
import { useSafeState, useSize } from 'ahooks';
import './index.less';

const Chart2bCarouselCom = ({ list, currentItem, render }: ChildTaskCarouselType) => {
  const CarouselRef = useRef<CarouselRef>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const size = useSize(containerRef);
  const [currentPage, setCurrentPage] = useSafeState(0);
  const prev = () => {
    setCurrentPage(old=>old-1)
    CarouselRef.current?.prev();
  };
  const next = () => {
    setCurrentPage(old=>old+1)
    CarouselRef.current?.next();
  };

  const { colNum, currentList } = useMemo(() => {
    if (!size) {
      return { colNum: 0, currentList: [] };
    }
    const { width } = size;
    const containerWidth = width;
    const itemWidth = 176;
    let colNum = 0;
    let rowNum = 1;
    while ((colNum + 1) * itemWidth <= containerWidth) {
      colNum++;
    }
    !colNum && (colNum = 1);
    !rowNum && (rowNum = 1);
    if (colNum && rowNum) {
      return {
        colNum,
        currentList: chunk(list, colNum * rowNum).map((o) => chunk(o, colNum)),
      };
    }
    return { colNum: 0, currentList: [] };
  }, [list, size]);
  return (
    <div className="Chart2bCarouselalarm" ref={containerRef}>
      { currentPage > 0 && <IconFont type="icon-gaojingzhongxin_shangyige" className="prev" onClick={prev} />}
      <div className="container">
        <Carousel ref={CarouselRef} dots={false}>
          {currentList.map((panel, panelIndex) => {
            return (
              <div className="panel" style={{ height: `${containerRef.current?.offsetHeight || 0}px` }} key={panelIndex}>
                {panel.map((rows, rowIndex) => {
                  return (
                    <div key={rowIndex} style={{ display: 'grid', gridTemplateColumns: `repeat(${colNum},1fr)`, gap: '16px' }}>
                      {rows.map((item, itemIndex) => {
                        return (
                          <div key={itemIndex}>
                            {render(
                              item,
                              list?.findIndex((v) => v?.storageImageUrl == item?.storageImageUrl)
                            )}
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </Carousel>
      </div>
      { currentPage < currentList.length - 1 && <IconFont style={{ transform: 'rotate(180deg)' }} type="icon-gaojingzhongxin_shangyige" className="next" onClick={next} />}
    </div>
  );
};
const ChildTaskCarousel = ({ currentItem, list, render }: ChildTaskCarouselType) => {
  return <Chart2bCarouselCom list={list} currentItem={currentItem} render={render} />;
};
export default ChildTaskCarousel;
