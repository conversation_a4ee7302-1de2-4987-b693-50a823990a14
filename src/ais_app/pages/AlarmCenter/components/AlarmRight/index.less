.AlarmRight {
  width: 100%;
  height: 100%;
  padding: 20px 0px;
  display: flex;
  flex-direction: column;
  .header-title {
    color: #000;
    font-family: "PingFang SC";
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 32px;
    padding-left: 24px;
    margin-bottom: 15px;
  }
  .alarm-right-item_children-left {
    // margin-left: 0px;
    // margin-right: -24px;
  width: 20px;
  background: #e8f3ff;
  display: flex;
  height: 217px;
  padding: 4px 2px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  color: #165dff;
  font-family: "PingFang SC";
  font-size: 12px;
  font-weight: 600;
  line-height: 20px;
  cursor: pointer;
}
  .header {
    height: 32px;
    padding: 0px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .total {
      color: rgba(0, 0, 0, 0.6);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      span {
        color: var(--secondary2);
      }
    }
    .btn {
      .ant-btn {
        background-color: transparent;
        border: 1px solid var(--primary-light);
        color: var(--primary-light);
        border-radius: 3px;
      }
    }
  }
  .content {
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    padding: 0px 24px;
    padding-right: 0px;
    padding-bottom: 0px;
    flex: 1;
    margin-top: 16px;
    display: flex;
    flex-flow: wrap;
    .anticon {
      font-size: 14px;
      position: relative;
      // top: 2px;
    }
    .ant-grid-list {
      padding: 0px 24px;
    }
    .item:hover {
      background: #E8F3FF;
      border: 2px solid var(--secondary2);
      padding: 11px 7px;
    }
    .item {
      width: 100%;
      height: 100%;
      border: 1px solid rgba(255, 255, 255, 0.35);
      border-radius: 6px;
      color: rgba(0, 0, 0, 0.6);
      padding: 12px 8px;
      display: flex;
      position: relative;
      flex-direction: column;
      cursor: pointer;
      .handle-img {
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 10;
      }
      .item-img {
        flex: 1;
        width: 100%;
        height: 149px;
        margin-bottom: 8px;
        img {
          width: 100%;
          background-color: transparent;
        }
      }
      .item-name {
        font-size: var(--fs-small);
        .anticon {
          margin-right: 8px;
        }
      }
      .item-tag {
        width: max-content;
        padding: 0 12px;
        height: 24px;
        border: 1px solid var(--secondary2);
        color: var(--secondary2);
        border-radius: 2px;
        font-size: var(--fs-small);
        font-weight: 600;
        text-align: center;
        margin-bottom: 4px;
      }
      .item-adress {
        display: flex;
        .over {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        font-size: var(--fs-small);
        .anticon {
          margin-right: 8px;
        }
      }
    }
  }
}
.AlarmRight_times_popover {
  padding: 4px;
  width: 233px;
  height: 70vh;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  .AlarmRight_times_popover_list {
    .AlarmRight_times_popover_item {
      display: flex;
      align-items: center;
      padding: 8px;
      border-radius: 4px;
      color: rgba(0, 0, 0, 0.6);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-bottom: 8px;
      cursor: pointer;
      &:hover{
        color: rgba(0, 0, 0, 0.90);
        background: #E8F3FF;

      }
      &:hover .AlarmRight_times_popover_item_icon{
        background: var(--primary);
      }
      .AlarmRight_times_popover_item_icon {
        width: 6px;
        height: 6px;
        background: #dcdcdc;
        border-radius: 50%;
        margin-right: 8px;
      }
    }
    .AlarmRight_times_popover_item_active{
      color: rgba(0, 0, 0, 0.90) !important;
      background: #E8F3FF !important;
      .AlarmRight_times_popover_item_icon{
        background: var(--primary) !important;
      }
    }
  }
}
