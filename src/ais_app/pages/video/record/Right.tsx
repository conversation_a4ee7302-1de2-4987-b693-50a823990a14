import { RefModal, useHistory, useSimpleState } from "@cloud-app-dev/vidc";
import { RecordItem } from "@cloud-app-dev/vidc/es/ScreenPlayer/interface";
import { tagAToDownload } from "@src/ais_app/utils";
import { useContext, useEffect, useMemo, useRef } from "react";
import { VideoContext, VideoItem } from "../Context";
import { IRefModalMethodsProps } from "@cloud-app-dev/vidc/es/RefModal";
import { message, Modal } from "antd";
import { ISegmentType } from "@cloud-app-dev/vidc/es/Player/player";
import dayjs from "dayjs";
import { useMemoizedFn } from "ahooks";
import { queryLocalVideo } from "@src/service/ais_app";
import DownloadTimePick from "./DownloadTimePick";
import { snapshot as snapshotAction } from "../tools";
import { useLocation } from "react-router-dom";
import useScreenMove from "../useScreenMove";
import { queryRecord } from "./utils";
import { orderBy } from "lodash-es";
import { cache, SocketEmitter } from "@cloud-app-dev/vidc";
import RecordPlayer from "../../../components/ScreenPlayer/Record";

function Right() {
  const domRef = useRef<HTMLDivElement>(null);
  const [state, updateState] = useSimpleState({ seekLoading: false });
  const { list, clear, set, params, updateParams } = useContext(VideoContext);
  SocketEmitter.on("clearAllPlayerRecord", clear);
  const currentWinItem = useMemo(() => list[params.screenIndex as number], [list, params.screenIndex]);
  const history = useHistory();
  const ref = useRef<IRefModalMethodsProps>(null);
  const ref2 = useRef<IRefModalMethodsProps>(null);

  const snapshot = useMemoizedFn((url) => {
    const item = list[params.screenIndex as number] || ({} as VideoItem);
    const downloadName = `${item.name}_${dayjs().format("YYYY/MM/DD HH:mm:ss")}`;
    snapshotAction({ url, modal: Modal, downloadName, cid: item.cid as string });
  });
  // 录像下载处理
  const downloadRecord = (segments: ISegmentType[]) => {
    const name = currentWinItem?.name;
    const data = {} as { start: number; end: number };
    data.end = segments[segments.length - 1].endTime;
    data.start = data.end - 30 * 60 * 1000;

    const onChange = (options: { start?: number; end?: number }) => {
      if (options.start) {
        data.start = options.start;
      }
      if (options.end) {
        data.end = options.end;
      }
    };
    Modal?.confirm({
      title: "下载录像",
      content: <DownloadTimePick data={data} onChange={onChange} />,
      width: 502,
      icon: null,
      centered: true,
      onOk() {
        if (data.end - data.start > 30 * 60 * 1000) {
          return message.warning("录像片段最大不超过30分钟！");
        }
        const begin = Math.round(data.start / 1000);
        const end = Math.round(data.end / 1000);
        const item = list[params.screenIndex] || ({} as VideoItem);
        const { cid } = item || {};
        if (!cid) {
          return message.warning("当前无选中设备！");
        }
        queryLocalVideo({ cid, beginTime: begin, endTime: end })
          .then((res) => res.data.map((v: any) => v.play_url) as string[])
          .then((urls) => {
            const token = cache.getCache("token", "session");
            // const baseUrl = "/api/dvia-device-server/baseDevice/video/v1/downloadByUrl";
            const baseUrl = "/api/amc-user-server/common/downloadByUrl";
            if (urls.length === 1) {
              const title = `${name}_${dayjs(data.start).format("YYYYMMDDTHHmmss")}_${dayjs(data.end).format("YYYYMMDDTHHmmss")}.flv`;
              const url = `${baseUrl}?url=${urls[0]}&authorization=${token}`;
              tagAToDownload({ url, title });
            } else {
              urls.forEach((urlStr, index) => {
                const title = `${name}_${dayjs(data.start).format("YYYYMMDDTHHmmss")}_${dayjs(data.end).format("YYYYMMDDTHHmmss")}_片段${
                  index + 1
                }.flv`;
                const url = `${baseUrl}?url=${urlStr}&authorization=${token}`;
                tagAToDownload({ url, title });
              });
            }
            ref.current?.close();
          });
      },
    });
  };

  const getLocalRecordUrl = useMemoizedFn(({ begin, end, screenIndex }: { begin: number; end: number; screenIndex: number }) => {
    const item = list[screenIndex] || ({} as VideoItem);
    const { cid } = item || {};
    if (!cid) {
      return Promise.reject("当前窗口无选中设备！");
    }
    return queryLocalVideo({ cid, beginTime: begin, endTime: end }).then((res) => res.data[0].play_url as string);
  });

  const onTimeLineChange = useMemoizedFn((time: number) => {
    const idx = params.screenIndex;
    const item = list[idx];
    if (!item) {
      return;
    }
    item.date = time;
    item.loading = true;
    updateState({ seekLoading: true });
    queryRecord(item)
      .then((segments: any) => {
        item.loading = false;
        item.segments = orderBy([...segments, ...(item.segments ?? [])], "beginTime", "asc");
      })
      .finally(() => updateState({ seekLoading: false }));
  });

  // 触发外部传参开流
  const location = useLocation();
  useEffect(() => {
    if (location.state && location.state.cid && location.state.time) {
      const item = { name: "", type: "flv", cid: location.state.cid, date: location.state.time, recordType: 2, loading: true } as VideoItem;
      set(item, 0);
      queryRecord(item)
        .then((segments: any) => {
          item.loading = false;
          item.segments = segments;
          set(item, 0);
        })
        .catch((e: any) => {
          console.error(e);
          set(undefined, 0);
        });
      history.replace("/aisApp/videoReplay", {});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location?.state]);

  useScreenMove(domRef);
  return (
    <div style={{ width: "100%", height: "100%" }} ref={domRef}>
      <RefModal ref={ref} />
      <RefModal ref={ref2} footer={false} />
      <RecordPlayer
        snapshot={snapshot}
        screenNum={params.screenNum as any}
        defaultSelectIndex={params.screenIndex}
        onClose={() => set(undefined, params.screenIndex)}
        onCloseAll={clear}
        list={list as RecordItem[]}
        download={downloadRecord}
        defaultScreen={4}
        onIndexChange={(i) => updateParams({ screenIndex: i })}
        screenChange={(i) => updateParams({ screenNum: i as any })}
        getLocalRecordUrl={getLocalRecordUrl}
        seekLoading={state.seekLoading}
        onTimeLineChange={onTimeLineChange}
      />
    </div>
  );
}

export default Right;
