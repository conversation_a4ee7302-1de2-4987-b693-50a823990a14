import { useSimpleState } from "@cloud-app-dev/vidc";
import { DatePicker as TDatePicker } from "antd";
import dayjs from "dayjs";

const DatePicker: any = TDatePicker;
interface IDownloadTimePickProps {
  data: { start: number; end: number };
  onChange: (options: { start?: number; end?: number }) => void;
}

function DownloadTimePick({ data, onChange }: IDownloadTimePickProps) {
  const [state, stateChange] = useSimpleState<{ start: number; end: number }>({ start: data.start, end: data.end });
  return (
    <div style={{ margin: "20px 0px", display:'flex',alignItems:'center' }}>
      <div style={{marginRight:8,whiteSpace: 'nowrap'}}>时间段选择</div>
      <DatePicker.RangePicker
        format="YYYY-MM-DD HH:mm:ss"
        style={{ width: "100%" }}
        placeholder={["开始时间", "结束时间"]}
        value={[dayjs(state.start), dayjs(state.end)]}
        onChange={(v: any,s:any) => {
          const start = v[0]?.valueOf();
          const end = v[1]?.valueOf();
          stateChange({ start, end });
          onChange({ start, end });
        }}
        disabledDate={(d: any) => {
          return d.valueOf() > Date.now();
        }}
        showTime={{ defaultValue: dayjs("00:00:00", "HH:mm:ss") }}
      />
      {/* <DatePicker
        style={{ width: '100%', marginTop: '12px' }}
        format="YYYY-MM-DD HH:mm:ss"
        placeholder="结束时间"
        value={dayjs(state.end)}
        disabledDate={(d:any) => d.valueOf() > Date.now() || d.valueOf() < state.start}
        onChange={(v:any) => {
          const end = v?.valueOf();
          stateChange({ end });
          onChange({ end });
        }}
        showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
      /> */}
    </div>
  );
}

export default DownloadTimePick;
