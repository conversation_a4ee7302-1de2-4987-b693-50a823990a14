import { ISegmentType } from '@cloud-app-dev/vidc/es/Player/player';
import { queryLocalVideo } from '@src/service/ais_app';
import { message } from 'antd';
import dayjs from 'dayjs';

export const completionSegments = (start: number, end: number, segments: ISegmentType[]): ISegmentType[] => {
  const arr = [] as ISegmentType[];
  if (segments.length > 0 && start < +segments[0].beginTime) {
    arr.push({ beginTime: start, endTime: +segments[0].beginTime });
  }
  segments.reduce((prev, current, idx) => {
    if (arr.length === 0 && idx === 0) {
      prev.push(current);
    } else if (idx > 0 && segments[idx - 1].endTime !== current.beginTime) {
      prev.push({ beginTime: segments[idx - 1].endTime, endTime: current.beginTime }, current);
    } else {
      prev.push(current);
    }

    return prev;
  }, arr);

  if (end > +segments[segments.length - 1].endTime) {
    arr.push({ beginTime: segments[segments.length - 1].endTime, endTime: end });
  }
  return arr;
};

export function queryRecord({ cid, date }: any) {
  const m = dayjs(date);
  const beginTime = m.set("hours",0).set("minutes",0).set("seconds",0).unix();
  const endTime = m.set("hours",23).set("minutes",59).set("seconds",59).unix();

  return queryLocalVideo({ cid, beginTime, endTime })
    .then((res:any) => res.data.map((v: any) => ({ url: v.play_url, beginTime: v.begin * 1000, endTime: v.end * 1000 })))
    .then((res:any) => completionSegments(beginTime * 1000, endTime * 1000, res))
    .catch((e:any) => {
      console.error(e);
      message.warning('未获取到录像片段');
      return Promise.reject(e);
    });
}

