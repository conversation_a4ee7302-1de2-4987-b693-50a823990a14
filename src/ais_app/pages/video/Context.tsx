import { RecordItem } from '@cloud-app-dev/vidc/es/ScreenPlayer/interface';
import { useMemoizedFn } from 'ahooks';
import { DataNode } from 'antd/lib/tree';
import React, { useEffect, useMemo, useState } from 'react';

export type ListItem = {
  cid: string;
  accessType: string;
  lat: string;
  lng: string;
  localRecord: number;
  name: string;
  ptz: number;
  state: number;
  storageVideo: number;
  type: number;
};

export interface ITreeItem extends DataNode {
  id: string;
  groupName: string;
  onlineCount: number;
  totalCount: number;
  parentId: string;
  children?: ITreeItem[];
  name?: JSX.Element;
}

export interface VideoItem extends RecordItem {
  name: string;
  ptz?: boolean;
}

export interface IParams {
  currentTreeItem?: ITreeItem;
  screenNum: number;
  screenIndex: number;
  selectDeviceItem?: ListItem; //录像模式下选中设备
  loopTreeId?: string; //临时选中轮巡ID
  loopWinsIndex?: number[];
  loopInterval?: number;
  loopUnit?: number;
  loopId?: string; //真是轮巡树ID，用于获取设备
  recordDateVisible?: boolean;
  deviceCount: {
    onlineCount: number;
    totalCount: number;
  };
}

export interface IVideoContextProps {
  params: IParams;
  list: (VideoItem | undefined)[];
  selectItem: VideoItem;
  set: (item?: VideoItem, index?: number) => void;
  clear: () => void;
  get: (cid: string) => any;
  updateParams: (data: Partial<IParams>) => void;
  mutSet: (data: { [key: string]: VideoItem | undefined }) => void;
}

export const VideoContext = React.createContext<IVideoContextProps>({} as IVideoContextProps);

export function Provider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState({
    list: [] as VideoItem[],
    params: { screenIndex: 0, screenNum: 4, loopInterval: 10, loopUnit: 1000, deviceCount: { totalCount: 0, onlineCount: 0 } } as IParams,
  });

  const updateParams = useMemoizedFn((data: Partial<IParams>) => setState((old) => ({ ...old, params: { ...old.params, ...data } })));

  // 设置选中设备
  const set = useMemoizedFn((item: VideoItem, index?: number) => {
    setState((old) => {
      const list = [...old.list];
      if (typeof index === 'number') {
        list[index] = item;
      } else {
        list.push(item);
      }
      return { ...old, list };
    });
  });

  const mutSet = useMemoizedFn((data: { [key: string]: VideoItem }) => {
    setState((old) => {
      const list = [...old.list];
      const keys = Object.keys(data);
      keys.forEach((key: string) => {
        list[+key] = data[key];
      });
      return { ...old, list };
    });
  });

  // 窗口变化 同步到list数据变化
  useEffect(() => {
    setState((old) => {
      const list = [...old.list];
      const arr = new Array(state.params.screenNum).fill(undefined);
      const newList = arr.map((v, i) => (list[i] ? list[i] : v));
      return { ...old, list: newList };
    });
  }, [state.params.screenNum]);

  // 根据cid获取
  const get = useMemoizedFn((cid) => state.list.find((v) => v?.cid === cid));

  // 清空选中设备
  const clear = useMemoizedFn(() => setState((old) => ({ ...old, list: old.list.map((v) => undefined) as any })));

  const selectItem = useMemo(() => state.list[state.params.screenIndex] || {}, [state.list, state.params.screenIndex]);

  const value = useMemo(
    () => ({ list: state.list, set, get, clear, updateParams, params: state.params, selectItem, mutSet } as IVideoContextProps),
    [state.list, state.params, set, get, clear, updateParams, selectItem, mutSet]
  );

  return <VideoContext.Provider value={value}>{children}</VideoContext.Provider>;
}
