.video-layout {
  height: 100%;
  box-sizing: border-box;
  padding: 16px;
  color: #333;
  background: #FFF;
  border-radius: 8px;
  .left {
    height: 100%;
    width: 344px;
    position: relative;
    .leftContent {
      height: 100%;
      background: #fff;
      // border-radius: 6px;
      overflow: hidden;
    }
  }
  .line{
    width: 1px ;
    height: 100%;
    background-color: #E7E7E7;
    margin: 0px 16px;
  }
  .switch {
    cursor: pointer;
    position: absolute;
    top: 50%;
    margin-top: -32px;
    width: 0;
    z-index: 99;
    width: 12px;
    height: 64px;
    background-color: #c0c5ca;
    border-radius: 7px;
    border: 1px solid #b1b7bc;
    .anticon {
      position: absolute;
      left: -5px;
      top: 20px;
      color: #333;
      font-size: 12px;
    }
  }
  .right {
    height: 100%;
    .rightContent {
      height: 100%;
      background: #FFF;
      // background: var(--primary-dark);
      box-shadow: 0px 5px 14px rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }
  }
}