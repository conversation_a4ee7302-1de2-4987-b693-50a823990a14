import { useToggle } from "ahooks";
import { Row, Col } from "antd";
import { IconFont } from "@cloud-app-dev/vidc";
import { ReactElement } from "react";
import "./index.less";

function VideoLayout({ leftControl, RightContent }: { leftControl: ReactElement; RightContent: ReactElement }) {
  //用于存储时间选择组件的渲染父节点
  const [open, { toggle }] = useToggle(true);
  return (
    <div style={{ padding: 16, width: "100%", height: "100%" }}>
      <Row className="video-layout">
        <Col className="left" style={{ display: open ? "block" : "none", paddingRight: 0 }}>
          <div className="leftContent">{leftControl}</div>
          <div className="switch" onClick={toggle} style={{ right: "-22px" }}>
            <IconFont
              type="icon-caret-right-small"
              style={{
                fontSize: "20px",
                transform: "rotate(180deg)",
              }}
            />
          </div>
        </Col>

        {!open && (
          <div className="switch" onClick={toggle} style={{ left: "13px" }}>
            <IconFont
              type="icon-caret-right-small"
              style={{
                fontSize: "20px",
              }}
            />
          </div>
        )}
        <div className="line"></div>
        <Col className="right" style={{ width: open ? "calc(100% - 320px - 24px - 34px)" : "calc(100% - 34px)", paddingLeft: 0, paddingRight: 0 }}>
          <div className="rightContent">{RightContent}</div>
        </Col>
      </Row>
    </div>
  );
}
export default VideoLayout;
