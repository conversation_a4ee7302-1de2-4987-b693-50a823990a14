.video-device-card {
  width: 100%;
  // height: 62px;
  margin-bottom: 8px;
  border: 1px solid #dcdcdc;
  color: #333;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 8px;

  user-select: none;
  cursor: pointer;
  border-radius: 4px;
  &:hover,
  &.video-device-card-mid-selected {
    border: 1px solid var(--primary);
    // background-color: rgba(11, 59, 253, 0.1);
    .top .cardHandle {
      display: block;
    }
  }
  &.video-device-card-mid-selected {
    .videoTitle {
      color: var(--primary);
    }
  }
  &.video-device-card-selected {
    border: 1px solid var(--primary);
    background-color: transparent !important;
    .devicename {
      color: var(--primary);
      border-bottom: 1px solid var(--primary);
    }
    .top .leftcontent .videoTitle {
      color: var(--primary);
    }
  }
  &.video-device-card-play {
    background-color: transparent;
    .devicename {
      // background: rgba(255, 255, 255, 0.1);
      // color: rgba(0, 0, 0, 0.90);
      // border-bottom: 1px solid var(--secondary3);
    }
    .top .leftcontent .videoTitle {
      // color: rgba(0, 0, 0, 0.90);
    }
  }
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .leftcontent {
      display: flex;
      align-items: center;
      width: 90%;
      .videoIcon {
        width: 20px;
        height: 20px;
        font-size: 18px;
        display: flex;
        align-items: center;
        //微徽章
        .ant-badge-dot {
          width: 3px;
          height: 3px;
          min-width: 3px;
          box-shadow: none;
        }
      }
      .videoTitle {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-left: 10px;
        line-height: 20px;
      }
    }
    .cardHandle {
      font-size: var(--fs-small);
    }
  }
  .bot {
    margin-top: 8px;
    display: flex;
    align-items: center;
    .item {
      margin-right: 16px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: rgba(0, 0, 0, 0.9);
      /* Body/Small */
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      border-radius: 3px;
      border: 1px solid #dcdcdc;
      background: #f3f3f3;
      padding: 0px 4px;
      .anticon {
        height: 17px;
        margin-right: 10px;
        font-size: 16px;
        position: relative;
        top: 1px;
      }
    }
  }
}

.video-device-list-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid transparent;
  color: var(--gray1);
  padding: 0 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  &:last-child{
    margin-bottom: 0px;
  }
  &:hover,
  &.video-device-list-item-mid-selected {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--primary);
  }
  &.video-device-list-item-mid-selected {
    .devicename{
      color: var(--primary) !important;
    }
   
  }
  &.video-device-list-item-selected {
    border: 1px solid var(--primary);
    background-color: transparent;
    .devicename {
      color: var(--primary) !important;
    }
  }
  &.video-device-list-item-play {
    // background: rgba(255, 255, 255, 0.1);
    .devicename {
      // color: var(--secondary3);
    }
  }

  .leftcontent {
    display: flex;
    align-items: center;
    width: 90%;
    .itemicon {
      //微徽章
      .ant-badge-dot {
        width: 3px;
        height: 3px;
        min-width: 3px;
        box-shadow: none;
      }
    }
    .devicename {
      margin-left: 10px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: rgba(0, 0, 0, 0.9);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
