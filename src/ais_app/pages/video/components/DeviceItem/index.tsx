import { IconFont } from '@cloud-app-dev/vidc';
import DeviceIcon from '@src/ais_app/components/DeviceIcon';
import { Badge, Tooltip, Tag } from 'antd';
import { ListItem } from '../../Context';
import './index.less';

interface IDeviceItemProps {
  item: any;
  isInplayer?: boolean;
  isSelected?: boolean;
  isMidSelected?: boolean;
  isLive?: boolean;
  onClick: (mode?: number) => void;
}

export function DeviceCard({ item, isInplayer, isSelected, onClick, isLive, isMidSelected }: IDeviceItemProps) {
  return (
    <div
      className={`video-device-card ${isMidSelected ? 'video-device-card-mid-selected' : ''} ${isInplayer ? 'video-device-card-play' : ''} ${
        isSelected ? 'video-device-card-selected' : ''
      }`}
      onClick={() => onClick()}
    >
      <div className="top">
        <div className="leftcontent">
          <div className="videoIcon">
            <DeviceIcon state={item.state} type={item.type} />
          </div>
          <div className="videoTitle" title={item?.name}>
            {item?.name}
          </div>
        </div>
        {isLive && item?.storageVideo === 1 && <LiveModeChange className="cardHandle" onClick={onClick} />}
      </div>
      <div className="bot">
        {item?.ptz === 1 && (
          <div className="ptz item" title="PTZ">
            {/* <IconFont type="icon-shipinchakan_PTZ" style={{ color: item?.state ? 'var(--success)' : 'inherit' }} /> */}
            PTZ
          </div>
        )}
        {item?.storageVideo === 1 && (
          <div className="cloudvideo item" title="云录像">
            {/* <IconFont type="icon-shipinchakan_yunluxiang" style={{ color: item?.state ? 'var(--success)' : 'inherit' }} /> */}
            云录像
          </div>
        )}
        {item?.localRecord === 1 && (
          <div className="webvideo item" title="前端录像">
            {/* <IconFont type="icon-shipinchakan_qianduanluxiang" style={{ color: item?.state ? 'var(--success)' : 'inherit' }} /> */}
            前端录像
          </div>
        )}
        {!item?.localRecord && !item?.storageVideo && (
          <div className="webvideo item" title="无录像" style={{ color: 'var(--gray8)' }}>
            {/* <IconFont type="icon-shipinchakan_qianduanluxiang" /> */}
            无录像
          </div>
        )}
        {
          item?.tagInfoList?.map((v:any)=>{
            return <Tag
            style={{
              color: "#" + v?.tagFontColor,
              background: "#" + v?.tagBgColor,
              borderColor: "#" + v?.tagFontColor,
            }}
          >
            {v?.tagName}
          </Tag>
          })
        }
      </div>
    </div>
  );
}

export function DeviceListItem({ item, isInplayer, isSelected, onClick, isLive, isMidSelected }: IDeviceItemProps) {
  return (
    <div
      className={`video-device-list-item ${isMidSelected ? 'video-device-list-item-mid-selected' : ''} ${isInplayer ? 'video-device-list-item-play' : ''} ${
        isSelected ? 'video-device-list-item-selected' : ''
      }`}
      onClick={() => onClick()}
    >
      <div className="leftcontent">
        <div className="itemicon">
          <Badge dot={item?.storageVideo || item?.localRecord ? true : false} size="small">
            <DeviceIcon state={item.state} type={item.type} />
          </Badge>
        </div>
        <div className="devicename" title={item?.name}>
          {item?.name}
        </div>
      </div>
      {isLive && <LiveModeChange className="listitemHandle" onClick={(mode?: number) => onClick(mode)} />}
    </div>
  );
}

interface ILiveModeChangeProps {
  className?: string;
  onClick: (mode?: number) => void;
}

function LiveModeChange({ className, onClick }: ILiveModeChangeProps) {
  return (
    <div className={`${className} device-live-mode-change`}>
      <Tooltip
        title={
          <div
            className="handleitem"
            style={{ cursor: 'pointer' }}
            onClick={(e) => {
              e.stopPropagation();
              onClick(2);
            }}
          >
            切换流畅优先
          </div>
        }
        trigger="click"
        placement="bottomRight"
      >
        <IconFont type="icon-shebeiguanli_gengduo" style={{ fontSize: '16px' }} onClick={(e) => e.stopPropagation()} />
      </Tooltip>
    </div>
  );
}
