import { useSimpleState } from '@cloud-app-dev/vidc';
import { useEventListener, useLatest, useSize } from 'ahooks';
import { BasicTarget, getTargetElement } from 'ahooks/lib/utils/domTarget';
import { useEffect, useMemo } from 'react';

function useRect(target: BasicTarget<HTMLCanvasElement>) {
  const [state, updateState, setState] = useSimpleState<{ tempRect: number[]; isOpen: boolean; rects: number[][] }>({ tempRect: [], rects: [], isOpen: false });
  const isOpenRef = useLatest(state.isOpen);
  const rectRef = useLatest(state.tempRect);
  const size = useSize(target);

  const status = useMemo(() => (state.isOpen ? 'open' : 'close'), [state.isOpen]);
  const open = () => updateState({ isOpen: true });
  const close = () => updateState({ isOpen: false });
  const clear = () => updateState({ tempRect: [], rects: [] });

  useEffect(() => {
    if (!size) {
      return;
    }
    const canvas = getTargetElement(target) as HTMLCanvasElement;
    canvas.width = size?.width;
    canvas.height = size?.height;
    console.warn('canvas，size变化，请重新绘制！');
    updateState({ rects: [], tempRect: [] });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [size?.height, size?.width]);

  useEventListener(
    'mousedown',
    (event: MouseEvent) => {
      if (!isOpenRef.current) {
        return;
      }
      const p_x = event.offsetX;
      const p_y = event.offsetY;
      updateState({ tempRect: [p_x, p_y] });
    },
    { target }
  );

  useEventListener(
    'mousemove',
    (event: MouseEvent) => {
      const [x, y] = rectRef.current;
      if (x === undefined || y === undefined) {
        return;
      }
      const p_x = event.offsetX;
      const p_y = event.offsetY;
      const width = p_x - x;
      const height = p_y - y;
      updateState({ tempRect: [x, y, width, height] });
    },
    { target }
  );

  useEventListener(
    'mouseup',
    () => {
      if (!isOpenRef.current) {
        return;
      }
      const rect = rectRef.current;
      if (rect.length !== 4 || rect[2] < 10 || rect[3] < 10) {
        console.warn('目标框太小');
        updateState({ tempRect: [] });
        return;
      } else {
        setState((old) => ({ ...old, tempRect: [], rects: [...old.rects, rect] }));
      }
    },
    { target }
  );

  const clearCanvas = () => {
    const canvas = getTargetElement(target) as HTMLCanvasElement;
    const ctx = canvas.getContext('2d');
    // 重置画布
    ctx?.clearRect(0, 0, canvas.width, canvas.height);
  };

  const deleteRect = (rect: number[]) => {
    setState((old) => {
      const newArea = old.rects.filter((v) => v.join('-') !== rect.join('-'));
      return { ...old, rects: [...newArea] };
    });
  };

  const drawRects = (rects: number[][]) => {
    const canvas = getTargetElement(target) as HTMLCanvasElement;
    const ctx = canvas?.getContext('2d') as CanvasRenderingContext2D;
    // 绘制已完成的图形
    rects.forEach((item) => {
      // 绘制坐标线
      const [x, y, w, h] = item;
      ctx.beginPath();
      ctx.moveTo(x, y);
      ctx.lineTo(x + w, y);
      ctx.lineTo(x + w, y + h);
      ctx.lineTo(x, y + h);
      ctx.closePath();
      ctx.strokeStyle = '#f32d37';
      ctx.lineWidth = 2;
      ctx.fillStyle = 'rgba(243,45,55,0.5)';
      ctx.fill();
      ctx.stroke();
    });
  };

  //绘制hook
  useEffect(() => {
    clearCanvas();
    drawRects(state.rects);
    drawRects([state.tempRect]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.rects, state.tempRect]);

  return { open, close, clear, status, rects: state.rects, deleteRect };
}

export default useRect;
