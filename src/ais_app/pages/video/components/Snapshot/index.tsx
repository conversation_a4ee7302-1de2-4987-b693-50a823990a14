import { IconFont, useSimpleState } from "@cloud-app-dev/vidc";
import { TaskItemType } from "@src/ais_app/core/interface.d";
import { queryAisAlarmTaskList } from "@src/service/ais_app";
import { useRequest } from "ahooks";
import { Button, Select, message } from "antd";
import { useMemo, useRef } from "react";
import { handleAlarm } from "./utils";
import useRect from "./useRect";
import "./index.less";

interface ISnapshotProps {
  url: string;
  downloadName: string;
  cid: string;
  onAlaramHandle?: () => void;
}
function Snapshot({ url, downloadName, cid, onAlaramHandle }: ISnapshotProps) {
  const [state, updateState] = useSimpleState<{ open: boolean; taskId?: number; desc?: string }>({ open: false, taskId: undefined, desc: undefined });
  const { data: taskRes = {} } = useRequest(() => queryAisAlarmTaskList({ cids: [cid], limit: 10000, offset: 0 }), { refreshDeps: [cid] });
  const taskList = useMemo(() => (taskRes.list ?? []) as TaskItemType[], [taskRes]);
  const ref = useRef<HTMLCanvasElement>(null);
  const { open, status, close, clear, rects, deleteRect } = useRect(ref);

  // 获取多边形中心点
  const centerPoints = useMemo(() => rects.map((v) => [v[0] + v[2] / 2, v[1] + v[3] / 2]), [rects]);

  //触发手动告警方法
  const { loading, run: controllAlarm } = useRequest(
    async () => {
      if (!state.taskId || !url || !cid) {
        return undefined;
      }
      const taskItem = taskList.find((v) => v.id === state.taskId) as TaskItemType;
      const boxCoordinate = rects.map(([x, y, w, h]) => ({ left: x, top: y, width: w, height: h }));
      const blob = await (await fetch(url)).blob();
      const form = new FormData();
      form.append("cid", cid);
      form.append("taskId", state.taskId.toString());
      form.append("file", blob);
      form.append("alarmTime", Date.now().toString());
      form.append("algorithmId", taskItem.algorithmId.toString());

      boxCoordinate.forEach((item, index) => {
        form.append(`boxCoordinate[${index}].left`, item.left.toString());
        form.append(`boxCoordinate[${index}].top`, item.top.toString());
        form.append(`boxCoordinate[${index}].width`, item.width.toString());
        form.append(`boxCoordinate[${index}].height`, item.height.toString());
      });

      return handleAlarm(form)
        .then((res: any) => (res.code === 0 ? Promise.resolve() : Promise.reject(res)))
        .then(() => {
          message.success("告警已触发！");
          onAlaramHandle?.();
        })
        .catch((e) => {
          console.error(e);
          message.warning("操作失败");
        });
    },
    { manual: true, refreshDeps: [cid, url, rects, state.taskId] },
  );

  return (
    <>
      <div className="video-snapshot-layout">
        
        <div className="img-layout">
          {url === "data:," ? <div className="no-img">暂无截图</div> : <img src={url} alt="" />}
          <canvas ref={ref} style={{ cursor: status === "open" ? "crosshair" : "unset" }} />
          <div className="video-snapshot-rect-mask">
            {centerPoints.map(([x, y], i) => (
              <span style={{ left: x, top: y }} key={i}>
                目标{i + 1}
              </span>
            ))}
          </div>
          {/* {state.open && (
            <div className="video-snapshot-rect-box">
              <div className="video-snapshot-rect-header">
                <span>框选目标</span>
                <Button type="primary" size="small" onClick={status === 'open' ? close : open}>
                  {status === 'open' ? '取消' : '添加'}
                </Button>
              </div>
              <div className="video-snapshot-rect-content">
                {rects.map((rect, i) => (
                  <div key={i} className="video-snapshot-rect-item">
                    <span>目标{i + 1}</span>
                    <span></span>
                    <span onClick={() => deleteRect(rect)}>删除</span>
                  </div>
                ))}
              </div>
              <div className="video-snapshot-rect-footer">
                <IconFont type="icon-renwupeizhi_shebeirenwu_qingkong" />
                <span onClick={clear}>清空</span>
              </div>
            </div>
          )} */}
        </div>
        {/* {url !== 'data:,' && (
          <div className="tools-layout">
            <Button type="link" download={`${downloadName}.png`} href={url}>
              下载
            </Button>
            <Button type="link" onClick={() => updateState({ open: true })}>
              手动告警
            </Button>
          </div>
        )} */}
      </div>
      <div>
        {/* {state.open && (
          <div className="snapshot-handle-alarm-layout">
            <div className="alarm-form">
              <div className="form-item">
                <span className="form-label">所属任务：</span>
                <span>
                  <Select style={{ minWidth: 200 }} placeholder="请选择告警所属任务" value={state.taskId} onChange={(val) => updateState({ taskId: val })}>
                    {taskList.map((item) => (
                      <Select key={item.id} value={item.id}>
                        {item.taskName}
                      </Select>
                    ))}
                  </Select>
                </span>
              </div>
            </div>
            <div className="alarm-tools">
              <Button onClick={() => updateState({ open: false })}>取消</Button>
              <Button type="primary" onClick={controllAlarm} loading={loading} disabled={!(state.taskId && rects.length > 0)}>
                触发告警
              </Button>
            </div>
          </div>
        )} */}
      </div>
    </>
  );
}

export default Snapshot;
