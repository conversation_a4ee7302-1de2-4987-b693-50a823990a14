.video-snapshot-layout {
  // background-color: var(--background);
  width: 705px;
  position: relative;
  margin-top: 20px;
  .img-layout {
    display: flex;
    align-items: center;
    .no-img{
      width: 100%;
      text-align: center;
      font-size: 30px;
      min-height: 365px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    & > img {
      width: 100%;
    }
    & > canvas {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      // min-height: 468px;
    }
    .video-snapshot-rect-box {
      position: absolute;
      background: var(--secondary1);
      opacity: 0.8;
      width: 140px;
      right: 10px;
      top: 10px;
      min-height: 160px;
      font-size: 12px;
      user-select: none;
    }
    .video-snapshot-rect-mask {
      span {
        height: 29px;
        line-height: 28px;
        position: absolute;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 2px;
        font-size: 12px;
        padding: 0 8px;
        user-select: none;
      }
    }
    .video-snapshot-rect-header {
      display: flex;
      justify-content: space-between;
      padding: 0 8px;
      align-items: center;
      height: 40px;
      line-height: 40px;
    }
    .video-snapshot-rect-content {
      min-height: calc(160px - 40px - 32px);
      .video-snapshot-rect-item {
        display: flex;
        justify-content: space-around;
        margin: 4px 0;
        align-items: center;
        padding: 0 8px;
        span:nth-child(2) {
          flex: 1;
          height: 1px;
          background-color: var(--danger);
          margin: 0 4px;
        }
        span:nth-child(3) {
          cursor: pointer;
          color: var(--primary);
        }
      }
    }
    .video-snapshot-rect-footer {
      height: 32px;
      text-align: center;
      color: var(--primary-light);
      border-top: 1px solid var(--bd);
      line-height: 32px;
      .anticon {
        padding-right: 4px;
      }
    }
  }

  .tools-layout {
    height: 45px;
    width: 100%;
    text-align: center;
    line-height: 45px;
  }
}

.snapshot-handle-alarm-layout {
  .form-item {
    display: flex;
    padding-top: 20px;
    .form-label {
      display: inline-block;
      width: 80px;
      text-align: right;
      & + span {
        flex: 1;
      }
    }
  }
  .alarm-tools {
    padding: 20px 0;
    text-align: center;
    button {
      margin: 0 10px;
    }
  }
}
