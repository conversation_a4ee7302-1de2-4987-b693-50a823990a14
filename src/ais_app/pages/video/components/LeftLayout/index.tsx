import { useEventListener, useSafeState } from 'ahooks';
import { ReactElement, useRef } from 'react';
import './index.less';

interface ILeftControlLayoutProps {
  topContent: ReactElement;
  bottomContent: ReactElement;
  midContent?: ReactElement;
  minTopHeight?: number;
}
function LeftLayout({ topContent, bottomContent, midContent, minTopHeight = 200 }: ILeftControlLayoutProps) {
  const ref = useRef<HTMLDivElement>(null);
  const [state, setState] = useSafeState({ topSize: minTopHeight });
  const statusRef = useRef({ mouseDown: false });

  useEventListener('mouseup', () => (statusRef.current.mouseDown = false), { target: document.body });

  useEventListener(
    'mousedown',
    (e) => {
      if (e.currentTarget === ref.current?.querySelector('.move-line')) {
        statusRef.current.mouseDown = true;
      }
    },
    { target: ref.current?.querySelector('.move-line') }
  );

  useEventListener(
    'mousemove',
    (e: MouseEvent) => {
      if (!statusRef.current.mouseDown) {
        return;
      }
      const moveSize = e.movementY;

      setState((old) => ({ ...old, topSize: old.topSize + moveSize }));
    },
    { target: ref }
  );

  return (
    <div className="video-left-layout" ref={ref}>
      <div className="top-content" style={{ height: state.topSize, minHeight: minTopHeight, maxHeight: 600 }}>
        {topContent}
      </div>
      <div className="move-line" style={{ cursor: 'row-resize' }} draggable={false}>
        <div />
      </div>
      <div className="mid-content">{midContent}</div>
      <div className="bottom-content">{bottomContent}</div>
    </div>
  );
}

export default LeftLayout;
