import { Calendar, message } from 'antd';
import { IconFont, RefModal, useSimpleState } from '@cloud-app-dev/vidc';
import dayjs, { Dayjs } from 'dayjs';
import { useContext, useMemo, useRef } from 'react';
import { ListItem, VideoContext, VideoItem } from '../../Context';
import { IRefModalMethodsProps } from '@cloud-app-dev/vidc/es/RefModal';
import { nextTick } from '@cloud-app-dev/vidc';
import { queryRecord } from '../../record/utils';
import { useUpdateEffect } from 'ahooks';
import 'dayjs/locale/zh-cn';
import './index.less';

dayjs.locale('zh-cn');

const CalendarHandle = () => {
  const { params, list, set, updateParams } = useContext(VideoContext);
  const [state, stateChange] = useSimpleState({ date: dayjs() });

  // 当前选中的窗口
  const currentWinItem = useMemo(() => list[params.screenIndex], [list, params.screenIndex]);

  useUpdateEffect(() => {
    if (!!currentWinItem && currentWinItem?.cid === params.selectDeviceItem?.cid) {
      stateChange({ date: dayjs(currentWinItem?.date) });
    }
  }, [currentWinItem, params.selectDeviceItem]);

  // 日期开启选择状态
  const open = useMemo(() => params.recordDateVisible, [params.recordDateVisible]);
  const ref = useRef<IRefModalMethodsProps>(null);
  const hasRecord = useMemo(
    () => params.selectDeviceItem && (params.selectDeviceItem?.localRecord === 1 || params.selectDeviceItem?.storageVideo === 1),
    [params.selectDeviceItem]
  );

  const onSelect = (value: Dayjs) => {
    const selectItem = params.selectDeviceItem || ({} as ListItem);
    const { name, cid } = selectItem;
    const date = value.set("hours",0).set("minutes",0).set("seconds",0).valueOf();

    // 打开录像通用方法
    const openRecord = (item: VideoItem) => {
      let i = 0;
      const index = list.findIndex((v) => !v);
      if (index === -1) {
        i = params.screenIndex;
        // if (params.screenNum === 16) {
        //   // 16窗口开满的情况下
        //   i = params.screenIndex;
        // } else {
        //   // 当前窗口已经开满了
        //   const screens = [1, 4, 6, 9, 16];
        //   const screenNumIndex = screens.indexOf(params.screenNum as number);
        //   updateParams({ screenNum: screens[screenNumIndex + 1] as any, screenIndex: list.length });
        //   i = list.length;
        // }
      } else {
        // 当前窗口未开满,打开并选中当前窗口
        i = index;
        updateParams({ screenIndex: index });
      }

      // 处理播放器状态 获取录像片段
      item.loading = true;
      set(item, i);
      queryRecord(item)
        .then((segments:any) => {
          item.loading = false;
          item.segments = segments;
          set(item, i);
        })
        .catch((e:any) => {
          console.error(e);
          set(undefined, i);
        });
    };

    let listItem = { name, cid, date, recordType: 1, type: 'hls' } as VideoItem;
    // 是否相同设备，打开相同日期的录像
    const isOpenIndex = list.findIndex((v) => v?.cid === cid && v?.date === date);
    if (isOpenIndex > -1) {
      message.warning('当前录像已经正在播放！');
      updateParams({ screenIndex: isOpenIndex });
      return;
    }

    // 前端录像、云录像同时存在，用户选择处理
    if (selectItem.storageVideo === 1 && selectItem.localRecord === 1) {
      const onChange = (options: any) => {
        listItem = { ...listItem, ...options };
      };

      ref.current?.open({
        title: '选择录像方式',
        width: 400,
        content: <SelectRecordType onChange={onChange} key={Date.now()} />,
        onOk() {
          listItem.type = listItem.recordType === 1 ? 'hls' : 'flv';
          openRecord(listItem);
          nextTick(() => ref.current?.close());
        },
      });
      return;
    }

    // 一种录像,默认处理
    listItem.recordType = selectItem.storageVideo === 1 ? 1 : selectItem.localRecord === 1 ? 2 : undefined;
    if (!listItem.recordType) {
      console.warn('不支持录像的设备', listItem);
      return;
    }
    listItem.type = listItem.recordType === 1 ? 'hls' : 'flv';
    openRecord(listItem);
  };

  return (
    <div className="calendar-handle">
      <RefModal ref={ref} />
      <div className="header-part">
        <div className="header-left">
          {/* <div>
            <IconFont type="icon-renwupeizhi_shebeirenwu_shebeimulu" style={{ marginRight: 10, fontSize: 20 }} />
          </div> */}
          <div className="header-left-title">选择录像日期</div>
        </div>
        <div className="header-right">
          {open ? (
            <IconFont onClick={() => updateParams({ recordDateVisible: !open })} style={{ cursor: 'pointer' }} type={'icon-shipinchakan_shouqi'} />
          ) : (
            <IconFont onClick={() => updateParams({ recordDateVisible: !open })} style={{ cursor: 'pointer', transform: 'rotate(180deg)' }} type={'icon-shipinchakan_shouqi'} />
          )}
        </div>
      </div>
      {open && (
        <Calendar
          fullscreen={false}
          className="calendar-select-box"
          value={state.date}
          onSelect={onSelect}
          disabledDate={(v) => (!hasRecord ? true : dayjs(new Date()).diff(v) < 0)}
          headerRender={({ value }) => {
            return (
              <div className="header">
                <div className="pre" onClick={() => stateChange({ date: value.clone().add(-1, 'M') })}>
                  <IconFont type="icon-renwupeizhi_shebeirenwu_shouqi" />
                </div>
                <div className="months">{value.format('YYYY-MM')}</div>
                <div className="next" onClick={() => stateChange({ date: value.clone().add(1, 'M') })}>
                  <IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" />
                </div>
              </div>
            );
          }}
          fullCellRender={(date) => (
            <span
              className={`cloudapp-picker-cell-inner cloudapp-picker-calendar-date ${
                date.format('YYYY-MM-DD') === dayjs(new Date()).format('YYYY-MM-DD') && 'cloudapp-picker-calendar-date-today'
              }`}
            >
              <span className="cloudapp-picker-calendar-date-value">{date.format('YYYY-MM-DD') === dayjs(new Date()).format('YYYY-MM-DD') ? '今' : date.format('DD')}</span>
            </span>
          )}
        />
      )}
    </div>
  );
};

interface ISelectRecordTypeProps {
  onChange: (val: any) => void;
}

function SelectRecordType({ onChange }: ISelectRecordTypeProps) {
  const [state, setState] = useSimpleState({ recordType: 1 });
  const select = (recordType: number) => {
    onChange({ recordType });
    setState({ recordType });
  };
  return (
    <div className="video-record-type-select">
      <div className={state.recordType === 1 ? 'selected-item' : ''} onClick={() => select(1)}>
        <IconFont type="icon-shipinchakan_yunluxiang" />
        <span>云录像</span>
      </div>
      <div className={state.recordType === 2 ? 'selected-item' : ''} onClick={() => select(2)}>
        <IconFont type="icon-shipinchakan_qianduanluxiang" />
        <span>前端录像</span>
      </div>
    </div>
  );
}

export default CalendarHandle;
