.calendar-handle {
  .ant-picker-date-panel .ant-picker-content th {
    color: rgba(0, 0, 0, 0.9);
    font-size: 14px;
  }
  .ant-picker-cell-selected {
    color: var(--primary);
    border-radius: 3px;
    background: #e8f3ff;
  }
  .ant-picker-cell-disabled .cloudapp-picker-calendar-date-value {
    color: rgba(0, 0, 0, 0.26);
    text-align: center;
    /* Body/Medium */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
  .calendar-handle .ant-picker-cell-disabled {
    opacity: 1;
  }
  .header-part {
    height: 40px;
    font-size: var(--fs);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    .header-left,
    .header-right {
      height: 100%;
      display: flex;
      align-items: center;
    }
    .header-left-title {
      color: rgba(0, 0, 0, 0.9);
      /* Title/Medium */
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
    }
  }
  .calendar-select-box {
    padding: 0 16px 16px 16px;
    color: rgba(0, 0, 0, 0.9);
    .header {
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(0, 0, 0, 0.9);
      position: relative;
      height: 36px;
      .pre {
        position: absolute;
        left: 8px;
        cursor: pointer;
      }
      .next {
        position: absolute;
        right: 8px;
        cursor: pointer;
      }
    }
  }

  .ant-picker-calendar-mini .ant-picker-content {
    height: 186px;
  }
  .ant-picker-cell-disabled {
    // opacity: 0.6;
  }
  .ant-picker-calendar-date-value {
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
  .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before,
  .ant-picker-cell .ant-picker-cell-inner {
    border-radius: 50%;
    color: rgba(0, 0, 0, 0.9);
    width: 20px;
    height: 20px;
    min-width: 20px;
    line-height: 20px;
    font-size: 12px;
  }
  .ant-picker-calendar {
    background-color: transparent;
    .ant-picker-panel {
      background-color: rgba(255, 255, 255, 0.12);
      border: none;
    }
  }
}

.video-record-type-select {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 20px;
  & > div {
    width: 110px;
    height: 100px;
    background-color: var(--form-bg);
    font-size: var(--fs-large);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    padding: 10px 0;
    border: 1px solid var(--form-bg);
    cursor: pointer;
    &.selected-item {
      border-color: var(--secondary2);
      background-color: #E8F3FF;
    }
    .anticon {
      font-size: 30px;
    }
  }
}
