import { uniq } from 'lodash-es';
import { ITreeItem } from '../../Context';

export function getTreeCode(treeData: any[], arr = [] as string[]) {
  treeData.forEach((item: any) => {
    arr.push(item.code);
    if (Array.isArray(item.children) && item.children.length > 0) {
      getTreeCode(item.children, arr);
    }
  });
  return arr;
}

export function getTreeIdWithKeyword(treeMap: { [key: string]: ITreeItem }, keyword: string) {
  const arr = [] as string[];
  if (!keyword) {
    return arr;
  }
  const getParentCode = (code: string, arr2 = [] as string[]) => {
    if (treeMap[code]) {
      const item2 = treeMap[code];
      arr2.push(code);
      if (item2.parentId && treeMap[item2.parentId]) {
        getParentCode(item2.parentId);
      }
    }
    return arr2;
  };
  const data = Object.values(treeMap);
  data.forEach((item: ITreeItem) => {
    if (item.groupName.indexOf(keyword)) {
      arr.push(...getParentCode(item.id));
    }
  });

  return uniq(arr);
}

export function getTreeMap(treeData: ITreeItem[]) {
  const map = {} as any;
  treeData.forEach((item: ITreeItem) => {
    map[item.id] = item;
  });
  return map;
}


