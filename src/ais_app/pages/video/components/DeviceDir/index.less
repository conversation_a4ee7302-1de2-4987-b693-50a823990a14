.video-group-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  .video-group-content {
    flex: 1;
    contain: strict;
    overflow: auto;
    padding: 0 16px 10px;
    color: #333;
    .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
      background-color: #E8F3FF;
      color: var(--primary);
    }
    .ant-tree-switcher {
      line-height: 32px;
    }
    .video-group-item {
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        flex: 1;
        display: flex;
        align-items: center;
        .title {
          margin-left: 6px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .count {
          margin-left: 6px;
          span {
            color: var(--primary);
          }
        }
      }
      .rigth {
        width: 20px;
        font-size: var(--fs-large);
        display: none;
      }
    }
    .video-group-item:hover {
      .rigth {
        display: block;
      }
    }
    .ant-tree-treenode {
      width: 100%;
      padding: 0;
    }
    .ant-tree .ant-tree-node-content-wrapper {
      flex: 1;
      min-width: 190px;
    }
    .ant-tree-treenode:hover .ant-tree-node-content-wrapper {
      background-color: #E8F3FF;
    }

    .site-tree-search-value {
      color: var(--danger);
    }
  }
}
