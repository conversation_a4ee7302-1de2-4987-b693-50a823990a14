import { useRequest, useUpdateEffect } from 'ahooks';
import { IconFont, useSimpleState ,treeHelper} from '@cloud-app-dev/vidc';
import { message, Tree } from 'antd';
import { Key, useContext, useMemo, useRef, useEffect } from 'react';
import { cloneDeep } from 'lodash-es';
import { ITreeItem, VideoContext } from '../../Context';
import TreeHeader from '../TreeHeader';
import { getGroups, getGroupsNew } from '../utils';
import { getTreeIdWithKeyword, getTreeMap } from './utils';
import './index.less';

function DeviceDir({getCatalog,setCatalog}:any) {
  const { updateParams, params } = useContext(VideoContext);
  const [state, stateChange] = useSimpleState({ keyword: '', expandedAll: false, expandedKeys: [] as string[], forceKey: Date.now(), refreshLoading: false });
  const ref = useRef<HTMLDivElement>(null);

  // 基于group的树数据
  const { data: treeResultData, run } = useRequest<ITreeItem[], any>(() => {
    if(getCatalog === '1'){
      return getGroupsNew()
    }
    return getGroups()
  }, {
    refreshDeps: [state.forceKey],
    onSuccess: () => stateChange({ refreshLoading: false }),
  });

  const treeRes = useMemo(() => treeResultData ?? ([] as ITreeItem[]), [treeResultData]);

  const treeMap = useMemo(() => getTreeMap(treeRes), [treeRes]);
  const treeDataTemp = useMemo(() => treeHelper.computTreeList(treeRes), [treeRes]);

  useUpdateEffect(() => {
    const { onlineCount, totalCount } = treeDataTemp.reduce(
      (p, n) => {
        p.onlineCount += n.onlineCount || 0;
        p.totalCount += n.totalCount || 0;
        return p;
      },
      { totalCount: 0, onlineCount: 0 }
    );
    updateParams({ deviceCount: { totalCount, onlineCount } });
  }, [treeDataTemp]);

  // 树搜索处理,匹配结果展开
  const onFilterChange = (name: string) => {
    const codes = getTreeIdWithKeyword(treeMap, name);
    stateChange({ keyword: name, expandedKeys: codes });
  };

  useUpdateEffect(() => {
    if (state.refreshLoading) {
      return undefined;
    }
    message.destroy();
    message.success('数据已更新！');
  }, [state.refreshLoading]);

  const setExpandAll = () => {
    let expandedKeys: any = [];
    if (!state.expandedAll) {
      expandedKeys = treeRes.map((v) => v.id);
    }
    stateChange({ expandedKeys, expandedAll: !state.expandedAll } as any);
  };

  // 树选中处理
  const onSelect = (keys: Key[]) => {
    const id = keys[0];

    if (!id) {
      updateParams({ currentTreeItem: undefined });
    } else {
      const item = treeRes.find((v) => id === v.id) as ITreeItem;
      const { children, ...data } = item;
      updateParams({ currentTreeItem: cloneDeep(data) });
    }
  };

  // 树数据处理，高亮关键字
  const treeData = useMemo(() => {
    const loop = (data: ITreeItem[]): ITreeItem[] =>{
    // const {level=1,id}:any =  params?.currentTreeItem
     return data.map((item: any) => {
        const strName = item.groupName as string;
        const index = strName.indexOf(state.keyword);
        const beforeStr = strName.substring(0, index);
        const afterStr = strName.slice(index + state.keyword.length);
        const name =
          index > -1 ? (
            <span>
              {beforeStr}
              <span className="site-tree-search-value">{state.keyword}</span>
              {afterStr}
            </span>
          ) : (
            <span>{strName}</span>
          );
        const title = (
          <div className="video-group-item">
            <div className="left">
              <IconFont type={`${item.children?.length > 0 ? 'icon-wenjianjiaxianxing' : 'icon-renwupeizhi_shebeirenwu_fenzu'}`} />
              <div className="title">{name}</div>
              <div className="count">
                <span>{item?.onlineCount ?? 0}</span>/{item?.totalCount ?? 0}
              </div>
            </div>
            {/* <div className="rigth" onClick={() => updateParams({ loopTreeId: item.id })}>
              <IconFont type="icon-shipinchakan_lunxunshezhi" />
            </div> */}
          </div>
        );

        if (item.children) {
          return { ...item, name: title, code: item.code, children: loop(item.children) };
        }

        return { ...item, name: title, code: item.code };
      });
}
    return loop(treeDataTemp);
  }, [state.keyword, treeDataTemp, updateParams, getCatalog]);
  useEffect(()=>{
    updateParams({ currentTreeItem: undefined });
    run()
  },[getCatalog])
  return (
    <div className="video-group-layout">
      <TreeHeader
        onKeywordChange={onFilterChange}
        setExpandAll={setExpandAll}
        total={params.deviceCount.totalCount}
        onlineTotal={params.deviceCount.onlineCount}
        expandAll={state.expandedAll}
        onRefresh={() => (!state.refreshLoading ? stateChange({ forceKey: Date.now(), refreshLoading: true }) : undefined)}
        getCatalog={getCatalog} 
        setCatalog={setCatalog}
      />
      <div className="video-group-content">
        <div ref={ref}>
          <Tree
            switcherIcon={<IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" style={{ transform: 'rotate(90deg)' }} />}
            treeData={treeData}
            fieldNames={{ title: 'name', key: 'id' }}
            onExpand={(expandedKeys) => stateChange({ expandedKeys } as any)}
            expandedKeys={state.expandedKeys}
            onSelect={onSelect}
            selectedKeys={[params.currentTreeItem?.id] as any}
          />
        </div>
      </div>
    </div>
  );
}
export default DeviceDir;
