.video-loop-modal {
  height: 610px;
  .ant-modal-body {
    padding: 0 20px;
    height: 495px;
  }
}
.lx-set-content {
  height: 100%;
  width: 100%;
  .form-part {
    height: 63px;
    display: flex;
    align-items: center;
    padding: 20px 20px 10px 20px;
    justify-content: space-between;
  }
  .ant-input-number-group {
    color: inherit;
  }
  .ant-input-number-group-addon {
    background-color: var(--form-bg);
    border-color: var(--form-bd);
    color: inherit;
  }
  .form-item-part {
    font-size: var(--fs-small);
    display: flex;
    align-items: center;
    .label {
      display: inline-block;
      width: 60px;
    }
    input {
      font-size: var(--fs-small);
    }
    .ant-select {
      font-size: var(--fs-small);
      width: 70px;
      margin-right: 20px;
    }
  }
  .check-all-box {
    display: inline-block;
    width: 120px;
    text-align: right;
    font-size: var(--fs-small);
    padding-right: 5px;
  }
  .check-video-part {
    width: 100%;
    height: calc(~'100% - 63px');
    padding: 0 20px 20px 20px;
    .video-item {
      float: left;
      position: relative;

      .ant-checkbox {
        position: absolute;
        z-index: 1;
        right: 5px;
        top: 5px;
      }
      .video-content {
        color: var(--gray1);
        position: absolute;
        padding: 1px;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        .has-video {
          color: var(--secondary1-light);
        }
        & > div {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          background-color: #0e254b;
          span {
            font-size: var(--fs-small);
          }
          .anticon {
            font-size: 40px;
          }
        }
      }
    }
  }
  .tools-split-screen {
    cursor: pointer;
    display: inline-block;
    width: 150px;
    height: 32px;
    border-radius: 3px;
    line-height: 32px;
    padding: 0 16px;
    .anticon {
      font-size: 12px;
      &:first-child {
        padding-right: 6px;
      }
      &:last-child {
        padding-top: 10px !important;
      }
    }
  }
}
