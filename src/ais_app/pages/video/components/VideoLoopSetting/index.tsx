import React, { useContext, useEffect, useMemo } from 'react';
import { Select, Checkbox, InputNumber, message ,Modal} from 'antd';
import { IconFont,  useSimpleState } from '@cloud-app-dev/vidc';
import { VideoContext } from '../../Context';
import { ScreenType } from '@cloud-app-dev/vidc/es/ScreenPlayer/utils';

import './index.less';

export default function VideoLoopSetting() {
  const { params, updateParams, list } = useContext(VideoContext);
  const [state, stateChange, setState] = useSimpleState({ screenNum: params.screenNum, data: [] as boolean[], open: false });
  const currentScreen = useMemo(() => ScreenType.find((v) => v.name === state.screenNum, []) as any, [state.screenNum]);
  const submitLoop = async () => {
    if (state.data.filter((v) => !!v).length === 0) {
      return message.warning('请先选择轮巡视频窗口！');
    }
    const indexs = state.data.map((v, i) => (!!v ? i : undefined)).filter((v) => typeof v === 'number') as number[];
    updateParams({ loopWinsIndex: indexs, loopId: params.loopTreeId });
    stateChange({ open: false });
  };

  const onCancel = () => {
    stateChange({ open: false });
    updateParams({ loopTreeId: undefined });
  };

  useEffect(() => stateChange({ open: !!params.loopTreeId }), [params.loopTreeId, stateChange]);

  //单个选中
  const changeLoopBox = (index: number) => {
    setState((old) => {
      const data = [...old.data];
      data[index] = !data[index];
      return { ...old, data };
    });
  };

  // 窗口变化 同步到list数据变化
  useEffect(() => {
    setState((old) => {
      const list = [...old.data];
      const arr = new Array(state.screenNum).fill(undefined);
      const newList = arr.map((v, i) => (list[i] ? list[i] : v));
      return { ...old, data: newList };
    });
  }, [setState, state.screenNum]);

  const checkedAll = useMemo(() => state.data.findIndex((v) => !v) === -1, [state.data]);

  const onChangeAll = () => {
    const arr = [...state.data].map(() => !checkedAll);
    stateChange({ data: arr });
  };

  return (
    <Modal
      className="video-loop-modal modal-component"
      width={772}
      open={state.open}
      title={'轮巡设置'}
      onCancel={onCancel}
      onOk={submitLoop}
      // disabled={checkSize === 0 || loopListSize === 0}
    >
      <div className="video-loop-screen-popup-layout" />
      <div className="lx-set-content">
        <div className="form-part">
          <div className="form-item-part">
            <span className="label">时间间隔：</span>
            <InputNumber
              type="number"
              onChange={(loopInterval) => updateParams({ loopInterval } as any)}
              value={params.loopInterval}
              style={{ width: 180 }}
              addonAfter={
                <Select value={params.loopUnit} onChange={(val) => updateParams({ loopUnit: val })}>
                  <Select.Option value={1000}>秒</Select.Option>
                  <Select.Option value={60000}>分</Select.Option>
                </Select>
              }
            />
          </div>
          <div className="form-item-part">
            <span className="label" style={{ marginLeft: '16px' }}>
              窗口设置：
            </span>
            <Select value={state.screenNum} onChange={(val) => stateChange({ screenNum: val })}>
              {ScreenType.map((v) => (
                <Select.Option value={v.name} key={v.name}>
                  <IconFont type={v.icon} style={{ paddingRight: 6 }} />
                  {v.name}分屏
                </Select.Option>
              ))}
            </Select>
          </div>
          <div className="form-item-part">
            <span className="check-all-box">
              全部选择：
              <Checkbox onChange={onChangeAll} checked={checkedAll} />
            </span>
          </div>
        </div>
        <div className="check-video-part">
          {state.data.map((item: boolean, index: number) => (
            <div
              className="video-item"
              key={index}
              style={{
                width: currentScreen?.width,
                height: currentScreen?.height,
              }}
            >
              <Checkbox checked={item} onChange={() => changeLoopBox(index)}>
                <div className="video-content">
                  <div className={`${list[index]?.cid ? 'has-video' : ''}`}>
                    {list[index]?.cid ? (
                      <React.Fragment>
                        <IconFont type="icon-shexiangji4" />
                        <span>正在播放视频</span>
                      </React.Fragment>
                    ) : (
                      <React.Fragment>
                        <IconFont type="lm-player-PlaySource" />
                        <span>空闲状态</span>
                      </React.Fragment>
                    )}
                  </div>
                </div>
              </Checkbox>
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
}
