import { useToggle, useUpdateEffect } from "ahooks";
import { useInfiniteScroll, useSimpleState, cache } from "@cloud-app-dev/vidc";
import { useContext, useMemo, useRef } from "react";
import { size } from "lodash-es";
import { ListItem, VideoContext, VideoItem } from "../../Context";
import DeviceHeader from "../DeviceHeader";
import { DeviceCard, DeviceListItem } from "../DeviceItem";
import { getDevices } from "../utils";
import { message } from "antd";

import "./index.less";

const PAGE_SIZE = 30;

export default function DeviceList({ isLive, getCatalog, setCatalog }: any) {
  const { params, set, list, updateParams } = useContext(VideoContext);
  console.log(getCatalog,'getCatalog')
  const [state, stateChange] = useSimpleState({
    type: undefined,
    state: undefined,
    deviceTagId: undefined,
    queryMatch: undefined as undefined | string,
    forceKey: Date.now(),
    refreshLoading: false,
  });
  const [viewMode, { toggle: viewModeToggle }] = useToggle(1, 2);
  const ref = useRef<HTMLDivElement>(null);
  const groupId = useMemo(() => params.currentTreeItem?.id ?? "", [params.currentTreeItem]);

  /**
   * 获取设备和总数
   * @param page
   * @param pageSize
   * @param oldlist
   * @returns
   */
  const getLoadMoreList = (page: number, pageSize: number, oldlist: any): Promise<{ list: ListItem[]; total: number }> => {
    const options: any = {
      buzGroupId: groupId,
      offset: (page + 1) * pageSize,
      limit: pageSize,
      type: state.type ? [state.type] : undefined,
      state: state.state,
      keywords: state.queryMatch,
      deviceTagId: state.deviceTagId,
      isPagination: true,
      sortField: "name",
      sortOrder: "desc",
    };
    if (getCatalog === "1") {
      options.originGroupId = options.buzGroupId;
      delete options.buzGroupId;
      options.searchGroupType = '2'
    }
    if (getCatalog === "2") {
      options.searchGroupType = '1'
    }
    return getDevices(options)
      .then((res) => {
        stateChange({ refreshLoading: false });
        return res;
      })
      .then((result) => ({ list: [...oldlist, ...(result?.list || [])], total: result.totalCount, totalPage: result?.totalPage }));
  };

  useUpdateEffect(() => {
    if (state.refreshLoading) {
      return undefined;
    }
    message.destroy();
    message.success("数据已更新！");
  }, [state.refreshLoading]);

  /**
   * @desc 获取设备列表
   */
  const { data = { list: [] as ListItem[] } } = useInfiniteScroll<ListItem>(
    (d) => {
      // offset 0开始
      const page = d?.list.length ? Math.ceil(d.list.length / PAGE_SIZE) - 1 : -1;
      return getLoadMoreList(page, PAGE_SIZE, d ? d?.list : []);
    },
    {
      target: ref,
      reloadDeps: [state.type, state.forceKey, state.queryMatch, state.state,state.deviceTagId, groupId, getCatalog],
      isNoMore: (d) => (d ? size(d?.list) >= +d?.total : false),
    },
  );
  const onLiveSelect = (item: ListItem, type: number = 1) => {
    const token = cache.getCache("token", "session");
    const typeStr = type === 1 ? "flv" : "m3u8";
    const url = `${window.location.origin}/api/dvia-device-server/baseDevice/video/v1/realTimeVideo/${item.cid}.${typeStr}?decryption=0&authorization=${token}`;
    const selectItem = { cid: item.cid, url, name: item.name, ptz: item.ptz === 1, type: type === 1 ? "flv" : "hls" } as VideoItem;
    const inIndex = list.findIndex((v) => v?.cid === item.cid);
    if (inIndex > -1) {
      console.warn("设备视频已打开", selectItem);
      updateParams({ screenIndex: inIndex });
    } else {
      // 未打开视频
      const index = list.findIndex((v) => !v);
      if (index === -1) {
        set(selectItem, params.screenIndex);
        // if (params.screenNum === 16) {
        //   // 16窗口开满的情况下
        //   set(selectItem, params.screenIndex);
        //   return;
        // } else {
        //   // 当前窗口已经开满了
        //   const screens = [1, 4, 6, 9, 16];
        //   const screenNumIndex = screens.indexOf(params.screenNum as number);
        //   updateParams({ screenNum: screens[screenNumIndex + 1] as any, screenIndex: list.length });
        //   set(selectItem, list.length);
        // }
      } else {
        // 当前窗口未开满,打开并选中当前窗口
        set(selectItem, index);
        updateParams({ screenIndex: index });
      }
    }
  };
  const onRecordSelect = (item: ListItem) => {
    const oldItem = params.selectDeviceItem;
    if (oldItem?.cid === item.cid) {
      updateParams({ recordDateVisible: false, selectDeviceItem: undefined });
    } else {
      updateParams({ selectDeviceItem: item, recordDateVisible: true });
    }
  };

  const onSelect = isLive ? onLiveSelect : onRecordSelect;

  const Item = useMemo(() => (viewMode === 1 ? DeviceCard : DeviceListItem), [viewMode]);

  const currentWinItem = useMemo(() => list[params.screenIndex] ?? ({} as VideoItem), [list, params.screenIndex]);

  const totalCount = useMemo(
    () => (params.currentTreeItem?.id ? params.currentTreeItem.totalCount : params.deviceCount.totalCount),
    [params.currentTreeItem, params.deviceCount.totalCount, getCatalog],
  );

  const onlineCount = useMemo(
    () => (params.currentTreeItem?.id ? params.currentTreeItem.onlineCount : params.deviceCount.onlineCount),
    [params.currentTreeItem, params.deviceCount.onlineCount, getCatalog],
  );

  useUpdateEffect(() => {
    if (!params.selectDeviceItem) {
      return undefined;
    }

    const idx = list.findIndex((v) => v?.cid === params.selectDeviceItem?.cid);
    if (idx > -1) {
      updateParams({ screenIndex: idx });
    }
  }, [params.selectDeviceItem]);

  return (
    <div className="video-device-list">
      <DeviceHeader
        viewMode={viewMode}
        viewModeToggle={viewModeToggle}
        onRefresh={() => (!state.refreshLoading ? stateChange({ forceKey: Date.now(), refreshLoading: true }) : undefined)}
        onKeywordChange={(val) => stateChange({ queryMatch: val })}
        total={totalCount}
        onlineTotal={onlineCount}
        type={state.type}
        state={state.state}
        deviceTagId={state.deviceTagId}
        onChange={stateChange}
      />
      <div className="device-items" ref={ref} style={{ padding: "0 16px" }}>
        {data?.list?.map((item: ListItem, index: number) => (
          <Item
            item={item}
            key={`${item.cid}-${index}`}
            isInplayer={list.findIndex((v) => v?.cid === item.cid) > -1}
            isSelected={!!currentWinItem.cid && currentWinItem.cid === item.cid}
            isMidSelected={!!params.selectDeviceItem && params.selectDeviceItem.cid === item.cid}
            isLive={isLive}
            onClick={(mode) => onSelect(item, mode)}
          />
        ))}
      </div>
    </div>
  );
}
