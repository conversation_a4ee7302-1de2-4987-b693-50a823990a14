import { IconFont } from "@cloud-app-dev/vidc";
import { DeviceType } from "@src/ais_app/core/device";
import { Badge, Input, Popover, Radio } from "antd";
import Service from "@src/service/system";
import { useAntdTable, useSafeState, useSize, useToggle } from "ahooks";
import useDict from "@src/ais_app/hooks/useDict";
import { useEffect, useState } from "react";
import "./index.less";

interface ITreeHeaderProps {
  onlineTotal: number;
  total: number;
  onKeywordChange: (val: string) => void;
  onRefresh: () => void;
  viewMode: number;
  viewModeToggle: () => void;
  type?: number;
  state?: number;
  deviceTagId: string;
  onChange?: (options: any) => void;
}

function DeviceHeader({
  onlineTotal,
  total,
  onRefresh,
  onKeywordChange,
  viewMode,
  viewModeToggle,
  type,
  state,
  deviceTagId,
  onChange,
}: ITreeHeaderProps) {
  const Dict7004 = useDict(["7004"]); // 字典
  const [flag, { toggle }] = useToggle(false);
  const [tagList, setTagList] = useSafeState<any[]>([]);
  const clearKeyword = () => {
    onKeywordChange("");
    toggle();
  };
  const getFetchTag = () => {
    Service.device.deviceTagList({}).then((res) => {
      setTagList(res?.data?.list || []);
    });
  };
  useEffect(() => {
    getFetchTag();
  }, []);
  return (
    <div className="video-device-header">
      <div className="left">
        {/* <IconFont type="icon-renwupeizhi_shebeirenwu_shebei-1" style={{ marginRight: 10, fontSize: 20 }} /> */}
        <span className="left-title">设备列表</span>

        <span className="device-count">
          <i>{onlineTotal}</i>/{total}
        </span>
        <span style={{ marginLeft: 8 }} onClick={onRefresh}>
          <IconFont title="刷新" type="icon-shipinchakan_shuaxin" style={{ fontSize: "16px", cursor: "pointer" }} />
        </span>
      </div>
      <div className="right">
        <span onClick={toggle}>
          <IconFont title="搜索" type="icon-renwupeizhi_shebeirenwu_sousuo" />
        </span>
        <span onClick={viewModeToggle}>
          {viewMode === 1 ? (
            <IconFont type="icon-liebiao" style={{ fontSize: "16px" }} />
          ) : (
            <IconFont type="icon-shipinchakan_kapianmoshi" style={{ fontSize: "16px" }} />
          )}
        </span>
        <span>
          <Popover
            content={
              <div className="handlecontent">
                <div className="title" style={{ marginBottom: 8 }}>
                  状态
                </div>
                <Radio.Group value={state} onChange={(v) => onChange?.({ state: v.target.value })}>
                  <Radio value={undefined}>全部</Radio>
                  <Radio value={1}>在线</Radio>
                  <Radio value={0}>离线</Radio>
                </Radio.Group>

                <div className="title" style={{ marginTop: 16, marginBottom: 8 }}>
                  类型
                </div>
                <Radio.Group value={type} onChange={(v) => onChange?.({ type: v.target.value })}>
                  <Radio value={undefined}>全部</Radio>
                  {Dict7004.map((v: any) => (
                    <Radio value={v.id} key={v.id} children={v.value} />
                  ))}
                </Radio.Group>
                {window._PLATFORM_TYPE !== 2 && (
                  <>
                    <div className="title" style={{ marginTop: 16, marginBottom: 8 }}>
                      场所标签
                    </div>
                    <Radio.Group value={deviceTagId} onChange={(v) => onChange?.({ deviceTagId: v.target.value })}>
                      <Radio value={undefined}>全部</Radio>
                      {tagList.map((v) => (
                        <Radio value={v.id} key={v.id} children={v.tagName} />
                      ))}
                    </Radio.Group>
                  </>
                )}
              </div>
            }
            trigger="click"
            placement="bottom"
            overlayClassName="video-device-params-popover"
          >
            <Badge dot={Boolean(type || state)}>
              <IconFont title="筛选" type="icon-shaixuan" style={{ fontSize: "16px", color: "rgba(0, 0, 0, 0.60)" }} />
            </Badge>
          </Popover>
        </span>
      </div>

      {flag && (
        <div className="searchinput">
          <Input
            placeholder="请输入设备名称"
            autoFocus
            style={{ fontSize: "var(--fs-small)", height: 32 }}
            onChange={(e) => onKeywordChange(e.target.value)}
            prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: "12px" }} />}
            suffix={
              <IconFont type="icon-renwupeizhi_shebeirenwu_sousuoguanbi" onClick={clearKeyword} style={{ cursor: "pointer", fontSize: "12px" }} />
            }
          />
        </div>
      )}
    </div>
  );
}

export default DeviceHeader;
