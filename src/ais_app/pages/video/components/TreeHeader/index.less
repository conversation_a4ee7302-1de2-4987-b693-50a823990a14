.video-tree-header {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 18px;
  font-size: var(--fs);
  position: relative;
  .left-title {
    color: rgba(0, 0, 0, 0.9);
    /* Title/Medium */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
  }
  
  .left,
  .right {
    display: flex;
    align-items: center;
  }
  .custom-render-box {
    height: 40px;
    line-height: 40px;
  }
  .tree-device-count {
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    padding-left: 8px;
    i {
      font-style: normal;
      color: var(--primary);
    }
  }
  .tree-device-precent {
    display: inline-block;
    margin-left: 10px;
    background-color: rgba(46, 230, 168, 0.14);
    border-radius: 13px;
    color: var(--secondary3);
    font-size: var(--fs-small);
    padding: 1px 4px;
  }
  .right {
    & > span {
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .searchinput {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--content-bg);
    padding: 10px 16px;
    z-index: 99;
  }
}
.left-title-popover{
  .ant-popover-arrow{
    display: none;
  }
  inset: 160px auto auto 23px !important;
  
  .left-title-popover-item{
    padding: 4px 8px;
    margin-bottom: 8px;
    cursor: pointer;
    &:last-child{
      margin-bottom: 0px;
    }
    &:hover{
      background: #E8F3FF;
    }
  }
  .userform-wrapper-auth-tree span.secSon > label span::after{
    top: 35%  !important;
  }
}