import { IconFont } from "@cloud-app-dev/vidc";
import { useToggle } from "ahooks";
import { Input, Select, Popover } from "antd";
import { useMemo, useRef, useState } from "react";
import { useClickAway } from 'ahooks';
import "./index.less";

interface ITreeHeaderProps {
  onlineTotal: number;
  total: number;
  setExpandAll: () => void;
  onKeywordChange: (val: string) => void;
  expandAll: boolean;
  onRefresh: () => void;
  setCatalog: (e:string) => void;
  getCatalog: string;
}
const selectOptions = [
  { name: "原始目录", key: "1" },
  { name: "自定义目录", key: "2" },
];
function TreeHeader({ onlineTotal = 0, total = 0, expandAll, onRefresh, onKeywordChange, setExpandAll, getCatalog, setCatalog }: ITreeHeaderProps) {
  const [visible, setVisible] = useState(false);
  const ref:any = useRef<HTMLButtonElement>(null);
  useClickAway(() => {
    setVisible((s) => false);
  }, ref);
  const precentTemp = useMemo(() => (onlineTotal / total) * 100, [onlineTotal, total]);
  const [flag, { toggle }] = useToggle(false);

  // 优化百分比显示
  const precent = useMemo(() => {
    const cell = Math.ceil(precentTemp || 0);
    return cell === 100 && (precentTemp || 0) < 100 ? 99 : cell;
  }, [precentTemp]);

  const clearKeyword = () => {
    onKeywordChange("");
    toggle();
  };

  return (
    <div className="video-tree-header">
      <div className="left">
        {/* <IconFont type="icon-renwupeizhi_shebeirenwu_shebeimulu" style={{ marginRight: 10, fontSize: 20 }} /> */}
        {/* <span className="left-title">系统分组</span> */}
        <Popover
          trigger="click"
          open={visible}
          placement="bottom"
          overlayClassName="left-title-popover"
          content={
            <>
              {selectOptions.map((item) => (
                <div
                  style={getCatalog === item.key ? { color: "var(--primary)", background: "#E8F3FF" } : {}}
                  key={item.key}
                  className="left-title-popover-item"
                  onClick={() => {
                    setCatalog(item.key);
                    setVisible(false)
                  }}
                >
                  {item.name}
                </div>
              ))}
            </>
          }
        >
          <div  ref={ref} onClick={()=>setVisible((s) => !s)} className="left-title" style={{ cursor: "pointer" }}>
            
            {/* 系统分组 */}
            {selectOptions.find((item) => item.key === getCatalog)?.name}
            <IconFont type={visible ? 'icon-chevron-up':"icon-chevron-down"} style={{fontSize:16,marginLeft:8}}/>
          </div>
        </Popover>
        <span className="tree-device-count">
          <i>{onlineTotal}</i>/{total}
        </span>
        <span style={{ marginLeft: 8 }} onClick={onRefresh}>
          <IconFont title="刷新" type="icon-shipinchakan_shuaxin" style={{ fontSize: "16px", cursor: "pointer" }} />
        </span>
        {/* <span className="tree-device-precent">{precent}%</span> */}
      </div>
      <div className="right">
        <span onClick={toggle}>
          <IconFont title="搜索" type="icon-renwupeizhi_shebeirenwu_sousuo" />
        </span>
        <span onClick={setExpandAll}>
          {expandAll ? (
            <IconFont type="icon-renwupeizhi_shebeirenwu_zhankaicaidan" title="收起" />
          ) : (
            <IconFont style={{ fontSize: "16px" }} type="icon-renwupeizhi_shebeirenwu_shouqicaidan" title="展开" />
          )}
        </span>
      </div>

      {flag && (
        <div className="searchinput">
          <Input
            placeholder="请输入分组名称"
            autoFocus
            style={{ fontSize: "var(--fs-small)", height: 32 }}
            onChange={(e) => onKeywordChange(e.target.value)}
            prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: "12px" }} />}
            suffix={
              <IconFont type="icon-renwupeizhi_shebeirenwu_sousuoguanbi" onClick={clearKeyword} style={{ cursor: "pointer", fontSize: "12px" }} />
            }
          />
        </div>
      )}
    </div>
  );
}

export default TreeHeader;
