import { Slider } from 'antd';
import { useContext, useMemo } from 'react';
import { IconFont, useSimpleState } from '@cloud-app-dev/vidc';
import { ptzControl } from '@src/service/ais_app';
import { VideoContext, VideoItem } from '../../Context';
import './index.less';

const DirectionType = [
  { value: 'up', icon: 'icon-renwupeizhi_lunxunrenwu_qiyong' },
  { value: 'leftup', icon: 'icon-renwupeizhi_lunxunrenwu_qiyong' },
  { value: 'left', icon: 'icon-renwupeizhi_lunxunrenwu_qiyong' },
  { value: 'leftdown', icon: 'icon-renwupeizhi_lunxunrenwu_qiyong' },
  { value: 'down', icon: 'icon-renwupeizhi_lunxunrenwu_qiyong' },
  { value: 'rightdown', icon: 'icon-renwupeizhi_lunxunrenwu_qiyong' },
  { value: 'right', icon: 'icon-renwupeizhi_lunxunrenwu_qiyong' },
  { value: 'rightup', icon: 'icon-renwupeizhi_lunxunrenwu_qiyong' },
];

const ZoomType = [
  { value: 'zoomin', icon: 'icon-shipinchakan_suofangkongzhi_jia', className: 'leftBtn' },
  { value: 'zoomout', icon: 'icon-shipinchakan_suofangkongzhi_jian', className: 'rightBtn' },
];

// const FocusType = [
//   { value: 'near', icon: 'icon-shipinchakan_jiaodiankongzhi_jia', className: 'leftBtn' },
//   { value: 'far', icon: 'icon-shipinchakan_jiaodiankongzhi_jian', className: 'rightBtn' },
// ];

export default function HolderControl() {
  const [state, stateChange] = useSimpleState({ speed: 3, visible: false });
  const { params, list } = useContext(VideoContext);
  const item = useMemo(() => list[params.screenIndex ?? 0] as VideoItem, [list, params.screenIndex]);
  const disable = useMemo(() => (list[params.screenIndex ?? 0]?.ptz ? false : true), [list, params.screenIndex]);
  const moveChange = (direction: string) => ptzControl({ direction, cid: item.cid as string, speed: state.speed });
  // const focusChange = (operation: string) => focusControl({ operation, cid: item.cid as string, speed: state.speed });
  return (
    <div className="video-holder-control-wrapper">
      <div className="header-part">
        <div className="header-left">
          {/* <div>
            <IconFont type="icon-shipinchakan_yuntaishezhi" style={{ marginRight: 10, fontSize: 20, transform: 'translateY(2px)' }} />
          </div> */}
          <div className="header-left-title">云台控制</div>
        </div>
        <div className="header-right">
          {!state.visible ? (
            <IconFont
              onClick={() => stateChange({ visible: !state.visible } as any)}
              style={{ cursor: 'pointer', transform: 'rotate(180deg)' }}
              type={'icon-shipinchakan_shouqi'}
            />
          ) : (
            <IconFont onClick={() => stateChange({ visible: !state.visible } as any)} style={{ cursor: 'pointer' }} type={'icon-shipinchakan_shouqi'} />
          )}
        </div>
      </div>
      <div className="video-holder-control" style={{ pointerEvents: disable ? 'none' : 'auto', display: state.visible ? 'block' : 'none' }}>
        {disable && <div className="disable">{disable ? '当前设备暂不支持云台控制' : '请选择设备'}</div>}
        <div className="topControl">
          <div className="leftControl">
            <div className="backcircle">
              <div className="scendcircle">
                {DirectionType.map((v, index) => (
                  <div className={`item${index + 1} item`} key={v.value} onClick={() => moveChange(v.value)}>
                    <IconFont type={v.icon} />
                  </div>
                ))}

                <div className="inItem"></div>
              </div>
            </div>
          </div>
          <div className="rightControl">
            <ul className="items">
              <li className="item">
                <div className="itemtitle">缩放控制</div>
                <div className="btnicons">
                  {ZoomType.map((v) => (
                    <div className={`${v.className} btn`} key={v.value} onClick={() => moveChange(v.value)}>
                      <IconFont type={v.icon} />
                    </div>
                  ))}
                </div>
              </li>
              {/* <li className="item">
                <div className="itemtitle">焦点控制</div>
                <div className="btnicons">
                  {FocusType.map((v) => (
                    <div className={`${v.className} btn`} key={v.value} onClick={() => focusChange(v.value)}>
                      <IconFont type={v.icon} />
                    </div>
                  ))}
                </div>
              </li> */}
              <li className="item">
                <div className="itemtitle">控制速度</div>
                <div className="btnicons">
                  <Slider defaultValue={3} max={8} style={{ width: '100%' }} onChange={(v) => stateChange({ speed: v } as any)} />
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
