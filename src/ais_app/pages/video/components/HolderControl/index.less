.video-holder-control {
  width: 100%;
  .disable {
    color: var(--warn);
    text-align: center;
  }
  .topControl {
    width: 100%;
    display: flex;
    align-items: center;
    .leftControl {
      padding: 16px;
      padding-right: 0;
      .backcircle {
        width: 126px;
        height: 126px;
        border-radius: 50%;
        background-color: #f3f3f3;
        display: flex;
        justify-content: center;
        align-items: center;
        .scendcircle {
          width: 108px;
          height: 108px;
          border-radius: 50%;
          position: relative;
          background-color: #fff;
          overflow: hidden;
          .inItem {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            right: 50%;
            transform: translateX(22px) translateY(-22px);
            background-color: #f3f3f3;
          }
          .item:hover {
            background: rgba(36, 143, 250, 0.1);
            box-shadow: inset 1px 1px 0px rgba(0, 0, 0, 0.25);
            .anticon {
              color: var(--secondary1-light);
            }
          }
          .item:hover::after {
            width: 108px;
            height: 108px;
            border: 1px solid var(--secondary1-light);
            position: absolute;
            left: 50%;
            top: 50%;
            z-index: 1;
            content: "";
            border-radius: 50%;
            transform: skewY(45deg) rotate(-67.5deg) translate(-82px, -54px);
          }
          .item {
            overflow: hidden;
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            border-left: 1px solid #f3f3f3;
            cursor: pointer;
            .anticon {
              color: #dcdcdc;
              font-size: 14px;
              transform: skewY(45deg) rotate(-67.5deg) translate(-10px, 14px);
            }
          }
          .item1 {
            transform-origin: 0% 100%;
            transform: rotate(-22.5deg) skewY(-45deg);
          }
          .item2 {
            transform-origin: 0% 100%;
            transform: rotate(-67.5deg) skewY(-45deg);
          }
          .item3 {
            transform-origin: 0% 100%;
            transform: rotate(-112.5deg) skewY(-45deg);
          }
          .item4 {
            transform-origin: 0% 100%;
            transform: rotate(-157.5deg) skewY(-45deg);
          }
          .item5 {
            transform-origin: 0% 100%;
            transform: rotate(-202.5deg) skewY(-45deg);
          }
          .item6 {
            transform-origin: 0% 100%;
            transform: rotate(-247.5deg) skewY(-45deg);
          }
          .item7 {
            transform-origin: 0% 100%;
            transform: rotate(-292.5deg) skewY(-45deg);
          }
          .item8 {
            transform-origin: 0% 100%;
            transform: rotate(-337.5deg) skewY(-45deg);
          }
        }
      }
    }
    .rightControl {
      flex: 1;
      padding-right: 16px;
      .items {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-left: 20px;
        .item {
          // display: flex;
          // align-items: center;
          padding: 5px 0px;
          width: 100%;
          margin-bottom: 12px;
          .itemtitle {
            color: rgba(0, 0, 0, 0.9);
            /* Body/Medium */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            margin-bottom: 12px;
          }
          .btnicons {
            flex: 1;
            display: flex;
            .ant-slider .ant-slider-track{
              background: var(--primary);
            }
            .ant-slider .ant-slider-handle::after{
              box-shadow: 0 0 0 2px var(--primary);
            }
            .ant-slider .ant-slider-rail{
              background: #E7E7E7;
            }
            .btn:hover {
              color: var(--secondary1-light);
            }
            .btn {
              background-color: #f3f3f3;
              width: 33px;
              height: 30px;
              cursor: pointer;
              color: rgba(0, 0, 0, 0.6);
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 16px;
              border-right: 1px solid #e0e0e0;
            }
            .leftBtn {
              border-top-left-radius: 8px;
              border-bottom-left-radius: 8px;
            }
            .rightBtn {
              border-top-right-radius: 8px;
              border-bottom-right-radius: 8px;
            }
          }
        }
      }
    }
  }
}

.video-holder-control-wrapper {
  .header-part {
    height: 40px;
    font-size: var(--fs);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    .header-left,
    .header-right {
      height: 100%;
      display: flex;
      align-items: center;
    }
    .header-left-title {
      color: rgba(0, 0, 0, 0.9);
      /* Title/Medium */
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
    }
  }
}
