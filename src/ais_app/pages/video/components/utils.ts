import { Service ,cache} from '@cloud-app-dev/vidc';
import { HttpResult } from '@src/service/interface';

export function getHeader() {
  return {
    Authorization: cache.getCache('token', 'session'),
  };
}

export const ptzControl = ({ cid, direction, speed }: { cid: string; direction: string; speed: number }): Promise<HttpResult<any>> => {
  const moveparams = { cid, cmd: 'move', control: { direction, speed } };
  return Service.http({
    headers: getHeader(),
    url: '/api/baseDevice/baseCloud/v1/ptzControlAll',
    method: 'post',
    data: moveparams,
  }).then(() => ptzStopControl({ cid, direction, speed }));
};

export const ptzStopControl = ({ cid, direction, speed }: { cid: string; direction: string; speed: number }): Promise<HttpResult<any>> => {
  const stopparams = { cid, cmd: 'stop', control: { direction, speed } };
  return Service.http({
    headers: getHeader(),
    url: '/api/baseDevice/baseCloud/v1/ptzControlAll',
    method: 'post',
    data: stopparams,
  });
};

export const focusControl = (data: { cid: string; speed: number; operation: string }): Promise<HttpResult<any>> => {
  const params = { cid: data.cid, cmd: 'manual', control: { operation: data.operation, speed: data.speed } };

  return Service.http({
    headers: getHeader(),
    url: '/api//baseDevice/baseCloud/v1/focusControl',
    method: 'post',
    data: params,
  }).then(() => focusStopControl(data));
};

export const focusStopControl = (data: { cid: string; speed: number; operation: string }): Promise<HttpResult<any>> => {
  const stopparams = { cid: data.cid, cmd: 'stop', control: { operation: data.operation, speed: data.speed } };
  return Service.http({
    headers: getHeader(),
    url: '/api//baseDevice/baseCloud/v1/focusControl',
    method: 'post',
    data: stopparams,
  });
};

export function getGroups() {
  return Service.http({
    headers: getHeader(),
    url: '/api/dvia-app-scene-server/device/deviceBuzGroup/deviceBuzGroupList/query',
  }).then((res) => res.data ?? []);
}
export function getGroupsNew() {
  return Service.http({
    headers: getHeader(),
    url: '/api/dvia-app-scene-server/device/deviceOriginGroup/list/query',
  }).then((res) => res.data ?? []);
}
export function getDevices(data: { groupId?: string; limit: number; offset: number; keywords?: string; type?: any; state?: any }) {
  return Service.http({
    headers: getHeader(),
    url: '/api/dvia-app-scene-server/device/systemDevice/deviceList/query',
    data,
    method: 'post',
  }).then((res) => res.data ?? []);
}
