import { BasicTarget, getTargetElement } from "ahooks/lib/utils/domTarget";
import { useContext, useEffect } from "react";
import { VideoContext } from "./Context";

function useScreenMove(target: BasicTarget) {
  const { list, mutSet, updateParams } = useContext(VideoContext);
  const dragStart = (event: DragEvent) => {
    const target = event.currentTarget as HTMLDivElement;
    event.dataTransfer?.setData("DATA", JSON.stringify({ sourceIndex: target.dataset.index }));
  };

  const drop = (event: DragEvent) => {
    const target = event.currentTarget as HTMLDivElement;
    const currentIndex = target.dataset.index as string;
    const data = JSON.parse(event.dataTransfer?.getData("DATA") ?? "{}");
    const sourceIndex = data.sourceIndex as string;
    if (currentIndex !== undefined && sourceIndex !== undefined) {
      // 当前获取录像中的状态不能拖拽，不限制出现数据串乱的问题
      if (!list[+sourceIndex]?.loading && !list[+currentIndex]?.loading) {
        mutSet({ [`${currentIndex}`]: list[+sourceIndex], [`${sourceIndex}`]: list[+currentIndex] });
        updateParams({ screenIndex: +currentIndex });
      }
    }
  };
  const allowDrop = (event: DragEvent) => event.preventDefault();

  useEffect(() => {
    const dom = getTargetElement(target) as HTMLDivElement;
    const playLayouts = Array.from(dom.querySelectorAll(".player-with-ext-layout")) as HTMLDivElement[];
    playLayouts.forEach((item, index) => {
      item.draggable = true;
      item.dataset["index"] = `${index}`;
      item.addEventListener("dragstart", dragStart, false);
      item.addEventListener("dragover", allowDrop, false);
      item.addEventListener("drop", drop, false);
    });
    return () => {
      playLayouts.forEach((item) => {
        item.removeEventListener("dragstart", dragStart, false);
        item.removeEventListener("dragover", allowDrop, false);
        item.removeEventListener("drop", drop, false);
      });
    };
  }, [list]);
}

export default useScreenMove;
