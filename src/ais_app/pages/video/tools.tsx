import { IRefModalMethodsProps } from "@cloud-app-dev/vidc/es/RefModal";
import Snapshot from "./components/Snapshot";
import { Button, Flex, Select, message } from "antd";

interface ISnapshotProps {
  url: string;
  modal: any;
  downloadName: string;
  cid: string;
}

export function snapshot({ url, modal, downloadName, cid }: ISnapshotProps) {
  modal.confirm({
    title: (
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
        <div>{downloadName}</div>
        {url === "data:," ? (
          <></>
        ) : (
          <Button
            type="link"
            download={`${downloadName}.png`}
            href={url}
            style={{ borderRadius: "3px", border: "1px solid var(--primary)", background: "#FFF", color: "var(--primary)" }}
          >
            下载图片
          </Button>
        )}
      </div>
    ),
    width: 765,
    icon: null,
    footer: false,
    maskClosable: true,
    content: <Snapshot url={url} downloadName={downloadName} cid={cid} onAlaramHandle={() => modal.close()} />,
    onOk() {
      modal.close();
    },
  });
}
