import { cache } from '@cloud-app-dev/vidc';
import { queryDevice } from '@src/service/ais_app';
import { useUpdateEffect } from 'ahooks';
import { message } from 'antd';
import { useContext, useMemo, useRef } from 'react';
import { ListItem, VideoContext, VideoItem } from '../Context';

function useLoop() {
  const { params, set } = useContext(VideoContext);
  const timeRef = useRef<any>(null);
  const pageNumRef = useRef<number>(1);
  const isLoop = useMemo(() => !!params.loopId, [params.loopId]);
  useUpdateEffect(() => {
    clearTimeout(timeRef.current);
    if (!params.loopId) {
      message.success('已关闭轮巡模式！');
      return undefined;
    }
    message.success('已开启轮巡模式！');
    const fn = () => {
      const pageSize = params.loopWinsIndex?.length as number;
      const options = { sortField: 'name', sortOrder: 'desc', buzGroupId: params.loopId, offset: (pageNumRef.current - 1) * pageSize, limit: pageSize, isPagination: true };
      queryDevice(options)
        .then((res) => {
          if (res.data.list.length < pageSize || res.data.list.length === res.data.totalCount) {
            pageNumRef.current = 0;
          }
          const token = cache.getCache('token', 'session');
          const listItems = res.data.list.map((item: ListItem) => {
            const url = `${window.location.origin}/api/dvia-device-server/baseDevice/video/v1/realTimeVideo/${item.cid}.flv?decryption=0&authorization=${token}`;
            return { cid: item.cid, url, name: item.name, ptz: item.ptz === 1 } as VideoItem;
          });
          params.loopWinsIndex?.forEach((winIndex, index) => {
            set(listItems[index], winIndex);
          });
        })
        .then(() => {
          timeRef.current = setTimeout(() => {
            pageNumRef.current += 1;
            fn();
          }, (params?.loopInterval ?? 0) * (params?.loopUnit ?? 0));
        });
    };
    fn();
  }, [params.loopId]);

  return isLoop;
}

export default useLoop;
