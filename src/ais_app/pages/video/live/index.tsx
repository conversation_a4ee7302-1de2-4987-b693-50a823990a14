import { useState } from "react";
import VideoLayout from '../components/VideoLayout'
import Right from './Right';
import { Provider } from '../Context';
import LeftLayout from '../components/LeftLayout';
import DeviceDir from '../components/DeviceDir';
import DeviceList from '../components/DeviceList';
import HolderControl from '../components/HolderControl';
import Authority from "@src/components/Authority";
import { useDebounceFn } from 'ahooks';
import { SocketEmitter, cache } from '@cloud-app-dev/vidc';
import './index.less';

function Live() {
  const [getCatalog, setCatalog] = useState('2');
  const { run } = useDebounceFn(
    () => {
      const _USER_SESSION_ = cache.getCache('_USER_SESSION_', 'session') || {}
      const roleId = _USER_SESSION_.roleId?.length ? _USER_SESSION_.roleId: []
      const conservator = roleId.filter((i:any) =>  i == 351305240027909)
      if(roleId.length &&  conservator.length !== 1){
        SocketEmitter.emit('clearAllPlayer');
      }
    },
    {
      wait: 1800000,
    }
  );
  return (
    <Provider>
      <div onClick={run} className="video-live-page">
        <VideoLayout leftControl={<LeftLayout topContent={<DeviceDir getCatalog={getCatalog} setCatalog={setCatalog} />} midContent={<DeviceList isLive={true} getCatalog={getCatalog} setCatalog={setCatalog} />} bottomContent={<Authority code="30000314" other={<span></span>}><HolderControl /></Authority>} />} RightContent={<Right />} />
      </div>
    </Provider>
  );
}

export default Live;
