import { IconFont, useHistory, cache, SocketEmitter } from "@cloud-app-dev/vidc";
import { RecordItem } from "@cloud-app-dev/vidc/es/ScreenPlayer/interface";
import { useMemoizedFn } from "ahooks";
import { useContext, useEffect, useRef } from "react";
import { Modal } from "antd";
import VideoLoopSetting from "../components/VideoLoopSetting";
import { VideoContext, VideoItem } from "../Context";
import useLoop from "./useLoop";
import { useLocation } from "react-router-dom";
import { snapshot as snapshotAction } from "../tools";
import RefModal , { IRefModalMethodsProps } from "@cloud-app-dev/vidc/es/RefModal";
// import Modal, { IRefModalMethodsProps } from '@cloud-app-dev/vidc/es/Modal';
import dayjs from "dayjs";
import useScreenMove from "../useScreenMove";
import LivePlayer from "../../../components/ScreenPlayer/Live";
function Right() {
  const domRef = useRef<HTMLDivElement>(null);
  const isLoop = useLoop();
  const history = useHistory();
  const { list, clear, set, params, updateParams } = useContext(VideoContext);
  SocketEmitter.on('clearAllPlayer',clear)
  const ref = useRef<IRefModalMethodsProps>(null);
  const snapshot = useMemoizedFn((url) => {
    const item = list[params.screenIndex as number] || ({} as VideoItem);
    const downloadName = `${item.name}_${dayjs().format("YYYY/MM/DD HH:mm:ss")}`;
    snapshotAction({ url, modal: Modal, downloadName, cid: item.cid as string });
  });
  const { state } = useLocation();
  useEffect(() => {
    const token = cache.getCache("token", "session");
    if (state && state.cid) {
      const url = `${window.location.origin}/api/dvia-device-server/baseDevice/video/v1/realTimeVideo/${state.cid}.flv?decryption=0&authorization=${token}`;
      const options = { name: "", type: "flv", cid: state?.cid, url } as VideoItem;
      set(options, 0);
      history.replace("/aisApp/rtVideo", {});
    }
  }, [state]);

  useScreenMove(domRef);

  return (
    <div style={{ width: "100%", height: "100%" }} ref={domRef}>
      <RefModal ref={ref} footer={false} />
      <LivePlayer
        snapshot={snapshot}
        screenNum={params.screenNum as any}
        defaultSelectIndex={params.screenIndex}
        onClose={() => set(undefined, params.screenIndex)}
        onCloseAll={clear}
        list={list as RecordItem[]}
        allWinExtTools={
          isLoop ? (
            <span className="player-tools-item" onClick={() => updateParams({ loopId: undefined, loopTreeId: undefined })}>
              <IconFont type="icon-shipinchakan_kaishilunxun" title="结束轮询" style={{ fontSize: "22px", color: `var(--success)` }} />
            </span>
          ) : (
            <></>
          )
        }
        defaultScreen={4}
        onIndexChange={(i) => updateParams({ screenIndex: i })}
        screenChange={(i) => updateParams({ screenNum: i as any })}
      />
      <VideoLoopSetting />
    </div>
  );
}

export default Right;
