import { useForm } from "antd/es/form/Form";
import Drawer from "@src/components/Drawer";
import { Form, Input, Radio } from "antd";
import { useState,useEffect } from "react";
import { IconFont } from "@cloud-app-dev/vidc";
import { isEmpty } from "lodash-es";
import "./index.less";
const FormItem = Form.Item;
const Group = Radio.Group
export default ({ isShow, toggleD, onFinish, isAdd, row }: any) => {
  const [form] = useForm();
  useEffect(()=>{
    if(!isEmpty(row)){
      form.setFieldsValue({...row})
    }else{
      form.resetFields()
    }
  },[row])
  return (
    <Drawer
      closable={false}
      open={isShow}
      width={500}
      onClose={toggleD}
      forceRender
      className="server-edit-drawer"
      onOk={()=>onFinish(form)}
      title={
        <div className="userform-wrapper-title">
          <div>{isAdd ? "注册" : "编辑"}服务</div>
          <div>
            <IconFont type="icon-guanbi" onClick={toggleD} />
          </div>
        </div>
      }
      getContainer={() => document.body}
    >
      <Form form={form} layout='vertical'>
        <FormItem name="name" label="服务名称" rules={[{ required: true, message: '请输入服务名称' }]}>
          <Input
            placeholder="请输入服务名称"
            className="user-filter-btn vidc-Input"
            allowClear={false}
            showCount maxLength={15}
          />
        </FormItem>
        <FormItem name="appKey" label="appkey"  rules={[{ required: true, message: '请输入appkey' }]}>
          <Input
            placeholder="请输入appkey"
            className="user-filter-btn vidc-Input"
            allowClear={false}
          />
        </FormItem>
        <FormItem name="appId" label="appID"  rules={[{ required: true, message: '请输入appID' }]}>
          <Input
            placeholder="请输入appID"
            className="user-filter-btn vidc-Input"
            allowClear={false}
          />
        </FormItem>
        <FormItem name="url" label="url" rules={[{ required: true, message: '请输入url地址' }]}>
          <Input
            placeholder="请输入url地址"
            className="user-filter-btn vidc-Input"
            allowClear={false}
          />
        </FormItem>
        {/* <FormItem name="platformSource" label="接入平台" rules={[{ required: true, message: '请选择接入平台' }]}>
        <Radio.Group onChange={()=>{}} value={option.platformSource}>
          <Radio value={800}>新接入</Radio>
          <Radio value={810}>国标新接入</Radio>
          </Radio.Group>
        </FormItem> */}
        
      </Form>
    </Drawer>
  );
};
