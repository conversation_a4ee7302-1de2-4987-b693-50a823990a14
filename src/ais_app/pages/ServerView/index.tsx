import { Select, Table, Switch, Button, Space, Form, message, Input, Modal } from "antd";
import { IconFont } from "@cloud-app-dev/vidc";
import type { ColumnsType } from "antd/es/table";
import { useEffect, useRef } from "react";
import { useToggle, useRequest, useSafeState, useThrottleEffect, useSize, useAntdTable } from "ahooks";
import EditDrawer from "./components/EditDrawer";
import Authority from "@src/components/Authority";
import { devicePlatform, platformPage, platformEdit, platformDelect } from "@src/service/ais_app";
import dayjs from "dayjs";
import "./index.less";
const FormItem = Form.Item;
export default () => {
  const [form] = Form.useForm();
  const ref = useRef<HTMLDivElement>(null);
  const size: any = useSize(ref);
  const [tableHeight, setTableHeight] = useSafeState<number>(0);
  const [row, setRow] = useSafeState<any>({});
  const [time, setTime] = useSafeState<any>("-");
  const [isShow, { toggle: setShow }] = useToggle(false);
  const [isAdd, { toggle: setIsAdd }] = useToggle(false);
  useThrottleEffect(
    () => {
      setTableHeight(size && size);
      // setTableHeight((size && size?.height - 128) || 0);
    },
    [size],
    {
      wait: 300,
    },
  );
  useEffect(() => {
    if (!isShow) {
      setRow(undefined);
    }
  }, [isShow]);
  const getTableData = ({ current, pageSize }: { current: number; pageSize: number }, formData: Object): Promise<any> => {
    let params: any = {
      limit: pageSize,
      offset: (current - 1) * pageSize,
      isPagination: true,
      ...formData,
    };

    return platformPage(params)
      .then((res) => {
        setTime(dayjs().format("YYYY-MM-DD HH:mm:ss"));
        return {
          list: res?.data?.list,
          total: res?.data?.totalCount,
        };
      })
      .catch((err) => {
        message.warning(err.data.message);
        return {
          list: [],
          total: 0,
        };
      });
  };
  const { tableProps, search } = useAntdTable(getTableData, {
    defaultPageSize: 10,
    form,
  });
  const searchForm = (
    <Form form={form} layout="inline" className="search-form">
      <FormItem name="state" label="服务状态">
        <Select
          placeholder="请选择"
          style={{ width: "145px" }}
          className="user-filter-btn"
          onChange={search.submit}
          allowClear={false}
          options={[
            {
              label: "全部",
              value: null,
            },
            {
              label: "启用",
              value: 1,
            },

            {
              label: "禁用",
              value: 0,
            },
          ]}
        />
      </FormItem>
      <FormItem name="name">
        <Input
          placeholder="请输入服务名称"
          className="user-filter-btn vidc-Input"
          onChange={search.submit}
          allowClear={false}
          style={{ width: "346px", borderRadius: "4px" }}
          suffix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ cursor: "pointer", fontSize: "12px" }} />}
        />
      </FormItem>
    </Form>
  );
  const columns: ColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "key",
      key: "key",
      width: 88,
      fixed: "left",
      render: (_, __, index) => index + 1,
    },
    {
      title: "服务名称",
      ellipsis: true,
      dataIndex: "name",
      width: 134,
    },
    {
      title: "服务状态",
      ellipsis: true,
      dataIndex: "state",
      width: 144,
      render: (res) => <span className={`table-server-state ${res ? "" : "table-server-state_dis"}`}>{res ? "启用" : "禁用"}</span>,
    },
    {
      title: "平台关联数量",
      ellipsis: true,
      dataIndex: "platformNum",
      width: 180,
      render: (res) => res || "-",
    },
    {
      title: "接入通道数量",
      ellipsis: true,
      dataIndex: "channelNum",
      width: 135,
    },
    {
      title: "内网IP/端口",
      ellipsis: true,
      width: 135,
      dataIndex: "url",
      render: (res) => res || "-",
    },
    {
      title: "更新时间",
      ellipsis: true,
      width: 135,
      dataIndex: "updateTime",
      render: (res) => (res ? dayjs(res).format("YYYY-MM-DD HH:mm:ss") : "-"),
    },
    {
      title: "操作",
      width: 180,
      dataIndex: "description",
      ellipsis: true,
      fixed: "right",
      render: (text: string, item: any) => (
        <Space>
          <Authority code="30000327">
            <a
              onClick={() => {
                setShow();
                setRow(item);
              }}
            >
              编辑
            </a>
          </Authority>
          {/* <a onClick={() => {}}>设备同步</a> */}
          <Authority code="30000328">
            <a
              onClick={() => {
                Modal.confirm({
                  title: "提示",
                  content: "是否删除该服务？",
                  okText: "确定",
                  cancelText: "取消",
                  onOk: () => {
                    platformDelect(item.id).then((res) => {
                      if (res.code === 0) {
                        message.success("删除成功");
                        search.submit();
                      }else{
                        message.warning(res.message);
                      }
                    },(err)=>{
                      message.warning(err.message);
                    });
                  },
                });
              }}
            >
              删除
            </a>
          </Authority>
        </Space>
      ),
    },
  ];
  const onFinish = (form: any) => {
    form.validateFields().then((res: any) => {
      const resolveHandel = row?.id ? platformEdit : devicePlatform;
      resolveHandel({ ...res, platformSource: 810 }, row?.id).then(
        (res: any) => {
          if (res.code === 0) {
            // 刷新列表
            message.success("操作成功");
            setShow();
            setRow({});
            search.submit();
          } else {
            message.warning(res.message);
          }
        },
        () => {
          message.warning("操作失败");
        },
      );
    });
  };
  const onClose = () => {
    setShow();
    setIsAdd();
    setRow(undefined);
  };
  return (
    <div className="server-wrapper">
      <div className="server-content">
        <div className="server-title">
          <div>服务管理</div>
          <div className="server-time">
            <IconFont
              onClick={() => {
                search.submit();
                message.success("刷新成功");
              }}
              type="icon-refresh"
              style={{ fontSize: 16, cursor: "pointer", marginRight: 8 }}
            />{" "}
            最后更新：{time}
          </div>
        </div>
        <div className="server-header">
          {searchForm}
          <Authority code="30000326">
            <Button
              onClick={() => {
                setShow();
                setIsAdd();
              }}
              type="primary"
            >
              注册服务
            </Button>
          </Authority>
        </div>
        <div className="server-body" ref={ref}>
          <Table
            scroll={{ y: size?.height && size.height - 120 }}
            columns={columns}
            rowKey="id"
            {...tableProps}
            // dataSource={[1, 2, 3]}
          />
        </div>
      </div>
      <EditDrawer row={row} isAdd={isAdd} toggleD={onClose} isShow={isShow} onFinish={onFinish} />
    </div>
  );
};
