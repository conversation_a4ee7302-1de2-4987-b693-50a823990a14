.server-wrapper {
  height: 100%;
  padding: 16px;
  .server-content {
    width: calc(100%);
    height: 100%;
    background: var(--content-bg);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 24px;
    display: flex;
    flex-direction: column;
    .server-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #000;
      font-family: "PingFang SC";
      font-size: 24px;
      font-style: normal;
      font-weight: 600;
      line-height: 32px;
      margin-bottom: 24px;
      .server-time {
        color: rgba(0, 0, 0, 0.6);
        text-align: center;
        /* Body/Medium */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        display: flex;
        align-items: center;
      }
    }
    .server-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .server-body {
      margin-top: 24px;
      height: 100%;
    }
  }
  .table-server-state {
    display: inline-flex;
    padding: 2px 8px;
    align-items: center;
    border-radius: 3px;
    border: 1px solid #2ba471;
    background: #e3f9e9;
    color: #2ba471;
    /* Body/Small */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
  .table-server-state_dis {
    border: 1px solid #d54941 !important;
    background: #fff0ed !important;
    color: #d54941 !important;
  }
}
