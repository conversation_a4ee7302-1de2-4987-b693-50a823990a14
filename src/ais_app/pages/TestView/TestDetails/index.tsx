import PackUp from '@src/ais_app/components/PackUp';
import { Row, Col, Tooltip } from 'antd';
import { IconFont, useHistory } from '@cloud-app-dev/vidc';
import { alarmTaskInfoById, coverImage, queryAlgorithmList } from '@src/service/ais_app';
import GradientTitle from '@src/components/GradientTitle';
import { useMemo, useRef } from 'react';
import { useRequest, useSafeState, useSize, useUpdateEffect } from 'ahooks';
import { DateToFormat, getAlgorithmNameById, TransMins, TransStatus,getDeviceNameByCode } from '@src/ais_app/utils';
import RoiImage from '@src/ais_app/components/RoiImage';
import './index.less';

interface infoType {
  label: string;
  fname: string;
  tip?: string;
}
export default function TestDetails() {
  const scollRef = useRef<HTMLElement>(null);
  const history = useHistory();
  const { data } = useRequest(alarmTaskInfoById, { defaultParams: [+history.location.search?.slice(1)] });
  const [imgUrl, setImgUrl] = useSafeState<string>('');
  const sRef = useRef<HTMLElement>(null);
  const size = useSize(sRef);
  const { data: sflist } = useRequest(() => queryAlgorithmList({ limit: 1000, offset: 0 }));
  useUpdateEffect(() => {
    coverImage(data?.cid).then((res) => {
      if (res.code === 0) {
        setImgUrl(res.data?.url);
      }
    });
  }, [data]);
  const rois = useMemo(() => {
    const { width = 0, height = 0 } = size || {};
    const Roidata = data?.configJson?.Analysis?.Rois?.map((v: any) => {
      return v?.PolygonArea?.map((m: any) => {
        return [m.X * width, m.Y * height];
      });
    });
    return Roidata || [];
  }, [size, data]);
  const baseType: infoType[] = [
    {
      label: '任务ID：',
      fname: data?.id || '-',
    },
    {
      label: '任务名称：',
      fname: data?.taskName || '-',
    },
    {
      label: '任务状态：',
      fname: TransStatus(data?.taskState) || '-',
    },
    {
      label: '创建人：',
      fname: data?.createdBy || '-',
    },
    {
      label: '创建时间：',
      fname: DateToFormat(data?.createdTime, 'YYYY-MM-DD HH:mm:ss'),
    },
    {
      label: '最后更新时间：',
      fname: DateToFormat(data?.updatedTime, 'YYYY-MM-DD HH:mm:ss'),
    },
  ];
  const deviceInfo: infoType[] = [
    {
      label: '设备名称：',
      fname: data?.deviceInfo?.name || '-',
    },
    {
      label: '设备CID：',
      fname: data?.deviceInfo?.cid || '-',
    },
    {
      label: '设备分辨率：',
      fname: data?.deviceInfo?.deviceResolving || '-',
    },
    {
      label: '设备编码格式：',
      fname: data?.deviceInfo?.deviceEncoding || '-',
    },
    {
      label: '码流大小：',
      fname: data?.deviceInfo?.deviceStreamSize || '-',
    },
    {
      label: '设备类型：',
      fname: getDeviceNameByCode(data?.deviceInfo?.type) || '-',
    },
    {
      label: '取流协议：',
      fname: data?.deviceInfo?.sdkType || '-',
    },
  ];
  const calcInfo: infoType[] = [
    {
      label: '算法名称：',
      fname: getAlgorithmNameById(sflist || [], data?.algorithmId),
    },
    {
      label: '分析频率：',
      fname: data?.configJson?.Analysis?.DetectInterval == '-1' ? 'I帧' : TransMins(data?.configJson?.Analysis?.DetectInterval) || '-',
    },
    {
      label: '场景持续时间：',
      tip: '用于定义结构化目标在画面中出现的时长，超过预设时长后，才视为一次告警事件。下拉选项包括（默认 / 10 分钟 / 15 分钟 / 30 分钟 / 60 分钟 / 自定义）',
      fname: TransMins(data?.configJson?.Analysis?.Rule[0]?.AreaRois?.MinDuration) || '-',
    },
    {
      label: '分析服务器：',
      fname: data?.configJson?.Analysis?.Rule[0]?.AreaRois?.a || '-',
    },
    {
      label: '调用GPU卡：',
      fname: data?.configJson?.Analysis?.Rule[0]?.AreaRois?.b || '-',
    },
  ];
  const higherInfo: infoType[] = [
    {
      label: '目标最小像素：',
      tip: '用于过滤尺寸较小的结构化目标。低于预设宽高的结构化目标，将被当作一次无效告警。',
      fname: `${data?.configJson?.Analysis?.Rule[0]?.AreaRois?.MinWidth ?? 0}px * ${data?.configJson?.Analysis?.Rule[0]?.AreaRois?.MinHeight ?? 0}px`,
    },
    {
      label: '目标最大像素：',
      tip: '主要为了防范因画面被遮挡导致的算法误报。高于预设宽高的结构化目标，将被当作一次无效告警。',
      fname: `${data?.configJson?.Analysis?.Rule[0]?.AreaRois?.MaxWidth ?? 0}px * ${data?.configJson?.Analysis?.Rule[0]?.AreaRois?.MaxHeight ?? 0}px`,
    },
    {
      label: '目标最小数量：',
      tip: '针对人员聚集、车辆拥堵等算法，目标数量少于预设数值，将被当作一次无效告警',
      fname: data?.configJson?.Analysis?.Rule[0]?.AreaRois?.MinObjectNumber ?? '-',
    },
    {
      label: '参考色 (RGB)：',
      fname: data?.configJson?.Analysis?.Rule[0]?.ColorRois?.DetectColor ?? '-',
      tip: '针对裸土未苫盖算法，建议选择裸土的颜色；针对水位超标算法，建议选择水位线的颜色。',
    },
    {
      label: '原图叠加边框：',
      tip: '原图叠加框	在输出的原图上，设置叠加结构化目标框或感兴趣区域框。不勾选叠加选项，对外告警时将直接输出原始图片。',
      fname: `${data?.configJson?.Output?.ImageBbox ? '叠加结构化目标框' : '-'}，${data?.configJson?.Output?.RoiBbox ? '叠加感兴趣区域' : '-'}`,
    },
  ];
  return (
    <div className="TestDetails">
      <div className="content" ref={scollRef as React.LegacyRef<HTMLDivElement>}>
        <div className="contentitem">
          <div className="base-info" id="baseInfo">
            <GradientTitle title="基本信息"></GradientTitle>
            <Row className="row-content" gutter={[0, 14]}>
              {baseType.map((v, i) => (
                <Col span={8} key={i} className="info-item">
                  <div className="label">{v.label}</div>
                  <div className="value">{v.fname}</div>
                </Col>
              ))}
            </Row>
          </div>
          <div className="device-info" id="deviceInfo">
            <GradientTitle title="设备信息"></GradientTitle>
            <Row className="row-content" gutter={[0, 14]}>
              {deviceInfo.map((v, i) => (
                <Col span={8} key={i} className="info-item">
                  <div className="label">{v.label}</div>
                  <div className="value">{v.fname}</div>
                </Col>
              ))}
            </Row>
          </div>
          <div className="calc-info" id="calcInfo">
            <GradientTitle title="算法信息"></GradientTitle>
            <div className="all-content">
              <PackUp title="感兴趣区域(ROI)">
                <div className="image-box" ref={sRef as any}>
                  <RoiImage imgUrl={imgUrl} rois={rois} />
                </div>
                <Row className="row-content" gutter={[0, 14]}>
                  {calcInfo.map((v, i) => (
                    <Col span={8} key={i} className="info-item">
                      <div className="label">{v.label}</div>
                      <div className="value">
                        {v?.tip && (
                          <Tooltip placement="top" title={v?.tip}>
                            <span>
                              <IconFont type="icon-renwupeizhi_shebeirenwu_shuoming" />
                            </span>
                          </Tooltip>
                        )}
                        {v.fname}
                      </div>
                    </Col>
                  ))}
                </Row>
              </PackUp>
              <PackUp title="高级配置">
                <Row className="row-content" gutter={[0, 14]}>
                  {higherInfo.map((v, i) => (
                    <Col span={8} key={i} className="info-item">
                      <div className="label">{v.label}</div>
                      <div className="value">
                        {v?.tip && (
                          <Tooltip placement="top" title={v?.tip}>
                            <span>
                              <IconFont type="icon-renwupeizhi_shebeirenwu_shuoming" />
                            </span>
                          </Tooltip>
                        )}
                        {v.fname}
                      </div>
                    </Col>
                  ))}
                </Row>
              </PackUp>
            </div>
          </div>
        </div>
      </div>

      {/* <div className="anchorbox">
        <Anchor getContainer={() => scollRef.current ?? window}>
          <Link href="#baseInfo" title="基本信息" />
          <Link href="#deviceInfo" title="设备信息" />
          <Link href="#calcInfo" title="算法设置" />
        </Anchor>
      </div> */}
    </div>
  );
}
