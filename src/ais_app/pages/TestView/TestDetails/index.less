.TestDetails {
  width: 100%;
  height: 100%;
  position: relative;
  color: var(--gray2);
  padding: 24px;
  overflow: auto;
  .anchorbox {
    position: absolute;
    right: 176px;
    top: 24px;
    .ant-anchor-link-title {
      color: var(--gray8);
    }
  }
  .content {
    height: max-content;
    width: 100%;
    border-radius: var(--radius3);
    background: var(--content-bg);
    display: flex;
    flex-direction: column;
    padding: 16px 24px;
    width: 80%;
    margin: 0 auto;
    min-width: 1000px;
    .contentitem {
      .base-info,
      .device-info,
      .calc-info {
        .row-content {
          padding: 16px 0;
          .info-item {
            display: flex;
            width: 100%;
            overflow: hidden;
            .label {
              width: 98px;
              text-align: end;
              text-wrap: nowrap;
              white-space: nowrap;
              color: rgba(0, 0, 0, 0.90);
            }
            .value {
              flex: 1;
              color: rgba(0, 0, 0, 0.60);
              display: flex;
              align-items: center;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              contain: strict;
              span {
                margin-right: 8px;
                color: var(--gray8);
              }
            }
          }
        }
      }
      .calc-info {
        .all-content {
          margin-top: 16px;
          .image-box {
            width: 100%;
            height: max-content;
            min-height: 400px;
            background-color: var(--gray8);
            img{
              width: 100%;
              min-height: 400px;
            }
          }
        }
      }
    }
  }
}
