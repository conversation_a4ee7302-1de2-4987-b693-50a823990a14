import LoopRecord from '../LoopRecord';
import LoopTestEmpty from '../../../../components/LoopTestEmpty';
import { queryPollingTask } from '@src/service/ais_app';
import { useToggle, useRequest, useUpdateEffect } from 'ahooks';
import { isEmpty } from 'lodash-es';
import { useHistory,cache } from '@cloud-app-dev/vidc';

export default function LoopTest() {
  const [isEmptyflag, { setRight, setLeft }] = useToggle(false);
  const history = useHistory();
  const { data } = useRequest(queryPollingTask);
  useUpdateEffect(() => {
    if (isEmpty(data)) {
      setRight();
    } else {
      setLeft();
    }
  }, [data]);
  return !isEmptyflag ? (
    <LoopRecord />
  ) : (
    <LoopTestEmpty
      onOK={() => {
        history.push('/aisApp/testConfig?formView', { edit: 2 });
      }}
      haveDesc={false}
      canEdit={cache.getCache('userInfo', 'session')?.userType == 1}
    />
  );
}
