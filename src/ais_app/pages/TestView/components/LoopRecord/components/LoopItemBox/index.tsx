import { IconFont,useInfiniteScroll } from '@cloud-app-dev/vidc';
import { DatePicker} from 'antd';
import { queryPollingExecutionList } from '@src/service/ais_app';
import GradientTitle from '@src/components/GradientTitle';
import { useRef } from 'react';
import { useSafeState } from 'ahooks';
import { size } from 'lodash-es';
import dayjs, { Dayjs } from 'dayjs';
import { DateToFormat, TransMins } from '@src/ais_app/utils';
import './index.less';

const { RangePicker } = DatePicker;
const PAGE_SIZE = 20
export default function LoopItemBox({pId,setPId}:{pId:string,setPId:(v:string)=>void}) {
  const ref = useRef<HTMLDivElement>(null);
  const [date,setDate] = useSafeState<Dayjs[]>([])
  const [loopInfo,setLoopInfo] = useSafeState<{averageTime:number,executionSum:number}>({averageTime:0,executionSum:0})
  const getLoadMoreList = (page: number, pageSize: number, oldlist: any): Promise<{ list: any[]; total: number }> => {
    const options :any = {
      offset: (page + 1) * pageSize,
      limit: pageSize
    };
    if(date){
      options.endTime= date[1]?.valueOf()
      options.startTime= date[0]?.valueOf()
    }
    return queryPollingExecutionList(options).then((result) => {
      setLoopInfo({
        averageTime:result?.data?.averageTime,
        executionSum:result?.data?.executionSum
      })
      const arr = result?.data?.pageList?.records || []
      const data = { list: [...oldlist, ...arr], total: result.data?.pageList?.total }
      setPId(data?.list[0]?.id)
      return data
    });
  };

  const { data = { list: [] } } = useInfiniteScroll<any>(
    (d) => {
      const page = d?.list.length ? Math.ceil(d.list.length / PAGE_SIZE) - 1 : -1;
      return getLoadMoreList(page, PAGE_SIZE, d ? d?.list : []);
    },
    {
      target: ref,
      reloadDeps:[date],
      isNoMore: (d) => (d ? size(d?.list) === +d?.total : false),
    }
  );
  return (
    <div className="loop-item-box">
        <div className="filter-box">
          <GradientTitle title="轮巡记录" margin="0 0 12px" />
          <RangePicker onChange={(v)=>{setDate(v as Dayjs[])}}/>
          <p className="title">轮巡记录表</p>
          <div className="static">
            <div>
              共轮巡：<span>{loopInfo?.executionSum ?? '-'}</span> 次
            </div>
            <div>平均耗时：{TransMins(+loopInfo?.averageTime/1000 >>> 0,false) || '-'}</div>
          </div>
        </div>
        <div className="items" ref={ref}>
          {data?.list?.map((v, i) => (
            <div key={i} className={`item ${pId === v?.id && 'selected'}`} onClick={()=>{setPId(v?.id)}}>
              <div className="header">
                <div className="icon" style={{ paddingRight: 8 }}>
                  <IconFont style={{color:'var(--primary)'}} type="icon-renwuchakan_lunxunrenwu_rili" />
                </div>
                <div className="name">{`${DateToFormat(v?.startTime,'YYYY-MM-DD HH:mm:ss')}—${DateToFormat(v?.endTime,'HH:mm:ss')}`}</div>
              </div>
              <div className="bot">
                <div className="iconbox">
                  <IconFont type="icon-renwuchakan_lunxunrenwu_chenggong" /> {v?.successSum??'-'}
                </div>
                <div className="iconbox">
                  <IconFont type="icon-renwuchakan_lunxunrenwu_shibai-1" /> {v?.failSum ?? '-'}
                </div>
                <div className="iconbox">
                  <IconFont type="icon-shipinchakan_shoudonggaojing" /> {v?.alarmSum ?? '-'}
                </div>
                <div className="iconbox">
                  <IconFont type="icon-gaojingzhongxin_shijian" /> {TransMins(dayjs(v?.endTime).diff(dayjs(v?.startTime),'seconds'),false) || '-'}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
  );
}
