.loop-item-box {
    width: 320px;
    height: 100%;
    padding: 16px 0px;
    border-radius: var(--radius3);
    background: var(--content-bg);
    color: var(--gray2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .items {
      margin-top: 12px;
      padding: 0 16px;
      width: 100%;
      flex: 1;
      overflow: auto;
      .item ~ .item {
        margin-top: 14px;
      }
      .item:hover {
        border: 2px solid var(--secondary2);
        background-color: #E8F3FF;
        padding: 9px 7px;
      }
      .item.selected{
        border: 2px solid var(--secondary2);
        background-color: #E8F3FF;
        padding: 9px 7px;
      }
      .item {
        cursor: pointer;
        height: 70px;
        border: 1px solid rgba(255, 255, 255, 0.35);
        border-radius: var(--radius3);
        padding: 10px 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .header {
          display: flex;
          align-items: center;
          font-weight: 600;
        }
        .bot {
          display: flex;
          align-items: center;
          font-size: var(--fs-small);
          font-weight: 400;
          .iconbox {
            white-space: nowrap;
            font-size: 14px;
            margin-right: 4px;
          }
          .iconbox ~ .iconbox {
            margin-left: 30px;
          }
        }
      }
    }
    .filter-box {
      padding: 0 16px;
      .title {
        margin: 14px 0 12px;
        font-size: var(--fs-large);
        font-weight: 600;
      }
      .ant-picker{
        width: 100%;
      }
      .static {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: var(--fs);
        span {
          color: var(--secondary2);
        }
      }
    }
  }