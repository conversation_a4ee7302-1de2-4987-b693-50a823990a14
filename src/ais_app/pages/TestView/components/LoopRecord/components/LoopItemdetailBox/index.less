.loop-itemdetail-box {
    flex: 1;
    height: 100%;
    margin: 0 16px;
    padding: 16px 0px;
    border-radius: var(--radius3);
    background: var(--content-bg);
    display: flex;
    flex-direction: column;
    .filter-box {
      margin-top: 16px;
      display: flex;
      padding: 0 24px;
      justify-content: space-between;
      color: var(--gray2);
      .left {
        display: flex;
        align-items: center;
        .scenes {
          display: flex;
          align-items: center;
          .currentItem.sceneItem {
            border: 1px solid var(--secondary2);
            color: var(--secondary2);
          }
          .sceneItem ~ .sceneItem {
            margin-left: 8px;
          }
          .sceneItem {
            padding: 6px 18px;
            box-sizing: border-box;
            border: 1px solid var(--gray7);
            border-radius: var(--radius1);
            color: var(--gray2);
            font-size: var(--fs-small);
            cursor: pointer;
          }
        }
      }
      .right {
      }
    }
    .content {
      width: 100%;
      flex: 1;
      .item:hover {
        background: #E8F3FF;
        border: 2px solid var(--secondary2);
        padding: 11px 7px;
      }
      .selected.item{
        background: #E8F3FF;
        border: 2px solid var(--secondary2);
        padding: 11px 7px;
      }
      .item {
        width: 100%;
        height: 100%;
        border: 1px solid rgba(255, 255, 255, 0.35);
        border-radius: 6px;
        color: var(--gray2);
        padding: 12px 8px;
        position: relative;
        .flagalarm{
          position: absolute;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background-color: var(--danger);
          display: flex;
          align-items: center;
          justify-content: center;
          left: -12px;
          top: -12px;
          font-size: 14px;
          .anticon{
            position: relative;
            left: -1px;
          }
        }
        cursor: pointer;
        .static-box {
          width: 100%;
          height: 60px;
          background-color: rgba(255, 255, 255, 0.1);
          display: flex;
          align-items: center;
          padding: 6px 4px;
          justify-content: space-around;
          .line {
            width: 1px;
            height: 20px;
            background: rgba(255, 255, 255, 0.35);
          }
          .infoitem {
            display: flex;
            .icon-box {
              margin-top: 5px;
              margin-right: 6px;
              .sucicon {
                background: var(--success-light);
                width: 6px;
                height: 6px;
                border-radius: 50%;
              }
              .erricon {
                background: var(--danger-light);
                width: 6px;
                height: 6px;
                border-radius: 50%;
              }
            }
            .content-box {
              .lable {
                font-size: var(--fs-small);
                font-weight: 500;
                color: var(--gray7);
                display: flex;
                align-items: center;
              }
              .value {
                font-size: var(--fs);
                font-weight: 500;
                color: var(--gray2);
                span {
                  color: var(--gray7);
                }
              }
            }
          }
        }
        .name-box {
          margin-bottom: 10px;
          display: flex;
          align-items: center;
          .name {
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-weight: 600;
          }
          .icon {
            width: 14px;
            height: 14px;
            margin-left: 6px;
            position: relative;
            bottom: 4px;
          }
        }
      }
    }
  }