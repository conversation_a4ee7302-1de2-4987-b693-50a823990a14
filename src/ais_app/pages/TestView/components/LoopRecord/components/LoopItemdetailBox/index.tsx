import { Icon<PERSON>ont,  DynamicGridList } from '@cloud-app-dev/vidc';
import { queryPollingDeviceInfoList } from '@src/service/ais_app';
import { Row,Col ,Input} from 'antd';
import { useMemo } from 'react';
import GradientTitle from '@src/components/GradientTitle';

import { useSafeState } from 'ahooks';
import './index.less';

const sceneArr = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '成功',
    value: '1',
  },
  {
    label: '失败',
    value: '0',
  },
];
export default function LoopItemdetailBox({ setCurrentItem, currentItem, pId }: { pId: string; currentItem: any; setCurrentItem: (v: any) => void }) {
  const [params, setParams] = useSafeState({ keyWords: '', successFlag: '' });
  const loadPage = async (d: any): Promise<any> => {
    const res = await queryPollingDeviceInfoList({ pollingExecutionId: pId, limit: 40, offset: d?.page * 40 || 0, ...params });
    const resultData = res?.data?.list;
    d.total = res?.data?.totalCount;
    d.page ? d.page++ : (d.page = 1);
    d.list ? (d.list = [...d.list, ...resultData]) : (d.list = resultData);
    setCurrentItem(d?.list[0]);
    return new Promise((resolve) => {
      setTimeout(() => resolve(d), 500);
    });
  };

  const reloadDeps = useMemo(() => [params, pId], [params, pId]);
  return (
    <div className="loop-itemdetail-box">
      <GradientTitle title="轮巡记录详情" margin="0 24px" />
      <Row className="filter-box" gutter={[16,16]}>
        <Col className="left" xxl={16} xl={24}>
          <div className="lable">分析状态：</div>
          <div className="scenes">
            {sceneArr?.map((v, i) => (
              <div
                className={`sceneItem ${v?.value === params?.successFlag && 'currentItem'}`}
                key={i}
                onClick={() => {
                  setParams((old) => ({ ...old, successFlag: v?.value }));
                }}
              >
                {v?.label}
              </div>
            ))}
          </div>
        </Col>
        <Col className="right" xxl={8} xl={24}>
          <Input
            placeholder="请输入设备名称/IP"
            className="vidc-Input"
            allowClear={false}
            suffix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" />}
            onChange={(v) => {
              setParams((old) => ({ ...old, keyWords: v.target.value }));
            }}
          />
        </Col>
      </Row>
      <div className="content">
        {pId !== '' && (
          <DynamicGridList
            itemKey="id"
            itemHeight={112}
            itemWidth={234}
            loadPage={loadPage}
            reloadDeps={reloadDeps}
            isNoMore={(data) => data?.list?.length === data?.total}
            renderItem={(item: any, index: any) => {
              return (
                <div
                  className={`item ${item?.id === currentItem?.id && 'selected'}`}
                  key={item.id + '_' + index}
                  onClick={() => {
                    setCurrentItem(item);
                  }}
                >
                  {item?.alarmFlag ? (
                    <div className="flagalarm">
                      <IconFont style={{ color: '#fff' }} type="icon-renwuchakan_lunxunrenwu_gaojingcishu" />
                    </div>
                  ) : (
                    <></>
                  )}
                  <div className="name-box">
                    <div className="name">{item?.deviceName || '-'}</div>
                    <div className="icon">
                      {item?.successFlag ? (
                        <IconFont style={{ color: 'var(--success)' }} type="icon-renwuchakan_lunxunrenwu_yiwancheng" />
                      ) : (
                        <IconFont style={{ color: 'var(--danger)' }} type="icon-renwuchakan_lunxunrenwu_shibai" />
                      )}
                    </div>
                  </div>
                  <div className="static-box">
                    <div className="calc infoitem">
                      <div className="content-box">
                        <div className="lable">检测算法</div>
                        <div className="value">
                          {item?.analysisAllSum ?? 0} <span>个</span>{' '}
                        </div>
                      </div>
                    </div>
                    <div className="line"></div>
                    <div className="success infoitem">
                      <div className="icon-box">
                        <div className="sucicon"></div>
                      </div>
                      <div className="content-box">
                        <div className="lable">
                          <div className="sucicon"></div> 成功
                        </div>
                        <div className="value">
                          {item?.analysisSuccessSum ?? 0} <span>个</span>{' '}
                        </div>
                      </div>
                    </div>
                    <div className="line"></div>
                    <div className="error infoitem">
                      <div className="icon-box">
                        <div className="erricon"></div>
                      </div>
                      <div className="content-box">
                        <div className="lable">失败</div>
                        <div className="value">
                          {item?.analysisFailSum ?? 0} <span>个</span>{' '}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            }}
          />
        )}
      </div>
    </div>
  );
}
