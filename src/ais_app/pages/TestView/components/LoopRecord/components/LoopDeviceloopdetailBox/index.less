.loop-deviceloopdetail-box {
    width: 492px;
    height: 100%;
    padding: 16px 0;
    border-radius: var(--radius3);
    background: var(--content-bg);
    .device-loop-detaillist {
      height: calc(100% - 48px);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      color: var(--gray2);
      .name,
      .time,
      .result,
      .alarm {
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 80px;
      }
      .name {
        width: 145px;
      }
      .time {
        width: 146px;
      }
      .result {
        width: 70px;
      }
      .alarm {

      }
      .header {
        margin: 16px 16px 0;
        height: 38px;
        background: rgba(29, 187, 255, 0.2);
        border: 1px dashed var(--primary-light);
        display: flex;
        align-items: center;
        .theader {
          font-size: var(--fs-small);
          font-weight: 600;
        }
      }
      .content {
        width: 100%;
        flex: 1;
        padding: 0 16px;
        overflow: auto;
        .item {
          display: flex;
          align-items: center;
          height: 40px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }
      }
    }
  }