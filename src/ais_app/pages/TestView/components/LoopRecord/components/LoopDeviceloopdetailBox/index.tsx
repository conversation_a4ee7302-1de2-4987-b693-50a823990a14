import { queryPollingAlgorithmInfoList, queryAlgorithmList } from '@src/service/ais_app';
import GradientTitle from '@src/components/GradientTitle';
import { IconFont, useInfiniteScroll } from '@cloud-app-dev/vidc';
import './index.less';
import { useRequest } from 'ahooks';
import { useRef } from 'react';
import { size } from 'lodash-es';
import { DateToFormat, getAlgorithmNameById } from '@src/ais_app/utils';
const PAGE_SIZE = 20;
export default function LoopDeviceloopdetailBox({ currentItem, pId }: { currentItem: any; pId: string }) {
  const { data: sflist } = useRequest(() => queryAlgorithmList({ limit: 1000, offset: 0 }));
  const ref = useRef<HTMLDivElement>(null);
  const getLoadMoreList = (page: number, pageSize: number, oldlist: any): Promise<{ list: any[]; total: number }> => {
    const options = {
      offset: (page + 1) * pageSize,
      limit: pageSize,
      cid: currentItem?.cid,
      pollingExecutionId: pId,
    };
    if (!currentItem || pId === '') {
      return Promise.resolve({ list: [], total: 0 });
    }
    return queryPollingAlgorithmInfoList(options).then((result) => ({ list: [...oldlist, ...result?.data.list], total: result.data?.pageList?.total }));
  };

  const { data = { list: [] } } = useInfiniteScroll<any>(
    (d) => {
      const page = d?.list.length ? Math.ceil(d.list.length / PAGE_SIZE) - 1 : -1;
      return getLoadMoreList(page, PAGE_SIZE, d ? d?.list : []);
    },
    {
      target: ref,
      reloadDeps: [currentItem, pId],
      isNoMore: (d) => (d ? size(d?.list) === +d?.total : false),
    }
  );
  return (
    <div className="loop-deviceloopdetail-box">
      <GradientTitle title="设备轮巡详情" margin="0 16px" />
      <div className="device-loop-detaillist">
        <div className="header">
          <div className="name theader">算法名称</div>
          <div className="result theader">分析结果</div>
          <div className="time theader">监测时间</div>
          <div className="alarm theader">是否产生报警</div>
        </div>
        <div className="content" ref={ref}>
          {data?.list?.map((v, i) => (
            <div className="item" key={i}>
              <div className="name" title={getAlgorithmNameById(sflist || [], v?.algorithmId)}>
                {getAlgorithmNameById(sflist || [], v?.algorithmId)}
              </div>
              <div className="result" title={`${v?.analysisResult === 1 ? '分析失败' : v?.analysisResult === 2 ? '成功' : '拉流失败'}`}>
                {v?.analysisResult !== 2 && <IconFont style={{ color: 'var(--danger)' }} type="icon-renwuchakan_shebeirenwu_shibai" />}{' '}
                {v?.analysisResult === 1 ? '分析失败' : v?.analysisResult === 2 ? '成功' : '拉流失败'}
              </div>
              <div className="time">{DateToFormat(v?.analysisTime, 'YYYY-MM-DD HH:mm:ss')}</div>
              <div className="alarm">{v?.alarmFlag ? '是' : '否'}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
