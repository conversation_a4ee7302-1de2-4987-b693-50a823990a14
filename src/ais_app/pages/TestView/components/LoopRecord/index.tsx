import './index.less';
import LoopItemBox from './components/LoopItemBox';
import LoopItemdetailBox from './components/LoopItemdetailBox';
import { useSafeState } from 'ahooks';
import LoopDeviceloopdetailBox from './components/LoopDeviceloopdetailBox';
export default function LoopRecord() {
  const [pId,setPId] = useSafeState<string>('')
  const [currentItem,setCurrentItem] = useSafeState()
  return (
    <div className="LoopRecord">
      <LoopItemBox pId={pId} setPId={setPId} />
      <LoopItemdetailBox currentItem={currentItem} setCurrentItem={setCurrentItem} pId={pId}/>
      <LoopDeviceloopdetailBox pId={pId} currentItem={currentItem}/>
    </div>
  );
}
