import LoopTestEmpty from "@src/ais_app/components/LoopTestEmpty";
import DeviceTestLayout from "../DeviceTestLayout";
import { queryAisAlarmTaskList } from "@src/service/ais_app";
import { useMount, useToggle, useSafeState } from "ahooks";
import { Tabs } from "antd";
import { size } from "lodash-es";
import { useHistory } from "@cloud-app-dev/vidc";
import { cache } from "@cloud-app-dev/vidc";
import Disposition from "./components/Disposition";
import NoDisposition from "./components/NoDisposition";
import EditDrawer from "./components/EditDrawer";
import TaskModle from "./components/TaskModle";
import { Provider } from "./Context";
import "./index.less";

export default function DeviceTestRight({ params }: { params: any }) {
  // const [isEmpty, { setLeft, setRight }] = useToggle(false);
  const [isEditc, { toggle: setIsEdit, setRight: setIsEditT }] = useToggle(false);
  const [refsh, { toggle: rToggle }] = useToggle(false);
  const [visible, { toggle: setVisible }] = useToggle(false);
  const [isEditAdd, { toggle: setIsEditAdd, setRight: setIsEditAddT, setLeft: setIsEditAddF }] = useToggle(true);
  const [isEdit, { toggle: SetIsEdit, setRight: SetIsEditT, setLeft: SetIsEditF }] = useToggle(false);
  const [getEditRow, setEditRow] = useSafeState({});
  const [selectItems, setSelectItems]: any = useSafeState([]);
  //
  // useMount(() => {
  //   queryAisAlarmTaskList({ limit: 1000, offset: 1 }).then((res) => (size(res?.list) ? setLeft() : setRight()));
  // });
  const handleSubmit = (e: any, tabKey: any) => {
    if (tabKey === "2") {
      setEditRow({ ...getEditRow, name: "批量任务配置" });
      setIsEdit();
      setVisible();
    } else {
      console.log(selectItems, "selectItems2aws");
      setEditRow({ ...selectItems[0] });
      setIsEdit();
      setVisible();
      SetIsEditT();
    }
    
    // setVisible();
  };
  return (
    <Provider>
      <div className="DeviceTestRight">
        <div className="DeviceTestRight-title">设备任务管理</div>
        <Tabs
          defaultActiveKey="1"
          items={[
            {
              key: "1",
              label: "已配置",
              children: (
                <Disposition
                  setVisible={setVisible}
                  rToggle={rToggle}
                  refsh={refsh}
                  setIsEditAdd={setIsEditAddT}
                  setIsEdit={setIsEdit}
                  setRow={setEditRow}
                  params={params}
                />
              ),
            },
            {
              key: "2",
              label: "未配置",
              children: (
                <NoDisposition setVisible={setVisible} setIsEditAdd={setIsEditAddF} setIsEdit={setIsEdit} setRow={setEditRow} params={params} />
              ),
            },
          ]}
          onChange={() => {}}
        />
        {isEditc ? (
          <EditDrawer
            setVisible={setVisible}
            isEdit={isEdit}
            SetIsEdit={SetIsEdit}
            SetIsEditT={SetIsEditT}
            rToggleP={rToggle}
            isEditAdd={isEditAdd}
            isShow={isEditc}
            toggleD={() => {
              setIsEdit();
              SetIsEditF()
            }}
            getEditRow={getEditRow}
          />
        ) : null}
        {visible ? (
          <TaskModle
            onSelectChange={(v: any) => {
              setSelectItems([v[v.length - 1]]);
            }}
            selectItems={selectItems?.filter((v: any) => v)?.map((v: any) => v.cid) || []}
            visible={visible}
            handleSubmit={handleSubmit}
            onCancel={setVisible}
          />
        ) : null}
        {/* {!isEmpty ? (
        <DeviceTestLayout params={params}/>
      ) : (
        <LoopTestEmpty
          mode="设备"
          onOK={() => {
            history.push('/aisApp/testConfig');
          }}
          haveDesc={false}
          canEdit={cache.getCache('userInfo', 'session')?.userType == 1}
        />
      )} */}
      </div>
    </Provider>
  );
}
