import { useRequest, useUpdateEffect } from "ahooks";
import { Checkbox, Tree, Input, Tooltip } from "antd";
import { useMemo, useState, useEffect } from "react";
import { getDevices, getGroups, getDevices1 } from "./utils";
import { IconFont, List, useSimpleState, DynamicList } from "@cloud-app-dev/vidc";
import { getTreeIdWithKeyword, getTreeMap } from "@src/ais_app/pages/video/components/DeviceDir/utils";
import { ITreeItem, ListItem } from "@src/ais_app/pages/video/Context";
import { treeHelper } from "@cloud-app-dev/vidc";
import DeviceIcon from "@src/ais_app/components/DeviceIcon";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { cloneDeep, size, xor } from "lodash-es";
import { Segmented } from "antd";
import Service from "@src/service/system";
import "./index.less";

interface IDeviceSelectProps {
  selectItems?: ListItem[];
  onSelectChange?: (list: ListItem[]) => void;
  isNoSelect?: boolean;
  labelDis?: boolean;
}
const selectTreeOption = [
  { label: "设备分组", value: 1 },
  { label: "场所标签", value: 2 },
];
function DeviceSelect({ selectItems, onSelectChange, isNoSelect, labelDis }: IDeviceSelectProps) {
  const [list1, setList1] = useState([]);
  const [deviceTagList, setDeviceTagList] = useState([]);
  const [segmetedValue, setSegmentedValue] = useState(1);
  const [state, updateState, setState] = useSimpleState({
    expandedKeys: [] as string[],
    groupId: undefined as string | undefined,
    keyword: "" as string,
    selectItems: selectItems || ([] as any[]),
  });
  const { data: treeRes = [] } = useRequest<ITreeItem[], any>(() => getGroups());
  const treeDataTemp = useMemo(() => treeHelper.computTreeList(treeRes), [treeRes]);
  // 树数据处理，高亮关键字
  const treeData = useMemo(() => {
    const loop = (data: ITreeItem[]): ITreeItem[] =>
      data.map((item: any) => {
        const strName = item.groupName as string;
        const index = strName.indexOf(state.keyword);
        const beforeStr = strName.substring(0, index);
        const afterStr = strName.slice(index + state.keyword.length);
        const name =
          index > -1 ? (
            <span>
              {beforeStr}
              <span className="site-tree-search-value">{state.keyword}</span>
              {afterStr}
            </span>
          ) : (
            <span>{strName}</span>
          );
        const title = <div className="title">{name}</div>;

        if (item.children) {
          return { ...item, name: title, code: item.code, children: loop(item.children) };
        }

        return { ...item, name: title, code: item.code };
      });

    return loop(treeDataTemp);
  }, [state.keyword, treeDataTemp]);
  const treeData1 = useMemo(() => {
    const loop = (data: ITreeItem[]): ITreeItem[] =>
      data.map((item: any) => {
        const strName = item.tagName as string;
        const index = strName.indexOf(state.keyword);
        const beforeStr = strName.substring(0, index);
        const afterStr = strName.slice(index + state.keyword.length);
        const name =
          index > -1 ? (
            <span>
              {beforeStr}
              <span className="site-tree-search-value">{state.keyword}</span>
              {afterStr}
            </span>
          ) : (
            <span>{strName}</span>
          );
        const title = <div className="title">{name}</div>;

        if (item.children) {
          return { ...item, name: title, code: item.id, children: loop(item.children) };
        }

        return { ...item, name: title, code: item.id };
      });

    return loop(deviceTagList);
  }, [state.keyword, deviceTagList]);
  useEffect(() => {
    if (list1?.length && state.selectItems?.length) {
      setState((old: any) => {
        return {
          ...old,
          selectItems: old?.selectItems?.map((i: any) => {
            if (i.cid) {
              return i;
            }
            const row = list1.find((ic: any) => ic.cid == i) || {};
            return row;
          }),
        };
      });
    }
  }, [list1]);
  const onSelectItem = (items: ListItem[], type?: boolean) => {
    // console.log(items, type, isNoSelect, "type");
    if (isNoSelect) {
      const item = items[0];
      return updateState({ selectItems: [item] });
    }
    if (type) {
      updateState({ selectItems: [items[0]] });
    } else {
      const item = items[0];
      setState((old) => {
        let arr: any = cloneDeep(old.selectItems).filter((v: any) => v);
        const inSelectIndex = arr.findIndex((v: any) => v?.cid === item?.cid);
        if (inSelectIndex > -1) {
          arr.splice(inSelectIndex, 1);
        } else {
          arr.push(item);
        }
        return { ...old, selectItems: [...arr] };
      });
    }
  };
  const onSelectItem1 = (items: ListItem[], type?: boolean) => {
    const item = items[0];
    if (item) {
      setState((old) => {
        let arr: any = cloneDeep(old.selectItems).filter((v: any) => v);
        const inSelectIndex = arr.findIndex((v: any) => v?.cid === item?.cid);
        if (inSelectIndex > -1) {
          arr.splice(inSelectIndex, 1);
        } else {
          arr.push(item);
        }
        return { ...old, selectItems: [...arr] };
      });
    }else{
      return updateState({ selectItems: [] });
    }
  };
  useEffect(() => {
    Service.device.deviceTagList({}).then((res) => {
      setDeviceTagList(res?.data?.list || []);
    });
  }, []);
  useEffect(() => {
    onSelectChange?.(state.selectItems);
  }, [state.selectItems]);
  const treeMap = useMemo(() => getTreeMap(treeRes), [treeRes]);
  // 树搜索处理,匹配结果展开
  const onFilterChange = (name: string) => {
    const codes = getTreeIdWithKeyword(treeMap, name);
    updateState({ keyword: name, expandedKeys: codes });
  };
  const onDeleteAll = () => {
    updateState({ selectItems: [] });
  };
  return (
    <div className="device-select">
      <div className="org-tree-select select-content-part">
        <div
          className="tree-title"
          style={{
            display: "flex",
            width: "100%",
            alignContent: "center",
            justifyContent: labelDis ? "start" : "center",
            padding: "16px",
            height: "54px",
          }}
        >
          {labelDis ? (
            <span>
              {/* <IconFont type="icon-renwupeizhi_shebeirenwu_shebeimulu" style={{ fontSize: 20, position: 'relative', top: 2 }} /> */}
              <span>系统分组</span>
            </span>
          ) : (
            <Segmented
              options={selectTreeOption}
              value={segmetedValue}
              onChange={(e: any) => {
                updateState({
                  expandedKeys: [] as string[],
                  groupId: undefined as string | undefined,
                  keyword: "" as string,
                  selectItems: [],
                });
                setSegmentedValue(e);
              }}
              block
            />
          )}

          {/* <Checkbox>全选</Checkbox> */}
        </div>
        <div className="tree-content">
          <div className="search-box">
            <Input
              prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: "12px" }} />}
              placeholder="请输入分组名称"
              onChange={(v) => onFilterChange(v.target.value)}
            />
          </div>
          <Tree
            treeData={segmetedValue == 1 ? treeData : treeData1}
            switcherIcon={<IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" style={{ transform: "rotate(90deg)" }} />}
            fieldNames={{ title: "name", key: "id" }}
            onExpand={(expandedKeys) => updateState({ expandedKeys } as any)}
            onSelect={(treeKeys) => updateState({ groupId: treeKeys[0] as string })}
            expandedKeys={state.expandedKeys}
            selectedKeys={[state.groupId as string]}
            titleRender={(item) => {
              return <span className="ant-tree-title1111" style={{ width: '100%' }}><Tooltip title={item.name}>{item.name}</Tooltip></span>;
            }}
          />
        </div>
      </div>
      {isNoSelect ? (
        // <div style={{ width: 24 }}></div>
        <DeviceList1
          segmetedValue={segmetedValue}
          isNoSelect={isNoSelect}
          groupId={state.groupId}
          onCheck={onSelectItem}
          selectItems={state.selectItems}
        />
      ) : (
        <>
          <DeviceList1
            segmetedValue={segmetedValue}
            list1={list1}
            setList1={setList1}
            groupId={state.groupId}
            onCheck={onSelectItem1}
            selectItems={state.selectItems}
          />
          <DeviceList2 onDeleteAll={onDeleteAll} list1={list1} list={state.selectItems} className="devive-assign-list" onDelete={onSelectItem} />
        </>
      )}
    </div>
  );
}

interface Device1ListProps {
  groupId?: string;
  className?: string;
  onCheck: (items: ListItem[], type?: boolean) => void;
  selectItems?: ListItem[];
  isNoSelect?: boolean;
  setList1?: any;
  segmetedValue: any;
  list1?: any;
}

function DeviceList1({ list1 = [], segmetedValue, groupId, className = "", onCheck, selectItems = [], isNoSelect, setList1 }: Device1ListProps) {
  const [state, updateState] = useSimpleState({ keywords: "", total: "", list: [] as any });
  /**
   * 获取设备和总数
   * @param page
   * @param pageSize
   * @param oldlist
   * @returns
   */
  const getLoadMoreList = (page: number, pageSize: number, oldlist: any): Promise<{ list: ListItem[]; total: number }> => {
    const options: any = {
      buzGroupId: groupId,
      offset: (page + 1) * pageSize,
      limit: pageSize,
      keywords: state.keywords,
      isPagination: true,
      sortField: "name",
      sortOrder: "desc",
    };
    if (segmetedValue == 2) {
      options.deviceTagId = groupId;
      delete options.buzGroupId;
    }
    const resloveP = segmetedValue == 2 ? getDevices1 : getDevices;
    return resloveP(options).then((result) => {
      updateState({ list: [...oldlist, ...result.list], total: result.totalCount });
      // updateState({ list: [...oldlist, ...result.list]?.map((v) => v.cid), total: result.totalCount });
      setList1 && setList1([...oldlist, ...result?.list]);
      return { list: [...oldlist, ...result.list], total: result.totalCount, totalPage: result.totalPage, count: result.totalCount };
    });
  };

  const selectKeys = useMemo(() => {
    if (isNoSelect) {
      return selectItems?.filter((v: any) => v)?.map((item: any) => item.cid) || [];
    }
    // return selectItems;
    return selectItems?.filter((v: any) => v);
    // ?.map((item: any) => {
    //   if (item.cid) {
    //     return item.cid;
    //   } else {
    //     const row = list1.find((i:any)=> i.cid == item)||{}
    //     return row;
    //   }
    // }) || []
  }, [selectItems]);
  // console.log(selectItems, isNoSelect, selectKeys, "selectItems");
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    // setCheckedList(e.target.checked ? plainOptions : []);
    state?.list?.forEach((i: any) => {
      onCheck(e.target.checked ? [i] : [], true);
    });
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  useEffect(() => {
    if (size(selectKeys) === 0) {
      setIndeterminate(false);
      setCheckAll(false);
    } else {
      const flag =
        size(
          xor(
            selectKeys,
            state?.list?.map((v: any) => v),
          ),
        ) !== 0;
      setIndeterminate(flag);
      if (flag) {
        setCheckAll(false);
      } else {
        setCheckAll(true);
      }
    }
  }, [selectKeys, state?.list]);
  const bottList = list1?.map((i: any) => i.cid).filter((v: any) => selectItems?.map((i: any) => i.cid).includes(v));
  return (
    <div style={{ margin: "0px 24px" }} className={`devive-list select-content-part ${className}`}>
      <div className="tree-title" style={{ padding: "16px", height: "54px" }}>
        <div>设备列表({state.total || 0})</div>
        {isNoSelect ? (
          <></>
        ) : (
          <Checkbox
            indeterminate={list1?.lengthh > 0 && list1?.length < bottList.length}
            onChange={onCheckAllChange}
            checked={list1?.length === bottList?.length && list1?.length !== 0 && bottList?.length !== 0}
          >
            全选
          </Checkbox>
        )}
      </div>
      <div className="list-from">
        <div className="search-box">
          <Input
            prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: "12px" }} />}
            value={state.keywords}
            placeholder="请输入设备名称"
            onChange={(v) => updateState({ keywords: v.target.value })}
          />
        </div>
      </div>
      <DynamicList
        loadPage={(d: any) => {
          // offset 0开始
          const page = d?.list.length ? Math.ceil(d.list.length / 20) - 1 : -1;
          return getLoadMoreList(page, 20, d ? d?.list : []);
        }}
        reloadDeps={[groupId, state.keywords, segmetedValue]}
        renderItem={(item: { index: number; data: any }) => {
          return (
            <div className="device-list-item" key={item.data.cid}>
              {isNoSelect ? (
                <div
                  className={`device-list-item-noSelect ${selectKeys.includes(item.data.cid) && "device-list-item-noSelect_active"}`}
                  onClick={() => onCheck([item.data])}
                >
                  <DeviceIcon state={item.data.state} type={item.data.type} style={{ paddingRight: 4, fontSize: 18 }} />
                  <span>{item.data.name}</span>
                </div>
              ) : (
                <Checkbox checked={selectKeys?.map((i) => i.cid)?.includes(item.data.cid)} onClick={() => onCheck([item.data])}>
                  <DeviceIcon state={item.data.state} type={item.data.type} style={{ paddingRight: 4, fontSize: 18 }} />
                  <span>{item.data.name}</span>
                </Checkbox>
              )}
            </div>
          );
        }}
      />
    </div>
  );
}

interface DeviceList2Props {
  list?: ListItem[];
  className?: string;
  onDelete: (items: ListItem[]) => void;
  list1: any;
  onDeleteAll: () => void;
}

function DeviceList2({ list = [], className = "", onDelete, list1, onDeleteAll }: DeviceList2Props) {
  const [state, updateState] = useSimpleState({ keywords: "" });
  // const filterList = useMemo(() => {
  //   return list?.filter((v) => v.name?.includes(state.keywords));
  // }, [list, state.keywords]);
  // console.log(list,list1,'asdadqq11')
  const filterList = useMemo(() => {
    const currentList = list1.filter((v: any) => list.includes(v.cid));
    return currentList?.filter((v: any) => v.name?.includes(state.keywords));
  }, [list, state.keywords, list1]);
  return (
    <div className={`devive-list select-content-part ${className}`}>
      <div className="tree-title" style={{ height: 54, lineHeight: 54, paddingRight: 16 }}>
        已选择设备({list?.length || 0}){" "}
        <span onClick={onDeleteAll} className="clear">
          清空
        </span>
      </div>
      <div className="list-from">
        <div className="search-box">
          <Input
            placeholder="请输入设备名称"
            prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: "12px" }} />}
            value={state.keywords}
            onChange={(v) => updateState({ keywords: v.target.value })}
          />
        </div>
      </div>
      <List
        list={list || []}
        renderItem={(item: any) => (
          <div className="device-select-list-item" style={{ height: 40, alignItems: 'start' }} key={item.cid}>
            <DeviceIcon state={item.state} type={item.type} style={{ paddingRight: 4, fontSize: 18, alignItems: 'start', marginTop: 2 }} />
            <span>{item.name}</span>
            <IconFont type="icon-guanbi" style={{ alignItems: 'start', marginTop: -6 }} className="delete-icon" onClick={() => onDelete([item])} />
          </div>
        )}
      />
    </div>
  );
}

export default DeviceSelect;
