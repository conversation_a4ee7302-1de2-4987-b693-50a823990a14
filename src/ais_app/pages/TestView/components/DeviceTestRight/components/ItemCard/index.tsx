import { useMemo, useState, useEffect } from "react";
import { Switch, Card, Row, Col, Tag, Checkbox, AutoComplete, Modal, message } from "antd";
import { DateToFormat, getAlgorithmNameById, TransMins, TransStatus } from "@src/ais_app/utils";
import { IconFont } from "@cloud-app-dev/vidc";
import Authority from "@src/components/Authority";
import "./index.less";

export default function AlarmRightItem({
  isNoDis = false,
  row,
  setRow,
  setIsEdit,
  sflist,
  setIsEditAdd,
  statusChange = () => {},
  isSelect = false,
  setSelectedIds = () => {},
}: any) {
  const [isBatch, SetIsBatch] = useState(false);
  const [isChecked, SetIsChecked] = useState(false);
  // const selectStatusClassname = ""; // 选中'evidence-card-item--selected'
  const selectStatusClassname = isChecked ? "card-item--selected" : ""; // 选中'evidence-card-item--selected'
  useEffect(() => {
    SetIsBatch(isSelect);
    if (!isSelect) SetIsChecked(false);
  }, [isSelect]);
  return (
    <Card
      onClick={() => {
        if (!isBatch) return;
        SetIsChecked(!isChecked);
        setSelectedIds((old: any) => {
          return old?.includes(row.id) ? old.filter((item: any) => item !== row.id) : [...old, row.id];
        });
      }}
      className={`card-dir-item ${isBatch ? "card-item--batch" : ""} ${selectStatusClassname}`}
      cover={null}
    >
      <Card.Meta
        avatar={null}
        title={null}
        description={
          <>
            {row.alarmSum ? <span className="card-item_alarmSum">{row.alarmSum}&nbsp;个告警</span> : null}

            <Row align="middle" wrap={false} style={{ display: "flex", alignItems: "center", width: "100%", borderRadius: 2, overflow: "hidden" }}>
              <img alt="" src={row.screenshot} className="card-item__cover" />
            </Row>
            <div className="tagList">
              {row?.tagInfoList?.map((item: any) => {
                return (
                  <div style={{ marginBottom: 4 }}>
                    <Tag
                      style={{
                        color: "#" + item?.tagFontColor,
                        background: "#" + item?.tagBgColor,
                        borderColor: "#" + item?.tagFontColor,
                      }}
                    >
                      {item.tagName}
                    </Tag>
                  </div>
                );
              })}
            </div>
            <Row justify="space-between" align="middle" wrap={false}>
              <span className="card-item__uploader">{row?.deviceName || "-"}</span>
              {row.taskState ? (
                <span
                  style={{
                    color: "rgba(0, 0, 0, 0.60)",
                    fontFamily: "PingFang SC",
                    fontSize: "12px",
                    fontStyle: "normal",
                    fontWeight: 400,
                  }}
                >
                  {TransStatus(row.taskState)}
                </span>
              ) : (
                ""
              )}
            </Row>
            <Row
              style={{
                fontSize: 12,
              }}
              justify="space-between"
              align="middle"
            >
              <Col>
                <span className="card-dir-item-file-num">{getAlgorithmNameById(sflist || [], row?.algorithmId)}</span>
              </Col>
              <Checkbox checked={isChecked} className="card-item__checkbox" />
            </Row>
          </>
        }
      />

      <Row justify="center" className="card-item__actions" gutter={[0, 16]}>
        <Authority code="30000211">
          <Col
            className="card-item__action-item"
            span={8}
            onClick={() => {
              setRow(row);
              setIsEdit();
              setIsEditAdd(isNoDis ? true : false);
            }}
          >
            <IconFont type="icon-peizhi" className="card-item__action-icon" />
            <span className="card-item__action-label">配置</span>
          </Col>
        </Authority>
        <Authority code="30000213">
          {isNoDis ? null : (
            <>
              <Col className="card-item__action-item" span={8} onClick={() => {}}>
                <IconFont type="icon-close-circle" className="card-item__action-icon" />
                <span className="card-item__action-label">取消</span>
              </Col>
              <Col className="card-item__action-item" span={8}>
                {/* <IconFont type="icon-a-Group239108" style={{ fontSize: 15 }} className="card-item__action-icon" /> */}
                <Switch
                  onClick={(e) => {
                    statusChange(row);
                  }}
                  size="small"
                  checked={row.taskState === "QUEUEING"}
                />
                <span style={{ marginTop: 8 }} className="card-item__action-label">
                  {row.taskState === "QUEUEING" ? "开启" : "关闭"}
                </span>
              </Col>
            </>
          )}
        </Authority>
      </Row>
    </Card>
  );
}
