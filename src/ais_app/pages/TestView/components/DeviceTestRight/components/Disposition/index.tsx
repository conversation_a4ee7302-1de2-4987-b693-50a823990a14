import { Form, Button, Select, Input, Space, Checkbox, Tag, Table, Modal, message, Switch } from "antd";
import { useForm } from "antd/es/form/Form";
import { IconFont } from "@cloud-app-dev/vidc";
import { useEffect, useRef, useState, useMemo } from "react";
import { useToggle, useRequest, useSafeState, useThrottleEffect, useSize, useAntdTable } from "ahooks";
import type { ColumnsType } from "antd/es/table";
import { queryAisAlarmTaskList, queryAlgorithmList, updateAlarmTaskState, deleteAlarmTask } from "@src/service/ais_app";
import { DateToFormat, getAlgorithmNameById, TransMins, TransStatus } from "@src/ais_app/utils";
import ItemCard from "../ItemCard";
import { isEmpty, uniqBy } from "lodash-es";
import DynamicGridList from "@src/ais_app/components/DynamicGridList";
import Authority from "@src/components/Authority";
import "./index.less";

export default function Disposition({ setVisible, params, refsh = false, rToggle, setRow, setIsEdit, setIsEditAdd }: any) {
  const [form] = useForm();
  const ref = useRef<HTMLDivElement>(null);
  const size: any = useSize(ref);
  const [isSelect, { toggle }] = useToggle(false);
  const [isShow, { toggle: showToggle }] = useToggle(false);
  // const [modalMode, { setLeft, setRight }] = useToggle(false);
  const [isListView, { toggle: isSetListView }] = useToggle(false);
  const [isMore, { setLeft: MsetLeft, setRight: MsetRight }] = useToggle(false);
  const [Ids, setIds]: any = useState([]);
  const [tableHeight, setTableHeight] = useSafeState<number>(0);
  const [getSelectedIds, setSelectedIds]: any = useSafeState([]);
  const [localrefsh, { toggle: lToggle, setLeft, setRight }] = useToggle(false);
  const [total, setTotal] = useSafeState(0);
  const [list, setList] = useSafeState([]);
  const [staticInfo, setStaticInfo] = useSafeState({
    total: 0,
    runing: 0,
    isSwitch: 0,
  });
  const { data: sflist } = useRequest(() => queryAlgorithmList({ limit: 1000, offset: 0 }));
  useThrottleEffect(
    () => {
      // setTableHeight(size && size);
      setTableHeight((size && size?.height - 128) || 0);
    },
    [size],
    {
      wait: 300,
    },
  );
  const getTableData = ({ current, pageSize }: { current: number; pageSize: number }, formData: Object): Promise<any> => {
    const currPageSize = !isListView ? pageSize : 20;
    let lastparams: any = {
      limit: currPageSize,
      offset: (current - 1) * currPageSize,
      isPagination: true,
      algorithmFlag: true,
      ...params,
      ...formData,
    };
    if (isEmpty(params)) {
      return Promise.resolve(() => ({
        list: [],
        total: 0,
      }));
    } else {
      return queryAisAlarmTaskList(lastparams);
    }
  };
  const { tableProps, search } = useAntdTable(getTableData, {
    refreshDeps: [params, refsh],
    form,
  });
  //显示分页器跳转
  tableProps.pagination.showQuickJumper = true;
  tableProps.pagination.showSizeChanger = true;
  tableProps.pagination.showTotal = function (total: number, range: number[]) {
    return `共${total}项数据`;
  };
  const handles = (type: boolean, isMore: boolean = true, id: any = "", taskName: any = "") => {
    // type ? setRight() : setLeft();
    isMore ? MsetRight() : MsetLeft();
    showToggle();
    Modal.confirm({
      title: <>{`${!isMore ? "批量" : ""}${type ? "启动" : "暂停"}确认`}</>,
      content: <>{`是否要${!isMore ? "批量" : ""}${type ? "启动" : "暂停"}“${!isMore ? taskName.taskName : "以下"}”任务吗？`}</>,
      width: 416,
      icon: (
        <>
          {type ? (
            <IconFont style={{ fontSize: "24px", color: "var(--primary)" }} type="icon-danchuang_qidong" />
          ) : (
            <IconFont type="icon-danchuang_zanting" style={{ fontSize: "24px", color: "var(--primary)" }} />
          )}
        </>
      ),
      centered: true,
      onOk: () => {
        handleOk(type, isMore, id);
      },
      onCancel: () => { },
    });
  };
  const statusChange = (v: any) => {
    setIds([v?.id]);
    handles(v?.taskState === "STOPPED" ? true : false, false, v?.id, v);
  };
  const promptTask = (data: any) => {
    Modal.confirm({
      title: "任务处理提示",
      centered: true,
      footer: (
        <div style={{ display: "flex", justifyContent: "flex-end", position: "relative", top: "10px" }}>
          <Button
            onClick={() => {
              Modal.destroyAll();
            }}
            type="primary"
          >
            确定
          </Button>
        </div>
      ),
      content: (
        <div className="promptTask-box">
          <div className="promptTask-box-item">
            <div className="promptTask-box-item-title">操作类型</div>
            <div className="promptTask-box-item-data">{data.taskState === "STOPPED" ? "暂停" : "启动"}</div>
          </div>
          <div className="promptTask-box-item">
            <div className="promptTask-box-item-title">应完成</div>
            <div className="promptTask-box-item-data">{data.finishCount || 0}</div>
          </div>
          <div className="promptTask-box-item">
            <div className="promptTask-box-item-title">实际完成</div>
            <div className="promptTask-box-item-data">{data.totalCount || 0}</div>
          </div>
          <div className="promptTask-box-item">
            <div className="promptTask-box-item-title">失败数量</div>
            <div className="promptTask-box-item-data" style={{ color: "#D54941" }}>
              {data.failCount || 0}
            </div>
          </div>
        </div>
      ),
      okText: "确认",
      // cancelText: '取消',
      onOk: () => { },
    });
  };
  const handleOk = (type: boolean, isMore: boolean = true, id: any = "") => {
    showToggle();
    updateAlarmTaskState({ ids: isMore ? getSelectedIds : [id], taskState: type ? "QUEUEING" : "STOPPED" }).then((res) => {
      if (res.code === 0) {
        message.success("操作成功");
        promptTask(res.data);
        rToggle();
      } else {
        message.warning(res.message);
      }
    }, (err) => {
      message.warning(err?.data?.message);
    });
  };
  const columns: ColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "key",
      key: "key",
      width: 88,
      fixed: "left",
      render: (_, __, index) => index + 1,
    },
    {
      title: "任务名",
      ellipsis: true,
      dataIndex: "taskName",
      width: 134,
    },
    {
      title: "任务ID",
      ellipsis: true,
      dataIndex: "id",
      width: 144,
    },
    {
      title: "设备名称",
      ellipsis: true,
      dataIndex: "deviceName",
      width: 180,
      render: (res) => res || "-",
    },
    {
      title: "算法名称",
      ellipsis: true,
      dataIndex: "algorithmId",
      width: 135,
      render: (text: any) => getAlgorithmNameById(sflist || [], text),
    },
    {
      title: "场所标签",
      ellipsis: true,
      dataIndex: "tagInfoList",
      width: 234,
      render: (res) => {
        return (
          <>
            {/* <Space> */}
            {res?.map((v: any) => {
              return (
                <Tag
                  style={{
                    color: "#" + v?.tagFontColor,
                    background: "#" + v?.tagBgColor,
                    borderColor: "#" + v?.tagFontColor,
                  }}
                >
                  {v?.tagName}
                </Tag>
              );
            })}
            {/* </Space> */}
          </>
        );
      },
    },
    {
      title: "任务时间规则",
      ellipsis: true,
      width: 188,
      dataIndex: "ruleConfigName",
      render: (text: string) => text || "-",
    },
    {
      title: "ROI",
      ellipsis: true,
      width: 135,
      dataIndex: "hasRoi",
      render: (res) => (res ? "已配置" : "未配置"),
    },
    {
      title: "分析频率",
      ellipsis: true,
      width: 135,
      dataIndex: "detectInterval",
      render: (text: string) => (text == "-1" ? "I帧" : TransMins(+text)),
    },
    {
      title: "场景持续时间",
      ellipsis: true,
      width: 188,
      dataIndex: "duration",
      render: (text: string) => TransMins(+text) || "-",
    },
    {
      title: "运行状态",
      ellipsis: true,
      width: 210,
      dataIndex: "taskState",
      render: (text: string) => TransStatus(text),
    },
    {
      title: "报警数量",
      dataIndex: "alarmSum",
      width: 160,
      ellipsis: true,
    },
    // {
    //   title: "报警间隔",
    //   dataIndex: "detectInterval",
    //   ellipsis: true,
    //   width: 188,
    //   render: (text: string) => (text == "-1" ? "I帧" : TransMins(+text)),
    // },
    {
      title: "最近报警时间",
      ellipsis: true,
      width: 210,
      dataIndex: "lastAlarmTime",
      render: (text: string) => DateToFormat(text, "YYYY-MM-DD HH:mm:ss"),
    },
    {
      title: "操作",
      width: 160,
      dataIndex: "description",
      ellipsis: true,
      fixed: "right",
      render: (text: string, item: any) => (
        window._PLATFORM_TYPE === 2 &&
        <Space>
          <Authority code="30000213">
            <Switch
              onChange={() => {
                statusChange(item);
              }}
              size="small"
              checked={item?.taskState !== "STOPPED"}
            />
          </Authority>
          <Authority code="30000212">
            <a
              onClick={() => {
                setRow(item);
                setIsEdit();
                setIsEditAdd(false);
              }}
            >
              配置
            </a>
          </Authority>
          <Authority code="30000213">
            <a
              onClick={() => {
                Modal.confirm({
                  title: <>{`提示`}</>,
                  content: <>{`是否要取消任务“${item.taskName}”?`}</>,
                  width: 416,
                  icon: (
                    <>
                      <IconFont type="icon-gaojingzhongxin" style={{ fontSize: "24px", color: "var(--primary)" }} />
                    </>
                  ),
                  centered: true,
                  onOk: () => {
                    deleteAlarmTask(item.id).then((res) => {
                      if (res.code === 0) {
                        message.success("删除成功");
                        search.submit();
                      } else {
                        message.warning(res.message);
                      }
                    }, (err) => {
                      message.warning(err?.data?.message);
                    });
                  },
                  onCancel: () => { },
                });
              }}
            >
              取消
            </a>
          </Authority>
        </Space>
      ),
    },
  ];
  useEffect(() => {
    const runData = tableProps?.dataSource?.filter((v: any) => v.taskState === "RUNNING");
    const runData1 = tableProps?.dataSource?.filter((v: any) => v.taskState !== "STOPPED");
    setStaticInfo({ total: tableProps?.dataSource?.length ?? 0, runing: runData?.length ?? 0, isSwitch: runData1?.length ?? 0 });
  }, [tableProps?.dataSource]);
  const reloadDeps = useMemo(() => [params, refsh, localrefsh], [params, refsh, localrefsh]);
  const loadPage = async (d: any): Promise<any> => {
    d.page ? d.page++ : (d.page = 1);
    let lastparams: any = {
      limit: 30,
      offset: (d?.page - 1) * 30,
      isPagination: true,
      algorithmFlag: false,
      ...params,
      ...form.getFieldsValue(),
    };
    const res = await queryAisAlarmTaskList({
      ...lastparams,
    });
    setTotal(res?.totalCount);
    const resultData = res?.list;
    d.total = res?.totalCount;
    d.list ? (d.list = uniqBy([...d.list, ...resultData], "id")) : (d.list = resultData);
    setList(d.list ? (d.list = uniqBy([...d.list, ...resultData], "id")) : (d.list = resultData));
    if (localrefsh) {
      message.success("刷新成功");
      setLeft();
    }
    return d;
  };
  return (
    <div className="Disposition">
      <div className="Disposition-header">
        <div className="Disposition-header-left">
          {isSelect ? (
            <Checkbox
              indeterminate={getSelectedIds.length > 0 && getSelectedIds.length < tableProps.dataSource.length}
              checked={getSelectedIds.length === tableProps.dataSource.length}
              onChange={(e) => {
                const isShow = e.target.checked;
                setSelectedIds(isShow ? tableProps.dataSource.map((v: any) => v.id) : []);
              }}
            >
              全选
            </Checkbox>
          ) : (
            <>
              <Form form={form} layout="inline" className="search-form">
                <Space>
                  <Form.Item label="ROI区域" name="hasRoi" style={{ marginRight: 24 }}>
                    <Select
                      placeholder="请选择"
                      style={{ width: "145px" }}
                      className="user-filter-btn"
                      onChange={() => {
                        search.submit();
                        setList([]);
                        lToggle();
                      }}
                      allowClear={false}
                      options={[
                        {
                          label: "全部",
                          value: null,
                        },
                        {
                          label: "已配置",
                          value: true,
                        },

                        {
                          label: "未配置",
                          value: false,
                        },
                      ]}
                    />
                  </Form.Item>
                  <Form.Item name="taskName">
                    <Input
                      placeholder="输入任务名称、设备名称、算法名称进行查找"
                      className="user-filter-btn vidc-Input"
                      allowClear={false}
                      suffix={
                        <IconFont
                          type="icon-renwupeizhi_shebeirenwu_sousuo"
                          style={{ cursor: "pointer", fontSize: "12px", color: "rgba(255,255,255,0.5)" }}
                        />
                      }
                      onChange={() => {
                        search.submit();
                        setList([]);
                        lToggle();
                      }}
                      style={{ width: "260px", borderRadius: "4px" }}
                    />
                  </Form.Item>
                </Space>
              </Form>
              <div className="Disposition-header-left-llg">
                分析中/已开启/总任务：{staticInfo?.runing}/{staticInfo?.isSwitch}/{staticInfo?.total}
              </div>
            </>
          )}
        </div>

        <Space className="Disposition-header-right">
          {isSelect ? (
            <>
              {/* <Button disabled={!getSelectedIds?.length} style={{ color: "var(--primary)", borderColor: "var(--primary)" }}>
                全部取消
              </Button> */}
              <Button
                disabled={!getSelectedIds?.length}
                onClick={() => {
                  handles(true, true);
                }}
                style={{ color: "var(--primary)", borderColor: "var(--primary)" }}
              >
                全部开启
              </Button>
              <Button
                disabled={!getSelectedIds?.length}
                onClick={() => {
                  handles(false, true);
                }}
                style={{ color: "var(--primary)", borderColor: "var(--primary)" }}
              >
                全部暂停
              </Button>
              <Button onClick={toggle}>取消</Button>
            </>
          ) : (
            <>
           
                <>
                  <Authority code="30000212">
                    <Button
                      onClick={() => {
                        setRow({
                          name: "批量任务配置",
                        });
                        setVisible(true);
                      }}
                      style={{ color: "var(--primary)", borderColor: "var(--primary)" }}
                    >
                      任务配置
                    </Button>
                  </Authority>
                  <Authority code="30000213">
                    <Button onClick={toggle} type="primary">
                      批量处理
                    </Button>
                  </Authority>
                </>

              <Button onClick={isSetListView}>
                <IconFont type={isListView ? "icon-shipinchakan_liebiaomoshi" : "icon-kapian"} />
              </Button>
            </>
          )}
        </Space>
      </div>
      {isListView ? (
        // <List
        //   grid={{
        //     gutter: 16,
        //   }}
        //   dataSource={tableProps.dataSource}
        //   renderItem={(item) => (
        //     <List.Item>
        // <ItemCard
        //   setSelectedIds={setSelectedIds}
        //   isSelect={isSelect}
        //   statusChange={statusChange}
        //   row={item}
        //   setIsEdit={setIsEdit}
        //   setRow={setRow}
        //   sflist={sflist}
        //   setIsEditAdd={setIsEditAdd}
        // />
        //     </List.Item>
        //   )}
        // />
        <DynamicGridList
          itemKey="id"
          itemHeight={220}
          itemWidth={226}
          isNoMore={(data) => data?.list?.length === total}
          loadPage={loadPage}
          reloadDeps={reloadDeps}
          renderItem={(item: any, index: any) => {
            return (
              <ItemCard
                setSelectedIds={setSelectedIds}
                isSelect={isSelect}
                statusChange={statusChange}
                row={item}
                setIsEdit={setIsEdit}
                setRow={setRow}
                sflist={sflist}
                setIsEditAdd={setIsEditAdd}
              />
            );
          }}
        />
      ) : (
        <div ref={ref} style={{ overflow: 'hidden', height: '80%', width: '100%' }}>
          <Table
            rowSelection={
              isSelect
                ? {
                  type: "checkbox",
                  onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
                    setSelectedIds(selectedRows.map((v) => v.id));
                  },
                  selectedRowKeys: getSelectedIds,
                }
                : undefined
            }
            scroll={{ y: tableHeight }}
            columns={columns}
            rowKey="id"
            {...tableProps}
          />
        </div>
      )}
    </div>
  );
}
