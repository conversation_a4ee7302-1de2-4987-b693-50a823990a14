.Disposition {
  height: 100%;
  display: flex;
  flex-direction: column;
  // overflow: hidden;
  .css-12w4ian {
    overflow-x: hidden;
  }
  .Disposition-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    .Disposition-header-left {
      display: flex;
      align-items: center;
      .Disposition-header-left-llg {
        color: #165dff;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        display: flex;
        padding: 4px 12px;
        align-items: center;
        border-radius: 3px;
        border: 1px solid #165dff;
        background: #e8f3ff;
        margin-left: 16px;
      }
    }
    .Disposition-header-right {
      display: flex;
      align-items: center;
    }
  }
}
