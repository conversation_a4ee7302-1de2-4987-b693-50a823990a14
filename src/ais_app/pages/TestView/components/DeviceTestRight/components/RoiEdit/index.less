.interested-area {
  background: var(--content-bg);
  margin-top: 16px;
  .interested-area-header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e7e7e7;
    color: rgba(0, 0, 0, 0.9);
    text-align: right;
    font-family: "PingFang SC";
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 32px;
  }
  .fullscreen-box {
    color: var(--primary-light);
    cursor: pointer;
    width: max-content;
  }
  .draw-area {
    height: 100%;
    .roibox {
      padding: 16px 0px;
      .ROI {
        width: 100%;
        height: 100%;
        position: relative;
        .refshImg {
          position: absolute;
          cursor: pointer;
          left: 50%;
          top: 50%;
          transform: translate(-32px, -32px);
          z-index: 10;
        }
        .rect-mask {
          span {
            height: 29px;
            line-height: 28px;
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 2px;
            font-size: 12px;
            padding: 0 8px;
            user-select: none;
          }
        }
        canvas {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
        }
        .img-box {
          width: 100%;
          height: 100%;

          // min-height: 350px;
          background-color: rgba(175, 175, 175, 0.3);
          img {
            width: 100%;
            object-fit: fill;
            height: 100%;
          }
        }
      }
    }
    .infobox {
      padding: 16px;
      .roi-area-handle {
        display: flex;
        flex-direction: column;
        height: 100%;
        .header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .title {
            display: flex;
            align-items: center;
            .icon {
              width: 4px;
              height: 16px;
              background-color: var(--primary-light);
            }
            .titletext {
              margin-left: 10px;
              font-weight: 500;
              //   color: var(--gray2);
              color: rgba(0, 0, 0, 0.9);
              text-align: right;
              /* Body/Medium */
              font-family: "PingFang SC";
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
            }
          }
          .handle {
            display: flex;
            align-items: center;
            .clear-btn {
              cursor: pointer;
              color: var(--primary-light);
              span {
                margin-left: 2px;
              }
            }
            .add-btn {
              margin-left: 16px;
              .cloudappais-btn-primary {
                background: var(--primary-light);
                border-radius: var(--radius1);
              }
            }
          }
        }
        .tips {
          margin-top: 12px;
          color: var(--primary);
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          display: flex;
          padding: 8px;
          align-items: flex-start;
          border-radius: 3px;
          background: #e8f3ff;
        }
        .areas {
          margin-top: 12px;
          padding: 16px;
          border-radius: 4px;
          border: 1px solid #dcdcdc;
          height: 100%;
          .area-item {
            display: flex;
            align-items: center;
            border-radius: 3px;
            padding: 8px;
            margin-bottom: 16px;
            &:hover {
              background: #e8f3ff;
            }
            &:last-child {
              margin-bottom: 0px;
            }
            .name {
              color: #000;
              display: flex;
              align-items: center;
              .name-icon {
                width: 16px;
                height: 16px;
                border: 1px solid #d54941;
                background: #ffb9b0;
                margin-right: 8px;
              }
            }
            .line {
              flex: 1;
              border: 1px dashed transparent;
            }
            .delete-btn {
              cursor: pointer;
              margin-left: 16px;
              color: var(--primary-light);
            }
          }
        }
      }
    }
  }
}
