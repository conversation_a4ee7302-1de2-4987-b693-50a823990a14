import { IconFont } from "@cloud-app-dev/vidc";
import useDrawRO<PERSON> from "@src/ais_app/components/useDrawROI";
import { useSafeState, useSize, useToggle, useFullscreen } from "ahooks";
import { Button, Col, Form, Row } from "antd";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { polygon } from "@turf/helpers";
import "./index.less";
import centerOfMass from "@turf/center-of-mass";
import { coverImage } from "@src/service/ais_app";
import {cloneDeep} from 'lodash-es';

const DrawArea = ({ cid, defaultRoi, onChangeArea, eRight, config, AlgorithmName, configing, RoiEditRef, isFullscreen, exitFullscreen }: any) => {
  const roiRef = useRef(null);
  const size = useSize(roiRef);
  const [imgUrl, setImgUrl] = useSafeState<string>("");
  const [rImg, { toggle: rImgToggle }] = useToggle(false);
  const [de, setDe] = useState(cloneDeep(defaultRoi))
 
  const defaultAreas: [number, number][][] = useMemo(() => {
    const { width = 0, height = 0 } = size || {};
    const Roidata = de?.map((v: any) => {
      return v?.PolygonArea?.map((m: any) => {
        return [m.X * width, m.Y * height];
      });
    });
    return Roidata || [];
  }, [size, de, isFullscreen]);
  const { open, close, clear, areas, status, deleteArea } = useDrawROI(roiRef, { row: 18, rowItems: 32, defaultAreas });
  // 获取多边形中心点
  const centerPoints = useMemo(() => {
    const currAreas = areas.filter((item) => item?.length);
    // console.log(currAreas.map((v) => [...v, v[0]]),currAreas,'121221');
    if (!currAreas.length) return [];
    try {
      return currAreas
        .map((v) => [...v, v[0]])
        .map((v2) => (v2 ? centerOfMass(polygon([v2])) : undefined))
        .map((v) => (v ? v.geometry.coordinates : undefined));
    } catch (error) {}
  }, [areas]);
  useEffect(() => {
    coverImage(cid).then((res) => {
      if (res.code === 0) {
        setImgUrl(res.data?.url);
      } else {
        setImgUrl("");
      }
    });
  }, [cid, rImg]);
  useEffect(() => {
    clear();
  }, [config, cid, configing]);
  useEffect(() => {
   
    const { width = 0, height = 0 } = size || {};
    const roiParams = areas.map((oneItem) => ({
      PolygonArea: oneItem.map((v) => ({ X: (v[0] / width).toFixed(4), Y: (v[1] / height).toFixed(4) })),
    }));
    onChangeArea(roiParams);
    close();
  }, [areas]);
  return (
    <div className="interested-area" ref={RoiEditRef}>
      {isFullscreen ? (
        <div className="interested-area-header" style={{ padding: isFullscreen ? "24px 32px" : "0" }}>
          <div>感兴趣区域(ROI)</div>
          <IconFont onClick={exitFullscreen} style={{ cursor: "pointer" }} type="icon-guanbi" />
        </div>
      ) : null}

      <Row className="draw-area" style={isFullscreen ? { padding: "32px", height: "calc(100% - 64px)" } : {}}>
        <Col span={14} className="roibox">
          <div className="ROI">
            <div className="img-box">
              <img src={imgUrl} alt="" />
            </div>
            {imgUrl === "" && (
              <Button type="primary" className="refshImg" onClick={rImgToggle}>
                刷新
              </Button>
            )}
            <canvas ref={roiRef} />
            <div className="rect-mask">
              {centerPoints?.map(([x, y] = [0, 0], i) => (
                <span style={{ left: x, top: y }} key={i}>
                  目标{i + 1}
                </span>
              ))}
            </div>
          </div>
        </Col>
        <Col span={10} className="infobox">
          <div className="roi-area-handle">
            <div className="header">
              <div className="title">
                {/* <div className="icon"></div> */}
                <div className="titletext">{isFullscreen ? "" : "ROI区域"}</div>
              </div>
              <div className="handle">
                <Button
                  onClick={() => {
                    clear();
                    eRight();
                    setDe([])
                  }}
                >
                  <IconFont type="icon-clear" />
                  <span>清空</span>
                </Button>
                {/* <div
                  className="clear-btn"
                  onClick={() => {
                    clear();
                    eRight();
                  }}
                >
                  <IconFont type="icon-renwupeizhi_shebeirenwu_qingkong" />
                  <span>清空</span>
                </div> */}
                <div className="add-btn">
                  {status === "open" ? (
                    <Button type="primary" onClick={close}>
                      取消
                    </Button>
                  ) : (
                    <Button
                      // type="primary"
                      onClick={() => {
                        open();
                        eRight();
                      }}
                      //   disabled={!configing}
                    >
                      <IconFont type="icon-add" />
                      添加
                    </Button>
                  )}
                </div>
              </div>
            </div>
            <div className="tips">( Tips：敲击“Esc”终止绘制；连接首尾点、双击鼠标 或 敲击“回车键(Enter) ”闭合绘制。)</div>
            <ul className="areas">
              {areas?.map((v, i) => (
                <li key={i} className="area-item">
                  <div className="name"><span className="name-icon"></span>目标{i + 1}</div>
                  <div className="line"></div>
                  <div
                    className="delete-btn"
                    onClick={() => {
                      // setDe(de.filter((it:any,ind:number)=>ind !== i));
                      deleteArea(v);
                      eRight();
                    }}
                  >
                     <IconFont type="icon-delete" style={{color:'rgba(0, 0, 0, 0.9)'}}/>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default DrawArea;
