import { Row, Col, Tooltip,} from "antd";
import { IconFont } from "@cloud-app-dev/vidc";
import RoiImage from "@src/ais_app/components/RoiImage";
import { DateToFormat, TransMins, TransStatus, getDeviceNameByCode } from "@src/ais_app/utils";
import "./index.less";
export default function EditDrawer({ sRef, imgUrl, rois, ImData, isEditAdd }: any) {
 
  const baseType = [
    {
      label: "任务ID",
      fname: ImData?.id || "-",
    },
    {
      label: "任务名称",
      fname: ImData?.taskName || "-",
    },
    {
      label: "任务状态",
      fname: TransStatus(ImData?.taskState) || "-",
    },
    {
      label: "创建人",
      fname: ImData?.createdBy || "-",
    },
    {
      label: "创建时间",
      fname: DateToFormat(ImData?.createdTime, "YYYY-MM-DD HH:mm:ss"),
    },
    {
      label: "最后更新时间",
      fname: DateToFormat(ImData?.updatedTime, "YYYY-MM-DD HH:mm:ss"),
    },
  ];
  const deviceInfo = [
    {
      label: "设备名称",
      fname: ImData?.deviceInfo?.name || "-",
    },
    {
      label: "设备CID",
      fname: ImData?.deviceInfo?.cid || "-",
    },
    {
      label: "设备分辨率",
      fname: ImData?.deviceInfo?.deviceResolving || "-",
    },
    {
      label: "设备编码格式",
      fname: ImData?.deviceInfo?.deviceEncoding || "-",
    },
    {
      label: "码流大小",
      fname: ImData?.deviceInfo?.deviceStreamSize || "-",
    },
    {
      label: "设备类型",
      fname: getDeviceNameByCode(ImData?.deviceInfo?.type) || "-",
    },
    {
      label: "取流协议",
      fname: ImData?.deviceInfo?.sdkType || "-",
    },
  ];
  const calcInfo = [
    {
      label: "分析频率",
      fname: ImData?.configJson?.Analysis?.DetectInterval == "-1" ? "I帧" : TransMins(ImData?.configJson?.Analysis?.DetectInterval) || "-",
    },
    {
      label: "场景持续时间",
      tip: "用于定义结构化目标在画面中出现的时长，超过预设时长后，才视为一次告警事件。下拉选项包括（默认 / 10 分钟 / 15 分钟 / 30 分钟 / 60 分钟 / 自定义）",
      fname: TransMins(ImData?.configJson?.Analysis?.Rule[0]?.AreaRois?.MinDuration) || "-",
    },
    {
      label: "目标最小像素",
      tip: "用于过滤尺寸较小的结构化目标。低于预设宽高的结构化目标，将被当作一次无效告警。",
      fname: `${ImData?.configJson?.Analysis?.Rule[0]?.AreaRois?.MinWidth ?? 0}px * ${ImData?.configJson?.Analysis?.Rule[0]?.AreaRois?.MinHeight ?? 0}px`,
    },
    {
      label: "分析服务器",
      fname: ImData?.configJson?.Analysis?.Rule[0]?.AreaRois?.a || "-",
    },
    {
      label: "目标最小数量",
      tip: "针对人员聚集、车辆拥堵等算法，目标数量少于预设数值，将被当作一次无效告警",
      fname: ImData?.configJson?.Analysis?.Rule[0]?.AreaRois?.MinObjectNumber ?? "-",
    },
    {
      label: "目标最大像素",
      tip: "主要为了防范因画面被遮挡导致的算法误报。高于预设宽高的结构化目标，将被当作一次无效告警。",
      fname: `${ImData?.configJson?.Analysis?.Rule[0]?.AreaRois?.MaxWidth ?? 0}px * ${ImData?.configJson?.Analysis?.Rule[0]?.AreaRois?.MaxHeight ?? 0}px`,
    },
    {
      label: "调用GPU卡",
      fname: ImData?.configJson?.Analysis?.Rule[0]?.AreaRois?.b || "-",
    },
    {
      label: "参考色 (RGB)",
      fname: ImData?.configJson?.Analysis?.Rule[0]?.ColorRois?.DetectColor ?? "-",
      tip: "针对裸土未苫盖算法，建议选择裸土的颜色；针对水位超标算法，建议选择水位线的颜色。",
    },
    {
      label: "原图叠加边框",
      tip: "原图叠加框	在输出的原图上，设置叠加结构化目标框或感兴趣区域框。不勾选叠加选项，对外告警时将直接输出原始图片。",
      fname: `${ImData?.configJson?.Output?.ImageBbox ? "叠加结构化目标框" : "-"}，${ImData?.configJson?.Output?.RoiBbox ? "叠加感兴趣区域" : "-"}`,
    },
    {
      label: "告警频率",
      fname: ImData?.configJson?.Analysis?.DetectInterval == "-1" ? "I帧/次" : TransMins(ImData?.configJson?.Analysis?.DetectInterval) + "/次" || "-",
    },
  ];
 
  return (
    <>
      <div className="edit-box-container" style={{ marginTop: 24 }}>
        <div className="edit-box-container-title">设备信息</div>
        <Row className="row-content" gutter={[0, 14]}>
          {deviceInfo.map((v, i) => (
            <Col span={8} key={i} className="info-item">
              <div className="label">{v.label}</div>
              <div className="value" title={v.fname}>{v.fname}</div>
            </Col>
          ))}
        </Row>
      </div>
      {!isEditAdd ? (
        <div className="edit-box-container" style={{ marginTop: 24 }}>
          <div className="edit-box-container-title">任务信息</div>
          <Row className="row-content" gutter={[0, 14]}>
            {baseType.map((v, i) => (
              <Col span={8} key={i} className="info-item">
                <div className="label">{v.label}</div>
                <div className="value" title={v.fname}>{v.fname}</div>
              </Col>
            ))}
          </Row>
        </div>
      ) : null}

      <div className="edit-box-container" style={{ marginTop: 24 }}>
        <div className="edit-box-container-title">{!isEditAdd ? "算法信息" : "视频画面"}</div>
        {!isEditAdd ? (
          <>
            <Row className="row-content" gutter={[0, 14]}>
              {calcInfo.map((v, i) => (
                <Col span={8} key={i} className="info-item">
                  <div className="label" style={v?.tip ? { width: 115 } : {}}>
                    {v?.tip ? (
                      <Tooltip placement="top" title={v?.tip}>
                        <span>
                          <IconFont type="icon-renwupeizhi_shebeirenwu_shuoming" style={{ color: "rgba(0, 0, 0, 0.26)" }} />
                        </span>
                      </Tooltip>
                    ) : null}
                    {v.label}
                  </div>
                  <div className="value" title={v.fname}>{v.fname}</div>
                </Col>
              ))}
            </Row>
            <div className="edit-box-container-title" style={{ marginTop: 16 }}>
              感兴趣区域(ROI)：
            </div>
          </>
        ) : null}

        <div className="image-box" ref={sRef as any}>
          <RoiImage imgUrl={imgUrl} rois={rois} />
        </div>
      </div>
    </>
  );
}
