import { useSafeState } from "ahooks";
import { Row, Button, Col, Divider, Form, Select, Space, InputNumber, Checkbox } from "antd";
import { IconFont } from "@cloud-app-dev/vidc";
import OneAngle from "@src/ais_app/components/OneAngle";
import "./index.less";
const CheckboxGroup = Checkbox.Group;
export default function AlgorithmConfigurationEdit({ form, isFullscreen, exitFullscreen, enterFullscreen, isAllEdit }: any) {
  const [aItem, setaItem]: any = useSafeState();
  const [tItem, settItem]: any = useSafeState();
  const [intervalArr, setIntervalArr] = useSafeState([
    {
      label: 'I帧',
      value: -1,
    },
    {
      label: '全帧',
      value: 0,
    },
    {
      label: '10秒',
      value: 10,
    },
    {
      label: '30秒',
      value: 30,
    },
    {
      label: '5分钟',
      value: 300,
    },
    {
      label: '15分钟',
      value: 900,
    },
    {
      label: '30分钟',
      value: 1800,
    },
  ]);
  const [intervalArr1, setIntervalArr1] = useSafeState([
    {
      label: "10秒/次",
      value: 10,
    },
    {
      label: "30秒/次",
      value: 30,
    },
    {
      label: "1分/次",
      value: 600,
    },
    {
      label: "5分/次",
      value: 3000,
    },
    {
      label: "10分/次",
      value: 6000,
    },
    {
      label: "30分/次",
      value: 18000,
    },
    {
      label: "1小时/次",
      value: 36000,
    },
    {
      label: "5小时/次",
      value: 180000,
    },
  ]);
  const [timeArr, setTimeArr] = useSafeState([
    {
      label: "0秒",
      value: 0,
    },
    {
      label: "1秒",
      value: 1,
    },
    {
      label: "10秒",
      value: 10,
    },
    {
      label: "10分钟",
      value: 600,
    },
    {
      label: "15分钟",
      value: 900,
    },
    {
      label: "30分钟",
      value: 1800,
    },
    // {
    //   label: '30分钟',
    //   value: 3600,
    // },
  ]);

  const onNameChange = (value: number | null) => {
    if (value) {
      setaItem({ label: `${value}秒`, value: value });
    }
  };
  const addItem = (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();
    if (!aItem) {
      return;
    }
    setIntervalArr(
      [...new Set([...intervalArr, aItem])].filter((item, index) => {
        return [...new Set([...intervalArr, aItem])].findIndex((obj) => obj.label == item.label) === index;
      }),
    );
    setaItem(undefined);
  };
  const ontNameChange = (value: number | null) => {
    if (value) {
      settItem({ label: `${value}秒`, value: value });
    }
  };
  const addtItem = (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();
    if (!tItem) {
      return;
    }

    setTimeArr(
      [...new Set([...timeArr, tItem])].filter((item, index) => {
        return [...new Set([...timeArr, tItem])].findIndex((obj) => obj.label == item.label) === index;
      }),
    );
    settItem(undefined);
  };
  return (
    <Form style={{ marginTop: 16 }} layout="vertical" form={form}>
      <Row>
        <Col span={12} style={{ marginRight: 24 }}>
          <Form.Item name={["configJson", "Analysis", "DetectInterval"]} label="分析频率" tooltip="分析频率" rules={[{ required: true }]}>
            <Select
              placeholder="请选择分析频率"
              dropdownRender={(menu) => (
                <>
                  {menu}
                  <Divider style={{ margin: "8px 0" }} />
                  <Space style={{ padding: "0 8px 4px" }}>
                    <InputNumber type="number" placeholder="请输入自定义秒数" value={aItem?.value} onChange={onNameChange} />
                    <Button type="primary" icon={<IconFont type="icon-Vector-18" />} onClick={addItem}>
                      自定义秒数
                    </Button>
                  </Space>
                </>
              )}
              options={intervalArr.map((v) => ({ label: v.label, value: v.value }))}
            />
          </Form.Item>
        </Col>
        <Col span={11}>
          <Form.Item
            name={["configJson", "Analysis", "Rule", "AreaRois", "MinDuration"]}
            label="场景持续时间"
            tooltip="用于定义结构化目标在画面中出现的时长，超过预设时长后，才视为一次告警事件。"
            rules={[{ required: true }]}
          >
            <Select
              placeholder="请选择场景持续时间"
              dropdownRender={(menu) => (
                <>
                  {menu}
                  <Divider style={{ margin: "8px 0" }} />
                  <Space style={{ padding: "0 8px 4px" }}>
                    <InputNumber type="number" placeholder="请输入自定义秒数" value={tItem?.value} onChange={ontNameChange} />
                    <Button type="primary" icon={<IconFont type="icon-Vector-18" />} onClick={addtItem}>
                      自定义秒数
                    </Button>
                  </Space>
                </>
              )}
              options={timeArr.map((v) => ({ label: v.label, value: v.value }))}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={12} style={{ marginRight: 24 }}>
          <Form.Item
            name={["configJson", "Analysis", "Rule", "AreaRois", "Min"]}
            label="目标最小像素"
            tooltip="用于过滤尺寸较小的结构化目标。低于预设宽高的结构化目标，将被当作一次无效告警。"
          >
            <OneAngle />
          </Form.Item>
        </Col>
        <Col span={11}>
          <Form.Item
            name={["configJson", "Analysis", "Rule", "AreaRois", "Max"]}
            label="目标最大像素"
            tooltip="主要为了防范因画面被遮挡导致的算法误报。高于预设宽高的结构化目标，将被当作一次无效告警。"
          >
            <OneAngle />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={12} style={{ marginRight: 24 }}>
          <Form.Item
            name={["configJson", "Analysis", "Rule", "AreaRois", "MinObjectNumber"]}
            label="目标最小数量"
            tooltip="针对人员聚集、车辆拥堵等算法，目标数量少于预设数值，将被当作一次无效告警。"
          >
            <InputNumber />
          </Form.Item>
        </Col>
        <Col span={11}>
          <Form.Item
            name={["configJson", "Analysis", "Rule", "ColorRois", "DetectColor"]}
            label="参考色"
            tooltip="针对裸土未苫盖算法，建议选择裸土的颜色；针对水位超标算法，建议选择水位线的颜色。"
          >
            <Select placeholder="请选择参考色" mode="multiple">
              {[
               {
                color: '红色',
                value: 0,
              },
              {
                color: '橙色',
                value: 1,
              },
              {
                color: '黄色',
                value: 2,
              },
              {
                color: '绿色',
                value: 3,
              },
              {
                color: '青色',
                value: 4,
              },
              {
                color: '蓝色',
                value: 5,
              },
              {
                color: '紫色',
                value: 6,
              },
              {
                color: '黑色',
                value: 7,
              },
              {
                color: '灰色',
                value: 8,
              },
              {
                color: '白色',
                value: 9,
              },
              ].map((v: any, i: number) => (
                <Select.Option key={i} value={v.value}>
                  <div className="color-box" style={{ background: v.color }}></div>
                  {v.color}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={12} style={{ marginRight: 24 }}>
          <Form.Item
            name={["configJson", "Output", "Bbox"]}
            label="原图叠加边框"
            tooltip="原图叠加框	在输出的原图上，设置叠加结构化目标框或感兴趣区域框。不勾选叠加选项，对外告警时将直接输出原始图片。"
          >
            <CheckboxGroup>
              <Checkbox value={1}>叠加结构化目标框</Checkbox>
              <Checkbox value={2}>叠加感兴趣区域</Checkbox>
            </CheckboxGroup>
          </Form.Item>
        </Col>
        
          <Col span={11}>
            <Form.Item name="alarmInterval" label="告警频率">
            <Select
              placeholder="请选择告警频率"
              // dropdownRender={(menu) => (
              //   <>
              //     {menu}
              //     <Divider style={{ margin: "8px 0" }} />
              //     <Space style={{ padding: "0 8px 4px" }}>
              //       <InputNumber type="number" placeholder="请输入自定义秒数" value={aItem?.value} onChange={onNameChange} />
              //       <Button type="primary" icon={<IconFont type="icon-Vector-18" />} onClick={addItem}>
              //         自定义秒数
              //       </Button>
              //     </Space>
              //   </>
              // )}
              options={intervalArr1.map((v) => ({ label: v.label, value: v.value }))}
            />
            </Form.Item>
          </Col>
      </Row>
      <Row>
        {isAllEdit ? null : (
          <Col span={12} style={{ marginRight: 24 }}>
            <Form.Item name="nickname" label="感兴趣区域(ROI)" tooltip="可绘制感兴趣的目标区域">
              {/* <Form.Item name="nickname" label="感兴趣区域(ROI)" tooltip="可绘制感兴趣的目标区域" rules={[{ required: true }]}> */}
              {isFullscreen ? (
                <div className="fullscreen-box" onClick={exitFullscreen}>
                  <IconFont type="icon-shishiyulan_yuanhuamian" /> 退出全屏
                </div>
              ) : (
                <div className="fullscreen-box" onClick={enterFullscreen}>
                  <IconFont type="icon-renwupeizhi_shebeirenwu_quanping" /> 全屏
                </div>
              )}
            </Form.Item>
          </Col>
        )}
      </Row>
    </Form>
  );
}
