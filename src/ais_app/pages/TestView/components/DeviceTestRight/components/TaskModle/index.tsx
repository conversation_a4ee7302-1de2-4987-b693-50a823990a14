import { useState } from "react";
import { Modal, Tabs,message } from "antd";
import DeviceAssign from "../DeviceAssign";
import "./index.less";

export default function TaskModle({ visible, handleSubmit, onCancel,onSelectChange,selectItems }: any) {
  const [activeKey, setActiveKey] = useState("1");
  console.log(selectItems,'selectItems')
  return (
    <Modal
      className="task-modal"
      open={visible}
      onOk={(e) => {
        if(activeKey === "1"){
          if(!selectItems?.length) return  message.warning("请选择设备")
        }
        handleSubmit(e, activeKey);
        onSelectChange(selectItems)
      }}
      onCancel={onCancel}
      centered
      closable={true}
      width={768}
      title="任务配置"
    >
      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: "1",
            label: "单个设备任务配置",
            children: (
              <div className="tabs-current-pages">
                <DeviceAssign selectItems={selectItems} onSelectChange={onSelectChange} isNoSelect={true} />
              </div>
            ),
          },
          {
            key: "2",
            label: "批量任务配置",
            children: (
              <div className="tabs-current-pages">
                <div className="tabs-current-pages-centent">
                  <div>
                    1、点击“<span style={{ color: "#06F" }}>确认</span>”后，进入批量任务配置；
                  </div>
                  <div>
                    2、您可选择“<span style={{ color: "#06F" }}>多台设备</span>”及“<span style={{ color: "#06F" }}>多个算法</span>”进行任务配置；
                  </div>
                  <div>
                    3、配置完成后需<span style={{ color: "#06F" }}>重新设置ROI区域</span>；
                  </div>
                </div>
              </div>
            ),
          },
        ]}
        onChange={(e) => {
          setActiveKey(e);
        }}
      />
    </Modal>
  );
}
