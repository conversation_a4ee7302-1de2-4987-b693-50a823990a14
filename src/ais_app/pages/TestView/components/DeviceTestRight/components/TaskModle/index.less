.task-modal {
  .ant-modal-content {
    padding: 0px;
    height: 760px;
    display: flex;
    flex-direction: column;

    .ant-modal-close {
      top: 20px;
    }
    .ant-modal-header {
      .ant-modal-title {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px;
        border-bottom: 1px solid #e7e7e7;
        color: rgba(0, 0, 0, 0.9);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 28px;
      }
    }
    .ant-modal-body {
      height: 100%;
      .ant-tabs{
        display: flex;
        height: 100%;
      }
      .ant-tabs-tab {
        padding-left: 16px;
        padding-right: 8px;
      }
      .ant-tabs-content, .ant-tabs-tabpane-active, .tabs-current-pages{
        height: 100%;
      }
      .device-select{
        width: 100%;
      }
      .tabs-current-pages {
        padding: 16px 32px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        .tabs-current-pages-centent {
          display: inline-flex;
          flex-direction: column;
          padding: 16px 24px;
          align-items: flex-start;
          border-radius: 3px;
          background: #f3f3f3;
          color: #000;

          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 32px;
        }
      }
    }
    .ant-modal-footer {
      border-top: 1px solid #e7e7e7;
      padding: 16px;
    }
  }
}
