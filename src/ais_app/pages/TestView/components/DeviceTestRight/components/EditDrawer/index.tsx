import { useRequest, useSafeState, useToggle, useUpdateEffect, useDebounce, useMemoizedFn, useSize, useFullscreen } from "ahooks";
import { useEffect, useContext, useMemo, useRef, useCallback } from "react";
import { Row, Input, Button, Col, message, Divider, Modal } from "antd";
import { useForm } from "antd/es/form/Form";
import Drawer from "@src/components/Drawer";
import { IconFont, cache, SocketEmitter } from "@cloud-app-dev/vidc";
import {
  noCachequeryAlgorithmList,
  alarmTaskInfo,
  deleteAlarmTask,
  updateAlarmTask,
  saveAlarmTask,
  coverImage,
  alarmTaskInfoById,
  batchSaveAlarmTask,
  noconfigDeviceDataInfo,
  updateAlarmTaskState,
} from "@src/service/ais_app";
import Authority from "@src/components/Authority";
import { VideoContext } from "../../Context";
import { algConfig, getConfig } from "../../AlgorithmConfig";
import { cloneDeep, without } from "lodash-es";
import AlgorithmSelect from "../AlgorithmSelect";
import AlgorithmConfigurationView from "../AlgorithmConfigurationView";
import AlgorithmConfigurationEdit from "../AlgorithmConfigurationEdit";
import DeviceAssign from "../DeviceAssign";
import RoiEdit from "../RoiEdit";
import TaskTimeRule from "../TaskTimeRule";
import "./index.less";
export default function EditDrawer({ isShow, toggleD, onFinish, setVisible, getEditRow, isEditAdd, rToggleP, isEdit, SetIsEdit, SetIsEditT }: any) {
  const { params: contextParams, updateParams }: any = useContext(VideoContext);
  const ref = useRef<HTMLDivElement>(null);
  const sRef = useRef<HTMLElement>(null);
  const RoiEditRef = useRef<HTMLElement>(null);
  const size = useSize(ref);
  const [form] = useForm();
  const [isFullscreen, { enterFullscreen, exitFullscreen }] = useFullscreen(RoiEditRef);
  const [data, setData] = useSafeState<any>([]);
  const [selectItems, setSelectItems] = useSafeState<any>([]);
  const [selectedTimeRule, setSelectedTimeRule] = useSafeState([]);
  const [roi, setRoi] = useSafeState<number[][]>();
  const [imgUrl, setImgUrl] = useSafeState<string>("");
  const [currentVoice, setCurrentVoice] = useSafeState<any>({});
  const [configedInfo, setConfigedInfo] = useSafeState<any>({});
  const [currentAlgorithm, setCurrentAlgorithm] = useSafeState<any>();
  const [params, setParams] = useSafeState<any>({ limit: 1000, offset: 1, types: [1], algorithmType: null, algorithmNoteName: "" });
  const [refsh, { toggle: rToggle }] = useToggle();
  const [warnshow, { toggle: warnToggle }] = useToggle(false);
  // const [isEdit, { toggle: SetIsEdit, setRight: SetIsEditT }] = useToggle(false);
  const [modalshow, { toggle: modalToggle }] = useToggle(false);
  const [showIsConfig, { toggle: IsToggle }] = useToggle(false);
  const [deleteshow, { toggle: deleteToggle }] = useToggle(false);
  const [isShowSele, { toggle: setIsShowSele, setRight: setIsShowSeleT }] = useToggle(false);
  const [isShowSele1, { toggle: setIsShowSele1, setRight: setIsShowSel1eT }] = useToggle(false);
  const [configing, { toggle, setLeft, setRight }] = useToggle(false);
  const [edited, { setLeft: eLeft, setRight: eRight }] = useToggle(false);
  const [ldCheck, { toggle: ldToggle, setLeft: ldSetLeft, setRight: ldSetRight }] = useToggle(false);
  const debouncedParams = useDebounce(params, { wait: 500 });
  const currentDeviceItem = useMemo(() => getEditRow ?? {}, [getEditRow]);
  const { data: ImData }: any = useRequest(isEditAdd ? noconfigDeviceDataInfo : alarmTaskInfoById, { defaultParams: [+getEditRow.id] });
  const isAllEdit = useMemo(() => {
    return getEditRow.name === "批量任务配置";
  }, [getEditRow]);
  useEffect(() => {
    if (!isAllEdit) return;
    setIsShowSeleT();
    setIsShowSel1eT();
    SetIsEditT();
  }, [isAllEdit]);
  useEffect(() => {
    if (isEdit) {
      setIsShowSeleT();
    }
  }, [isEdit]);
  useUpdateEffect(() => {
    getEditRow?.cid &&
      coverImage(getEditRow?.cid).then((res) => {
        if (res.code === 0) {
          setImgUrl(res.data?.url);
        }
      });
  }, [data]);
  const rois = useMemo(() => {
    const { width = 0, height = 0 } = size || {};
    const Roidata = ImData?.configJson?.Analysis?.Rois?.map((v: any) => {
      return v?.PolygonArea?.map((m: any) => {
        return [m.X * width, m.Y * height];
      });
    });
    return Roidata || [];
  }, [size, ImData]);
  const IsConfiged = useMemo(() => {
    return ImData?.algorithms && ImData?.algorithms?.findIndex((v: any) => v === currentAlgorithm?.id) !== -1;
  }, [ImData, currentAlgorithm]);
  const config = useMemo(() => {
    form.resetFields();
    return getConfig(currentAlgorithm?.algorithmName as string);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentAlgorithm]);
  const ConfigData = useMemo(() => {
    const dArr = ImData?.algorithms || [];
    if (!showIsConfig) {
      return data;
    }
    return data?.filter((item: any) => dArr?.findIndex((v: any) => v === item?.id) !== -1);
  }, [data, showIsConfig, currentDeviceItem]);
  const save = () => {
    form.validateFields().then((res) => {
      modalToggle();
      const params = res;
      const data = res.configJson.Analysis;
      res.configJson.Output?.Bbox?.forEach((v: number) => {
        if (v === 1) {
          params.configJson.Output.ImageBbox = true;
        }
        if (v === 2) {
          params.configJson.Output.RoiBbox = true;
        }
      });
      const [MaxWidth, MaxHeight] = data.Rule.AreaRois.Max || [];
      const [MinWidth, MinHeight] = data.Rule.AreaRois.Min || [];
      params.configJson.Analysis.Rule.AreaRois.MaxWidth = MaxWidth;
      params.configJson.Analysis.Rule.AreaRois.MaxHeight = MaxHeight;
      params.configJson.Analysis.Rule.AreaRois.MinWidth = MinWidth;
      params.configJson.Analysis.Rule.AreaRois.MinHeight = MinHeight;
      //感兴趣区域
      params.configJson.Analysis.Rois = roi;
      params.cid = currentDeviceItem?.cid;
      params.deviceName = currentDeviceItem?.name || currentDeviceItem.deviceName;
      params.algorithmId = currentAlgorithm?.id;
      params.algorithmName = currentAlgorithm?.algorithmNoteName;
      delete params.configJson.Output.Bbox;
      delete params.configJson.Analysis.Rule.AreaRois.Max;
      delete params.configJson.Analysis.Rule.AreaRois.Min;
      delete params.nickname;
      params.configJson.Analysis.Rule = [params.configJson.Analysis.Rule];
      params.enabledLinkage = ldCheck;
      params.linkageVoiceId = currentVoice;
      params.ruleConfigIds = selectedTimeRule || [];
      if (!selectedTimeRule?.length) {
        return message.warning("请选择任务执行时间规则");
      }
      if (IsConfiged) {
        updateAlarmTask({ ...params, id: configedInfo?.id }).then(
          (res) => {
            if (res.code === 0) {
              message.success("更新成功");
              eLeft();
              SocketEmitter.emit("configRefsh", "update");
              rToggleP();
              toggleD();
            } else {
              message.warning(res.message);
            }
          },
          (err) => {
            message.warning(err?.data?.message);
          },
        );
      } else {
        const resolvePath = isAllEdit ? batchSaveAlarmTask : saveAlarmTask;
        if (isAllEdit) {
          delete params.cid;
          delete params.algorithmId;
          delete params.algorithmName;
          delete params.linkageVoiceId;
          params.algorithms = currentAlgorithm.map((v: any) => v.id);
          params.cids = selectItems?.map((v: any) => v.cid);
        }
        resolvePath(params).then(
          (res) => {
            if (res.code === 0) {
              message.success("应用成功");
              eLeft();
              updateParams({
                currentDeviceItem: { ...currentDeviceItem, algorithms: [...(currentDeviceItem?.algorithms || []), currentAlgorithm?.id] },
              });
              rToggleP();
              toggleD();
              SocketEmitter.emit("configRefsh", "add");
            } else {
              message.warning(res.message);
            }
          },
          (err) => {
            message.warning(err?.data?.message);
          },
        );
      }
    });
  };
  //查询算法列表
  useUpdateEffect(() => {
    // noCachequeryAlgorithmList(debouncedParams).then((res) => setData(res));
    noCachequeryAlgorithmList(params).then((res) => {
      // const arr = algConfig?.filter((v) => v.corvette);
      // const result = res?.filter((item) => arr?.findIndex((v) => v.aname === item.algorithmName) !== -1);
      const result = res?.filter((item: any) => item.corvette);
      setData(result);
    });
  }, [debouncedParams]);
  useEffect(() => {
    if (data) {
      if (ImData?.algorithms?.length || ImData?.algorithmId) {
        const currentFind = data.find((v: any) => v?.algorithmNoteName === ImData?.algorithmName) || data[0];
        setCurrentAlgorithm(currentFind);
      } else {
        setCurrentAlgorithm(isAllEdit ? [data[0]] : data[0]);
      }
    }
    if (ImData) {
      setSelectedTimeRule(ImData?.ruleConfigIds || []);
    }
  }, [data, ImData]);
  useEffect(() => {
    if (IsConfiged) {
      setRight();
    } else {
      setLeft();
      const newconfig = {
        configed: false,
        configJson: {
          Analysis: {
            DetectInterval: config?.DetectInterval,
            Rule: {
              AreaRois: {
                MinDuration: config?.MinDuration,
                Min: config?.Min,
                Max: config?.Max,
                MinObjectNumber: config?.MinObjectNumber,
              },
              ColorRois: {
                DetectColor: config?.DetectColor,
              },
            },
          },
          Output: {
            Bbox: [1, 2],
          },
        },
      };
      form.setFieldsValue(newconfig);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [config, IsConfiged]);
  useUpdateEffect(() => {
    setCurrentVoice(undefined);

    if (!currentDeviceItem || !currentAlgorithm) {
      return;
    }
    if (IsConfiged) {
      setRight();
      alarmTaskInfo(currentDeviceItem?.cid, currentAlgorithm?.id).then((res) => {
        if (res.code === 0) {
          setConfigedInfo(res?.data);
          res.data?.enabledLinkage ? ldSetRight() : ldSetLeft();
          setCurrentVoice(res.data?.linkageVoiceId ? res.data?.linkageVoiceId + "" : undefined);
          const { configJson } = res?.data;
          const { Analysis } = configJson;
          const newconfig = {
            configed: true,
            configJson: {
              Analysis: {
                DetectInterval: Analysis?.DetectInterval,
                Rule: {
                  AreaRois: {
                    MinDuration: Analysis?.Rule[0]?.AreaRois?.MinDuration,
                    Min: [Analysis?.Rule[0]?.AreaRois?.MinWidth, Analysis?.Rule[0]?.AreaRois?.MinHeight],
                    Max: [Analysis?.Rule[0]?.AreaRois?.MaxWidth, Analysis?.Rule[0]?.AreaRois?.MaxHeight],
                    MinObjectNumber: Analysis?.Rule[0]?.AreaRois?.MinObjectNumber,
                  },
                  ColorRois: {
                    DetectColor: Analysis?.Rule[0]?.ColorRois?.DetectColor,
                  },
                },
              },
              Output: {
                Bbox: [configJson?.Output?.ImageBbox ? 1 : undefined, configJson?.Output?.RoiBbox ? 2 : undefined],
              },
            },
          };

          form.setFieldsValue(newconfig);
        }
      });
    } else {
      setLeft();
      setConfigedInfo({});
    }
  }, [currentDeviceItem, currentAlgorithm, refsh, IsConfiged]);

  useEffect(() => {
    if (!configing) {
      if (IsConfiged) {
        deleteToggle();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [configing]);
  const [preAthm, setPreAthm] = useSafeState<any>({});

  const changeAthm = (item: any) => {
    // if (edited) {
    //   warnToggle();
    //   setPreAthm(item);
    // } else {

    if (isAllEdit) {
      const isFilList = currentAlgorithm.find((v: any) => v.id == item.id);
      if (isFilList) {
        setCurrentAlgorithm(currentAlgorithm.filter((v: any) => v.id !== item.id));
      } else {
        setCurrentAlgorithm([...currentAlgorithm, { ...item }]);
      }
    } else {
      setCurrentAlgorithm(item);
    }
    // }
  };
  const changeDevice = useMemoizedFn((item: any) => {
    if (edited) {
      warnToggle();
      setPreAthm(item);
    } else {
      updateParams({ currentDeviceItem: cloneDeep(item) });
    }
  });
  useEffect(() => {
    SocketEmitter.on("editChange", changeDevice);
    setParams((old: any) => ({ ...old, algorithmType: null }));
    return () => SocketEmitter.on("editChange", changeDevice);
  }, []);
  return (
    <Drawer
      closable={false}
      open={isShow}
      width={907}
      onClose={toggleD}
      forceRender
      className="edit-drawer"
      title={
        <div className="userform-wrapper-title">
          <div style={{ display: "flex", alignItems: "center" }}>
            <div>{getEditRow.deviceName || getEditRow.name}</div>
            <Authority code="30000212">
              {isAllEdit ? null : (
                <Button
                  onClick={() => {
                    if (isEdit) {
                      Modal.confirm({
                        title: <>{`提示`}</>,
                        content: <>{`当前设备任务将不会被保存`}</>,
                        width: 416,
                        centered: true,
                        onOk: () => {
                          toggleD();
                          setVisible();
                        },
                      });
                    } else {
                      toggleD();
                      setVisible();
                    }
                  }}
                  style={{ marginLeft: 8 }}
                  type="link"
                >
                  切换设备
                </Button>
              )}
            </Authority>
          </div>
          <div>
            <IconFont type="icon-guanbi" onClick={toggleD} />
          </div>
        </div>
      }
      footer={
        <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
          {!isEditAdd && !isEdit ? (
            <div>
              <Authority code="30000213">
                <Button
                  onClick={() => {
                    Modal.confirm({
                      title: <>{`取消确认`}</>,
                      content: <>{`确认要取消任务吗？`}</>,
                      width: 416,
                      icon: (
                        <>
                          <IconFont type="icon-gaojingzhongxin" style={{ fontSize: "24px", color: "var(--primary)" }} />
                        </>
                      ),
                      centered: true,
                      onOk: () => {
                        deleteAlarmTask(getEditRow.id).then((res) => {
                          if (res.code === 0) {
                            message.success("取消成功");
                            rToggleP();
                            toggleD();
                          } else {
                            message.warning(res.message);
                          }
                        });
                      },
                      onCancel: () => {},
                    });
                  }}
                  style={{ marginRight: 8 }}
                >
                  取消任务
                </Button>
                <Button
                  onClick={() => {
                    Modal.confirm({
                      title: <>{`${getEditRow?.taskState == "STOPPED" ? "启动" : "暂停"}确认`}</>,
                      content: <>{`确认要${getEditRow?.taskState == "STOPPED" ? "启动" : "暂停"}任务吗？`}</>,
                      width: 416,
                      icon: (
                        <>
                          {getEditRow?.taskState == "STOPPED" ? (
                            <IconFont style={{ fontSize: "24px", color: "var(--primary)" }} type="icon-danchuang_qidong" />
                          ) : (
                            <IconFont type="icon-danchuang_zanting" style={{ fontSize: "24px", color: "var(--primary)" }} />
                          )}
                        </>
                      ),
                      centered: true,
                      onOk: () => {
                        updateAlarmTaskState({ ids: [getEditRow.id], taskState: getEditRow?.taskState == "STOPPED" ? "QUEUEING" : "STOPPED" }).then(
                          (res) => {
                            if (res.code === 0) {
                              message.success("操作成功");
                              rToggleP();
                              getEditRow.taskState = getEditRow?.taskState == "STOPPED" ? "QUEUEING" : "STOPPED";
                            } else {
                              message.warning(res.message);
                            }
                          },
                        );
                      },
                      onCancel: () => {},
                    });
                  }}
                >
                  {getEditRow?.taskState == "STOPPED" ? "启动" : "暂停"}任务
                </Button>
              </Authority>
            </div>
          ) : (
            <div></div>
          )}

          <div>
            {isEdit ? (
              <>
                <Button
                  onClick={() => {
                    if (isAllEdit) {
                      toggleD();
                    }
                    SetIsEdit();
                  }}
                  style={{ marginRight: 8 }}
                >
                  取消
                </Button>
                <Button type="primary" onClick={save}>
                  确定
                </Button>
              </>
            ) : (
              <Button type="primary" onClick={SetIsEdit}>
                编辑任务
              </Button>
            )}
          </div>
        </div>
      }
      getContainer={() => document.body}
    >
      <div className="edit-box">
        <div className="edit-box-title">
          <div className="edit-box-title-left">
            <div>算法选择</div>
            <div className="edit-box-title-left-tag">{currentAlgorithm?.algorithmNoteName}</div>
          </div>
          <div onClick={setIsShowSele} className="edit-box-title-right">
            {isAllEdit ? "" : isShowSele ? "收起" : "展开"}
          </div>
        </div>
        {isShowSele ? (
          <>
            <Row justify="space-between" style={{ margin: "24px 0px", alignItems: "center" }}>
              <Col>
                <Input
                  onChange={(e) => {
                    setParams((old: any) => ({ ...old, algorithmNoteName: e.target.value }));
                  }}
                  style={{ width: 346 }}
                  placeholder="请输入算法名称"
                  suffix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" />}
                />
              </Col>
              <Col>
                <Button type="link" onClick={IsToggle}>
                  {showIsConfig ? "查看全部" : "查看已配置"}
                </Button>
              </Col>
            </Row>
            <AlgorithmSelect
              isAllEdit={isAllEdit}
              currentAlgorithmP={currentAlgorithm}
              getEditRow={ImData}
              changeAthm={changeAthm}
              data={data}
              ConfigData={ConfigData}
              setParams={setParams}
            />
          </>
        ) : null}
        <Divider />
        {isAllEdit ? (
          <>
            <div className="edit-box-title" style={{ marginBottom: 16 }}>
              <div className="edit-box-title-left">
                <div>设备选择</div>
              </div>
              <div className="edit-box-title-right"></div>
            </div>
            <DeviceAssign
              selectItems={selectItems}
              onSelectChange={(v) => {
                setSelectItems(v);
              }}
            />
            {/* <Divider /> */}
            <div className="edit-box-title" style={{ marginBottom: 16 }}>
              <div className="edit-box-title-left">
                {/* <div>重复任务</div> */}
                <div className="repeat">
                  <IconFont style={{ marginRight: 8, fontSize: 16 }} type="icon-danchuang_jinggao" />
                  任务配置完成后，将覆盖原设备中的配置的任务，请及时框选ROI区域
                </div>
              </div>
              <div className="edit-box-title-right"></div>
            </div>
            <Divider />
          </>
        ) : null}

        <div className="edit-box-title">
          <div className="edit-box-title-left">
            <div>算法配置</div>

            {isEdit ? (
              <div className="edit-box-title-left-tag" style={{ color: "var(--primary)", background: "#E8F3FF" }}>
                配置中
              </div>
            ) : !isEditAdd ? (
              <div className="edit-box-title-left-tag" style={{ color: "#2BA471", background: "#E3F9E9" }}>
                已配置
              </div>
            ) : (
              <div className="edit-box-title-left-tag">未配置</div>
            )}
          </div>
          <div className="edit-box-title-right">{/* {isAllEdit ? "" : isShowSele1 ? "收起" : "展开"} */}</div>
        </div>
        {isEdit ? (
          <>
            <AlgorithmConfigurationEdit
              isAllEdit={isAllEdit}
              form={form}
              isFullscreen={isFullscreen}
              exitFullscreen={exitFullscreen}
              enterFullscreen={enterFullscreen}
            />
            {isAllEdit ? null : (
              <RoiEdit
                exitFullscreen={exitFullscreen}
                isFullscreen={isFullscreen}
                RoiEditRef={RoiEditRef}
                onChangeArea={setRoi}
                defaultRoi={ImData?.configJson?.Analysis?.Rois}
                cid={currentDeviceItem.cid}
                AlgorithmName={currentAlgorithm?.algorithmName}
                configing={configing}
                config={config}
                eRight={eRight}
              />
            )}
            <Divider />
            <TaskTimeRule setSelectedTimeRule={setSelectedTimeRule} selectedTimeRule={selectedTimeRule} />
          </>
        ) : (
          <>
            <AlgorithmConfigurationView sRef={sRef} imgUrl={imgUrl} rois={rois} ImData={ImData} isEditAdd={isEditAdd} />
            <div className="edit-box-container" style={{ marginTop: 24 }}>
              <Row className="row-content" gutter={[0, 14]}>
                <TaskTimeRule disabled={true} setSelectedTimeRule={setSelectedTimeRule} selectedTimeRule={selectedTimeRule} />
              </Row>
            </div>
          </>
        )}
      </div>
    </Drawer>
  );
}
