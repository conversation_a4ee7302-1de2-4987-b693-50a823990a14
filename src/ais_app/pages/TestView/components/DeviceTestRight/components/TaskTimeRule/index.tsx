import { useEffect, useRef, useState } from "react";
import { Checkbox, Popover } from "antd";
import Service from "@src/service/system";
import TimePeriodSelector from "@src/ais_app/pages/TimeRule/TimePeriodSelector";
import { IconFont, useHistory } from "@cloud-app-dev/vidc";
import "./index.less";
const CheckboxGroup = Checkbox.Group;
export default function TaskTimeRule({ setSelectedTimeRule, selectedTimeRule, disabled = false }: any) {
  const history = useHistory();
  const [dataRule, setDataRule] = useState([]);
  useEffect(() => {
    getList();
  }, []);

  const getList = () => {
    Service.device.timeRuleList({}).then((res) => {
      setDataRule(res.data.list);
    });
  };
  return (
    <div className="task_time_rule">
      <div className="edit-box-title">
        <div className="edit-box-title-left">
          <div>任务执行时间规则</div>
        </div>
      </div>
      {dataRule?.length ? (
        <div className="task_time_rule_content">
          <CheckboxGroup disabled={disabled} value={selectedTimeRule?.map((item: any) => item + "") || []} onChange={setSelectedTimeRule}>
            {(disabled ? dataRule.filter((item: any) => (selectedTimeRule||[])?.includes(item?.id * 1)) : dataRule).map((item: any) => (
              <div style={{ width: "100%", margin: "8px 0" }}>
                <Popover
                  content={
                    <div
                      style={{
                        width: "800px",
                      }}
                    >
                      <TimePeriodSelector
                        allData={false}
                        setAllData={() => {}}
                        timeData={item?.ruleConfig || []}
                        scheduler={item?.ruleConfig || []}
                        setScheduler={() => {}}
                        disabled={true}
                      />
                    </div>
                  }
                >
                  <Checkbox value={item.id}>{item.ruleName}</Checkbox>
                </Popover>
              </div>
            ))}
          </CheckboxGroup>
        </div>
      ) : (
        <div className="task_time_rule_content_empty">
          暂无时间规则，去
          <span
            onClick={() => {
              history.push(`/systemManagement/timeRule`);
            }}
            style={{
              cursor: "pointer",
              color: "#165DFF",
            }}
          >
            新增
          </span>
        </div>
      )}
    </div>
  );
}
