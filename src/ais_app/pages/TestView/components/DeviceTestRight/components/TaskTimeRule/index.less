.task_time_rule {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  color: #000;
  .task_time_rule_content_empty {
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    width: 100%;
    height: 77px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .task_time_rule_content{
    margin-top: 8px;
  }
  .edit-box-title-left{
    display: flex;
    align-items: center;
    &::before{
      display: inline-block;
    margin-inline-end: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
    }
  }
  :where(.css-dev-only-do-not-override-1kuana8).ant-checkbox-disabled+span {
    color: #000 !important;
  }
}
