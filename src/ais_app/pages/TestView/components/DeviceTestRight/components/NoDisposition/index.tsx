import { Form, Button, message, Input, Space, Checkbox, Tag, Table } from "antd";
import { useForm } from "antd/es/form/Form";
import { IconFont } from "@cloud-app-dev/vidc";
import { useEffect, useRef, useMemo } from "react";
import { useToggle, useRequest, useSafeState, useThrottleEffect, useSize, useAntdTable } from "ahooks";
import type { ColumnsType } from "antd/es/table";
import { queryAisAlarmTaskList, queryAlgorithmList, noconfigDeviceDataQuery } from "@src/service/ais_app";
import { DateToFormat, getAlgorithmNameById, TransMins, TransStatus } from "@src/ais_app/utils";
import DynamicGridList from "@src/ais_app/components/DynamicGridList";
import ItemCard from "../ItemCard";
import { isEmpty, uniqBy } from "lodash-es";
import Authority from "@src/components/Authority";
import "./index.less";
export default function NoNoDisposition({ setVisible, params, refsh = false, setRow, setIsEdit, setIsEditAdd }: any) {
  const [form] = useForm();
  const ref = useRef<HTMLDivElement>(null);
  const size: any = useSize(ref);
  const { data: sflist } = useRequest(() => queryAlgorithmList({ limit: 1000, offset: 0 }));
  const [isSelect, { toggle }] = useToggle(false);
  const [total, setTotal] = useSafeState(0);
  const [list, setList] = useSafeState([]);
  const [isListView, { toggle: isSetListView }] = useToggle(false);
  const [getSelectedIds, setSelectedIds]: any = useSafeState([]);
  const [tableHeight, setTableHeight] = useSafeState<number>(0);
  const [localrefsh, { toggle: lToggle, setLeft, setRight }] = useToggle(false);
  useThrottleEffect(
    () => {
      // setTableHeight(size && size);
      setTableHeight((size && size?.height - 128) || 0);
    },
    [size],
    {
      wait: 300,
    },
  );
  const getTableData = ({ current, pageSize }: { current: number; pageSize: number }, formData: Object): Promise<any> => {
    const currPageSize = !isListView ? pageSize : 20;
    let lastparams: any = {
      limit: currPageSize,
      offset: (current - 1) * currPageSize,
      isPagination: true,
      algorithmFlag: false,
      ...params,
      ...formData,
    };
    if (isEmpty(params)) {
      return Promise.resolve(() => ({
        list: [],
        total: 0,
      }));
    } else {
      return noconfigDeviceDataQuery(lastparams)
        .then((result) => (result.code === 0 ? result?.data ?? {} : {}))
        .then((res) => {
          return {
            list: res.list || [],
            total: res.totalCount ?? 0,
          };
        });
    }
  };
  const { tableProps, search } = useAntdTable(getTableData, {
    refreshDeps: [params, refsh],
    form,
  });
  //显示分页器跳转
  tableProps.pagination.showQuickJumper = true;
  tableProps.pagination.showSizeChanger = true;
  tableProps.pagination.showTotal = function (total: number, range: number[]) {
    return `共${total}项数据`;
  };
  const columns: ColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "key",
      key: "key",
      fixed: "left",
      render: (_, __, index) => index + 1,
    },

    {
      title: "设备名称",
      ellipsis: true,
      dataIndex: "deviceName",
      render: (res) => res || "-",
    },
    {
      title: "场所标签",
      ellipsis: true,
      dataIndex: "tagInfoList",
      width: 234,
      render: (res) => {
        return (
          <>
            {/* <Space> */}
            {res?.map((v: any) => {
              return (
                <Tag
                  style={{
                    color: "#" + v?.tagFontColor,
                    background: "#" + v?.tagBgColor,
                    borderColor: "#" + v?.tagFontColor,
                  }}
                >
                  {v?.tagName}
                </Tag>
              );
            })}
            {/* </Space> */}
          </>
        );
      },
    },
    {
      title: "算法名称",
      ellipsis: true,
      dataIndex: "algorithmId",
      render: (text: any) => getAlgorithmNameById(sflist || [], text),
    },
    {
      title: "操作",
      width: 165,
      dataIndex: "description",
      ellipsis: true,
      fixed: "right",
      render: (text: string, item: any) => (
        window._PLATFORM_TYPE === 2 &&
        <Space>
          <Authority code="30000211">
            <a
              onClick={() => {
                setRow(item);
                setIsEdit();
                setIsEditAdd(true);
              }}
            >
              配置
            </a>
          </Authority>
        </Space>
      ),
    },
  ];
  const reloadDeps = useMemo(() => [params, refsh, localrefsh], [params, refsh, localrefsh]);
  const loadPage = async (d: any): Promise<any> => {
    d.page ? d.page++ : (d.page = 1);
    let lastparams: any = {
      limit: 30,
      offset: (d?.page - 1) * 30,
      isPagination: true,
      algorithmFlag: false,
      ...params,
      ...form.getFieldsValue(),
    };
    const res = await noconfigDeviceDataQuery({
      ...lastparams
    });
    setTotal(res?.data?.totalCount);
    const resultData = res?.data?.list;
    d.total = res?.data?.totalCount;
    d.list ? (d.list = uniqBy([...d.list, ...resultData], "id")) : (d.list = resultData);
    setList(d.list ? (d.list = uniqBy([...d.list, ...resultData], "id")) : (d.list = resultData));
    if (localrefsh) {
      message.success("刷新成功");
      setLeft();
    }
    return d;
  };

  return (
    <div className="NoDisposition">
      <div className="NoDisposition-header">
        <div className="NoDisposition-header-left">
          {isSelect ? (
            <Checkbox>全选</Checkbox>
          ) : (
            <>
              <Form form={form} layout="inline" className="search-form">
                <Space>
                  <Form.Item name="keywords">
                    <Input
                      placeholder="输入设备或算法名称进行查找"
                      className="user-filter-btn vidc-Input"
                      allowClear={false}
                      onChange={() => {
                        search.submit()
                        setList([])
                        lToggle()
                      }}
                      suffix={
                        <IconFont
                          type="icon-renwupeizhi_shebeirenwu_sousuo"
                          style={{ cursor: "pointer", fontSize: "12px", color: "rgba(255,255,255,0.5)" }}
                        />
                      }
                      style={{ width: "260px", borderRadius: "4px" }}
                    />
                  </Form.Item>
                </Space>
              </Form>
            </>
          )}
        </div>
        <Space className="NoDisposition-header-right">
          {isSelect ? (
            <>
              <Button style={{ color: "var(--primary)", borderColor: "var(--primary)" }}>全部取消</Button>
              <Button style={{ color: "var(--primary)", borderColor: "var(--primary)" }}>全部开启</Button>
              <Button style={{ color: "var(--primary)", borderColor: "var(--primary)" }}>全部暂停</Button>
              <Button onClick={toggle}>取消</Button>
            </>
          ) : (
            <>
              <Authority code="30000212">
                <Button onClick={setVisible} type="primary">
                  任务配置
                </Button>
              </Authority>
              <Button onClick={isSetListView}>
                <IconFont type={isListView ? "icon-shipinchakan_liebiaomoshi" : "icon-kapian"} />
              </Button>
            </>
          )}
        </Space>
      </div>
      {isListView ? (
        // <div style={{ height: "100%", overflowY: "auto", overflowX: "hidden" }}>
        //   <List
        //     grid={{
        //       gutter: 16,
        //     }}
        //     dataSource={tableProps.dataSource}
        //     renderItem={(item) => (
        //       <List.Item>
        //         <ItemCard row={item} setIsEdit={setIsEdit} setRow={setRow} sflist={sflist} isNoDis={true} setIsEditAdd={setIsEditAdd} />
        //       </List.Item>
        //     )}
        //   />
        // </div>
        <DynamicGridList
          itemKey="id"
          itemHeight={220}
          itemWidth={226}
          isNoMore={(data) => data?.list?.length === total}
          loadPage={loadPage}
          reloadDeps={reloadDeps}
          renderItem={(item: any, index: any) => {
            return <ItemCard row={item} setIsEdit={setIsEdit} setRow={setRow} sflist={sflist} isNoDis={true} setIsEditAdd={setIsEditAdd} />;
          }}
        />
      ) : (
        <div ref={ref} style={{ height: "100%" }}>
          <Table
            rowSelection={
              isSelect
                ? {
                  type: "checkbox",
                  onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
                    setSelectedIds(selectedRows.map((v) => v.id));
                  },
                }
                : undefined
            }
            scroll={{ y: size?.height && size.height - 120 }}
            columns={columns}
            rowKey="id"
            {...tableProps}
          />
        </div>
      )}
    </div>
  );
}
