.AlgorithmSelect-box{
    .edit-box-container {
        padding: 16px;
        border-radius: 4px;
        background: #f3f3f3;
        margin-bottom: 16px;
        .row-content{
          color:rgba(0, 0, 0, 0.60);
          .info-item {
              display: flex;
              width: 100%;
              overflow: hidden;
              .label {
                width: 98px;
                text-align: left;
                color: rgba(0, 0, 0, 0.60);
                white-space: nowrap;
              }
              .value {
                flex: 1;
                color: rgba(0, 0, 0, 0.90);
                display: flex;
                align-items: center;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                contain: strict;
              }
            }
        }
        .image-box {
          width: 100%;
          height: max-content;
          min-height: 400px;
          background-color: var(--gray8);
          img{
            width: 100%;
            min-height: 400px;
          }
        }
        .edit-box-container-title {
          color: #000;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px;
          margin-bottom: 16px;
        }
        .edit-box-container-scenes {
          display: flex;
          color: rgba(0, 0, 0, 0.9);
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          flex-flow: wrap;
  
          .sceneItem {
            display: flex;
            padding: 5px 12px;
            align-items: center;
            border-radius: 3px;
            border: 1px solid #dcdcdc;
            white-space: nowrap;
            margin-right: 8px;
            margin-bottom: 8px;
            cursor: pointer;
          }
          .currentItem {
            border: 1px solid var(--primary);
            color: var(--primary);
          }
        }
        .edit-box-container-Chart2bNodeBoxs {
          .edit-box-container-Chart2bNodeBoxs-item {
              cursor: pointer;
            display: flex;
            //   width: 127px;
            padding: 16px;
            flex-direction: column;
            align-items: flex-start;
            border-radius: 3px;
            border: 1px solid #dcdcdc;
            background: #fff;
            color: #000;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            margin-right: 16px;
            margin-bottom: 16px;
            .edit-box-container-Chart2bNodeBoxs-item-top {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              & > img {
                width: 24px;
                height: 24px;
              }
              .title {
                word-wrap: normal;
                white-space: nowrap;
              }
            }
            .edit-box-container-Chart2bNodeBoxs-item-footer {
              width: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;
              color: rgba(0, 0, 0, 0.6);
              /* Body/Small */
              font-family: "PingFang SC";
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
            }
          }
          .edit-box-container-Chart2bNodeBoxs-item_action{
              border: 1px solid  var(--primary);
              background: #E8F3FF;
              color: var(--primary);
              .edit-box-container-Chart2bNodeBoxs-item-footer{
                  color: var(--primary);
              }
          }
        }
      }
}