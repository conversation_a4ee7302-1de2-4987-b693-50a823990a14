import { useRequest, useSafeState } from "ahooks";
import { useEffect, useMemo } from "react";
import { List } from "antd";
import { IconFont } from "@cloud-app-dev/vidc";
import { queryAlgorithmType } from "@src/service/ais_app";
import "./index.less";
export default function AlgorithmSelect({ getEditRow, changeAthm, data, currentAlgorithmP, ConfigData, setParams, isAllEdit }: any) {
  console.log(ConfigData,'ConfigData')
  const [currentType, setCurrentType] = useSafeState<number>(0);
  const [currentAlgorithm, setCurrentAlgorithm] = useSafeState<any>();
  const currentDeviceItem = useMemo(() => getEditRow ?? {}, [getEditRow]);
  const { data: sceneArr } = useRequest(queryAlgorithmType);
  //查询算法列表
  // useEffect(() => {
  //   if (data) {
  //     if(currentDeviceItem?.algorithms?.length){
  //       const currentFind = data.find((v: any) => v?.algorithmNoteName === currentDeviceItem?.algorithmName)||data[0];
  //       console.log(data,currentFind,'sadsd')
  //       setCurrentAlgorithm(currentFind);
  //     }else{
  //       setCurrentAlgorithm(data[0]);
  //     }
  //     // setCurrentAlgorithm(currentDeviceItem?.algorithms.length ?  currentDeviceItem: data[0]);
  //     // currentDeviceItem?.algorithms && currentDeviceItem?.algorithms?.findIndex((v: any) => v == item?.id) !== -1
  //   }
  // }, [data,currentDeviceItem]);
  useEffect(() => {
    setCurrentAlgorithm(isAllEdit ? currentAlgorithmP?.filter((v: any) => v):currentAlgorithmP);
  }, [currentAlgorithmP]);

  return (
    <div className="AlgorithmSelect-box">
      <div className="edit-box-container">
        <div className="edit-box-container-title">已筛选</div>
        <div className="edit-box-container-scenes">
          {sceneArr?.map((v, i) => (
            <div
              className={`sceneItem ${v?.algorithmType === currentType && "currentItem"}`}
              key={i}
              onClick={() => {
                setCurrentType(v?.algorithmType);
                setParams((old: any) => ({ ...old, algorithmType: v?.algorithmType === 0 ? null : v?.algorithmType }));
              }}
            >
              {v?.algorithmTypeName}
            </div>
          ))}
        </div>
      </div>
      <div className="edit-box-container" style={{ padding: 0, background: "#FFF" }}>
        <List
          grid={{
            gutter: 16,
          }}
          className="edit-box-container-Chart2bNodeBoxs"
          dataSource={ConfigData||[]}
          renderItem={(item: any) => (
            <div
              className={`${(isAllEdit ? currentAlgorithm?.map((v: any) => v.algorithmName).includes(item?.algorithmName) : currentAlgorithm?.algorithmName === item?.algorithmName) && "edit-box-container-Chart2bNodeBoxs-item_action"} edit-box-container-Chart2bNodeBoxs-item`}
              key={item.cid}
              onClick={() => {
                changeAthm(item);
              }}
            >
              <div className="edit-box-container-Chart2bNodeBoxs-item-top">
                <img src={`data:image/svg+xml;base64,${item?.icon}`} alt="" style={{ marginRight: 8 }} />
                <div className="title">{item?.algorithmNoteName || "-"}</div>
              </div>
              <div className="edit-box-container-Chart2bNodeBoxs-item-footer">
                <div>{item?.algorithmVersion || "-"}</div>
                {currentDeviceItem?.algorithms && currentDeviceItem?.algorithms?.findIndex((v: any) => v == item?.id) !== -1 && (
                  <IconFont type="icon-renwuchakan_lunxunrenwu_yiwancheng" style={{ color: "var(--primary)" }} />
                )}
              </div>
            </div>
          )}
        />
      </div>
    </div>
  );
}
