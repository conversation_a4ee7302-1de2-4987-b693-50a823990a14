export const algConfig = [
  {
    aname: 'sunshade',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'rubbishreveal',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'rubbishstack',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'trashbinoverflow',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'materialdeposit',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'streethang',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'roadbillboard',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'safetyhelmet',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'mask',
    corvette:true,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'reflectiveclothing',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'workingclothing',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'preventdrowning',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'humangather',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
      MinObjectNumber: 30,
    },
  },
  {
    aname: 'humancross',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'watersourcecross',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'walkdog',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'trafficcongestion',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
      MinObjectNumber: 30,
    },
  },
  {
    aname: 'motorbike',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'nonmotorillegalparking',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'motorillegalparking',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'muckvehicledump',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'muckvehiclecover',
    corvette:true,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'boat',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'cross',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'illegalproduction',
    corvette:false,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'illegalfishing',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'firefightingaccessblock',
    corvette:false,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'movebooth',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'roadsidebooth',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'outstorebooth',
    corvette:false,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 1800,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'exposedsoilcover',
    corvette:false,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
      DetectColor: [1],
    },
  },
  {
    aname: 'waterflotage',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'riverpollution',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 1800,
      Min: [40, 40],
      Max: [1000, 1000],
      DetectColor: [3],
    },
  },
  {
    aname: 'watermonitor',
    corvette:false,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
      DetectColor: [0],
    },
  },
  {
    aname: 'fire',
    corvette:true,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'smoke',
    corvette:true,
    defaultvalue: {
      DetectInterval: 300,
      MinDuration: 3600,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
  {
    aname: 'imageabnormaldiagnostics',
    corvette:false,
    defaultvalue: {
      DetectInterval: -1,
      MinDuration: 1,
      Min: [40, 40],
      Max: [1000, 1000],
    },
  },
];
export const getConfig = (v: string) => {
  return algConfig?.find((item) => item.aname === v)?.defaultvalue;
};
