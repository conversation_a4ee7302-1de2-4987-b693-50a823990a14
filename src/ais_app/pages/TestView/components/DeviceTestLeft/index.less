.DeviceTestLeft{
    width: 100%;
    height: 100%;
    overflow: auto;
    padding: 16px 0px;
    color: var(--gray2);
    .header {
        padding-left: 16px;
        color: rgba(0, 0, 0, 0.9);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 16px;
        border-bottom: 1px solid #e7e7e7;
        padding-bottom: 16px;
    
        .header-right {
          color: #165dff;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
          cursor: pointer;
        }
      }
    .content{
        margin-top: 12px;
        padding: 0px 16px ;
        .ant-form-item{
            margin-bottom: 14px;
        }
        .choseBtn {
            cursor: pointer;
            // position: absolute;
            // right: 8px;
            // top: -43px;
            color: #165dff;
            /* Body/Medium */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            white-space: nowrap;
          }
    }
}
.header-right-details {
    width: 372px;
    .header-right-details-item {
      .header-right-details-item-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        .left {
          color: rgba(0, 0, 0, 0.9);
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 157.143% */
        }
        .right {
          color: #165dff;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
          cursor: pointer;
        }
      }
      .header-right-details-item-content {
        margin-bottom: 10px;
        .header-right-details-item-content-it {
          color: rgba(0, 0, 0, 0.9);
          font-family: "PingFang SC";
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 166.667% */
          display: inline-flex;
          padding: 2px 8px;
          align-items: center;
          border-radius: 3px;
          background: #f3f3f3;
          margin-bottom: 8px;
          margin-right: 8px;
          &:hover{
            color: var(--primary);
            background: #E8F3FF;
          }
        }
      }
    }
  }
  