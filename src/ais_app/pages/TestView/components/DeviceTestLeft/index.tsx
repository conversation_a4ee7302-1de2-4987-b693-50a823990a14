import PackUp from "@src/ais_app/components/PackUp";
import MyCheck from "@src/ais_app/components/MyCheck";
import { Form, Popover } from "antd";
import { useCallback, useRef } from "react";
import { queryAlgorithmList } from "@src/service/ais_app";
import { IconFont } from "@cloud-app-dev/vidc";
import { statusArr } from "@src/ais_app/utils";
import { useRequest } from "ahooks";
import DevicePoints from "@src/ais_app/components/DevicePoints";
import "./index.less";

export default function DeviceTestLeft({ setParams, paramsData }: any) {
  const ref1: any = useRef(null);
  const ref2: any = useRef(null);
  const ref3: any = useRef(null);
  const [form] = Form.useForm();
  const { data } = useRequest(queryAlgorithmList, { defaultParams: [{ limit: 1000, offset: 1 }] });
  const onChange = (points: any[]) => {
    const cids = points?.map((v) => v.cid);
    setParams((old: any) => ({ ...old, cids, points }));
  };
  const deleteDevice = (item: any) => {
    const arr = paramsData?.points.filter((v: any) => v.cid !== item.cid);
    if (ref3.current) {
      ref3.current?.setStatePoint((old: any) => {
        return { ...old, points: arr };
      });
    }
    onChange(arr);
  };
  return (
    <div className="DeviceTestLeft">
      <div className="header">
        <div>检索条件</div>
        <Popover
          placement="bottom"
          title={null}
          content={
            <div className="header-right-details">
              <div className="header-right-details-item">
                <div className="header-right-details-item-header">
                  <div className="left">已选设备</div>
                  <div
                    onClick={() => {
                      onChange([]);
                    }}
                    className="right"
                  >
                    重置
                  </div>
                </div>
                <div className="header-right-details-item-content">
                  {paramsData?.points?.length
                    ? paramsData.points.map((item: any, index: number) => {
                        return (
                          <div key={index} className="header-right-details-item-content-it">
                            {item.name}
                            <IconFont onClick={() => deleteDevice(item)} type="icon-guanbi" style={{ marginLeft: 8, cursor: "pointer" }}></IconFont>
                          </div>
                        );
                      })
                    : null}
                </div>
              </div>
              <div className="header-right-details-item">
                <div className="header-right-details-item-header">
                  <div className="left">已选算法</div>
                  <div
                    onClick={() => {
                      setParams((old: any) => ({ ...old, algorithmIds: [] }));
                      if (ref1.current) {
                        ref1.current?.setCheckedList([]);
                      }
                    }}
                    className="right"
                  >
                    重置
                  </div>
                </div>
                <div className="header-right-details-item-content">
                  {data
                    ?.map((v) => ({ id: v?.id, value: v?.algorithmNoteName }))
                    .filter((item) => paramsData?.algorithmIds?.includes(item.id))
                    .map((item, index) => {
                      return (
                        <div key={index} className="header-right-details-item-content-it">
                          {item.value}
                          <IconFont
                            onClick={() => {
                              const arr = paramsData?.algorithmIds.filter((v: any) => v !== item.id);
                              setParams((old: any) => ({ ...old, algorithmIds: arr }));
                              ref1.current?.setCheckedList(arr);
                            }}
                            type="icon-guanbi"
                            style={{ marginLeft: 8, cursor: "pointer" }}
                          ></IconFont>
                        </div>
                      );
                    })}
                </div>
              </div>
              <div className="header-right-details-item">
                <div className="header-right-details-item-header">
                  <div className="left">已选分析状态</div>
                  <div
                    onClick={() => {
                      setParams((old: any) => ({ ...old, processFlags: [] }));
                      if (ref2.current) {
                        ref2.current?.setCheckedList([]);
                      }
                    }}
                    className="right"
                  >
                    重置
                  </div>
                </div>
                <div className="header-right-details-item-content">
                  {statusArr
                    .filter((item) => paramsData?.taskState?.includes(item.id))
                    .map((item, index) => {
                      return (
                        <div key={index} className="header-right-details-item-content-it">
                          {item.value}
                          <IconFont
                            onClick={() => {
                              const arr = paramsData?.taskState.filter((v: any) => v !== item.id);
                              setParams((old: any) => ({ ...old, taskState: arr }));
                              ref2.current?.setCheckedList(arr);
                            }}
                            type="icon-guanbi"
                            style={{ marginLeft: 8, cursor: "pointer" }}
                          ></IconFont>
                        </div>
                      );
                    })}
                </div>
              </div>
            </div>
          }
          arrow={false}
        >
          <div className="header-right">查看</div>
        </Popover>
      </div>
      <div className="content">
        <Form
          form={form}
          onValuesChange={() => {
            setParams((old: any) => ({ ...old, ...form.getFieldsValue(true) }));
          }}
        >
          {/* <Form.Item name="taskName">
            <Input placeholder="请输入任务名称" className="vidc-Input" allowClear={false} suffix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" />} />
          </Form.Item> */}
          <DevicePoints
            ref={ref3}
            packUpIsTrue={true}
            onChange={(v) => {
              onChange(v);
            }}
          />
          <PackUp
            title="算法"
            clearD={
              <div
                className="check-all"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (ref1.current) {
                    ref1.current.onCheckClear();
                  }
                }}
              >
                <span className="choseBtn">重置</span>
              </div>
            }
          >
            <Form.Item name="algorithmIds">
              <MyCheck ref={ref1} itemcheckgroup={data?.map((v) => ({ id: v?.id, value: v?.algorithmNoteName }))} />
            </Form.Item>
          </PackUp>
          <PackUp
            title="分析状态"
            clearD={
              <div
                // className="check-all"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (ref2.current) {
                    ref2.current.onCheckClear();
                  }
                }}
              >
                <span className="choseBtn">重置</span>
              </div>
            }
          >
            <Form.Item name="taskState">
              <MyCheck ref={ref2} itemcheckgroup={statusArr} />
            </Form.Item>
          </PackUp>
        </Form>
      </div>
    </div>
  );
}
