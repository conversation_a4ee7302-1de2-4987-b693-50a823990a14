.DeviceTestLayout {
  width: 100%;
  height: 100%;
  padding: 16px 24px;
  color: var(--content-bg);
  display: flex;
  flex-direction: column;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    height: 32px;
    background: linear-gradient(90deg, rgba(36, 143, 250, 0.65) -1.22%, rgba(36, 143, 250, 0) 100%);
    margin-bottom: 16px;
    .text {
      font-size: var(--fs-large);
      font-weight: 600;
    }
    .change-mode-btns {
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 57px;
      .action.icon {
        color: var(--primary);
      }
      .icon {
        width: 16px;
        height: 16px;
        color: var(--gray8);
        cursor: pointer;
      }
      .line {
        width: 1px;
        height: 12px;
        background: var(--gray8);
        position: relative;
        top: 3px;
      }
    }
  }
  .static-info {
    display: flex;
    align-items: center;
    height: 32px;
    justify-content: space-between;
    .text {
      span {
        color: var(--secondary2);
      }
    }
    .handle-more {
    }
  }
}
.handle-popover {
  .ant-popover-arrow {
    display: none;
  }
  .ant-popover-inner {
    background-color: var(--primary-dark);
    border-radius: var(--radius2);
    .ant-popover-inner-content {
      padding: 4px 0px;
      color: var(--gray2);
      .handles {
        .hbtn:hover {
          background-color: var(--secondary2);
        }
        .hbtn {
          cursor: pointer;
          margin: 0;
          text-align: center;
          line-height: 32px;
          width: 112px;
          height: 32px;
        }
      }
    }
  }
}
