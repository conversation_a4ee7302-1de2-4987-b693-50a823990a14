import { IconFont } from '@cloud-app-dev/vidc';
import { useToggle, useSafeState } from 'ahooks';
import { updateAlarmTaskState } from '@src/service/ais_app';
import { Button, message, Popover } from 'antd';
import OperatingBounced from '@src/components/OperatingBounced';
import CardMode from '../CardMode';
import TableMode from '../TableMode';
import './index.less';

export default function DeviceTestLayout({ params }: { params: any }) {
  const [mode, { toggle }] = useToggle(true);
  const [modalMode, { setLeft, setRight }] = useToggle(false);
  const [isShow, { toggle: showToggle }] = useToggle(false);
  const [isMore,{setLeft:MsetLeft,setRight:MsetRight}] = useToggle(false)
  const [selectedIds, setSelectedIds] = useSafeState<number[]>([]);
  const [Ids, setIds] = useSafeState<number[]>([]);
  const [refsh, { toggle: rToggle }] = useToggle(false);
  const [staticInfo, setStaticInfo] = useSafeState({
    total: 0,
    runing: 0,
  });
  const handles = (type: boolean,isMore : boolean = true) => {
    type ? setRight() :setLeft()
    isMore ? MsetRight() : MsetLeft()
    showToggle();
  };
  const onOk = () => {
    showToggle();
    updateAlarmTaskState({ ids: isMore?selectedIds:Ids, taskState: modalMode ? 'QUEUEING' : 'STOPPED' }).then((res) => {
      if (res.code === 0) {
        message.success('操作成功');
        rToggle();
      } else {
        message.warning(res.message);
      }
    });
  };
  const content = (
    <div className="handles">
      <p
        className="hbtn"
        onClick={() => {
          handles(true);
        }}
      >
        批量启动
      </p>
      <p
        className="hbtn"
        onClick={() => {
          handles(false);
        }}
      >
        批量暂停
      </p>
    </div>
  );
  return (
    <div className="DeviceTestLayout">
      <div className="title">
        <div className="text">任务展示</div>
        <div className="change-mode-btns">
          <div className={`icon ${mode && 'action'}`}>
            <IconFont type="icon-renwuchakan_shebeirenwu_liebiaomoshi" onClick={toggle} />
          </div>
          <div className="line"></div>
          <div className={`icon ${!mode && 'action'}`}>
            <IconFont type="icon-renwuchakan_shebeirenwu_kapianmoshi" onClick={toggle} />
          </div>
        </div>
      </div>
      <div className="static-info">
        <div className="text">
          分析中/总数：<span>{staticInfo?.runing}</span>/{staticInfo?.total}
        </div>
        {mode && (
          <div className="handle-more" style={{ pointerEvents: selectedIds?.length === 0 ? 'none' : 'unset' }}>
            <Popover content={content} trigger="click" placement="bottom" overlayClassName="handle-popover">
              <Button type="primary" disabled={selectedIds?.length === 0 ? true : false}>
                <IconFont type="icon-renwuchakan_shebeirenwu_piliangcaozuo" /> 批量操作
              </Button>
            </Popover>
          </div>
        )}
      </div>
      {mode ? (
        <TableMode params={params} refsh={refsh} setIds={setIds} setStaticInfo={setStaticInfo} setSelectedIds={setSelectedIds} handles={handles} />
      ) : (
        <CardMode params={params} refsh={refsh} setIds={setIds} setStaticInfo={setStaticInfo} setSelectedIds={setSelectedIds} handles={handles} />
      )}
      <OperatingBounced
        title={`${isMore ? '批量' :''}${modalMode ? '启动' : '暂停'}确认`}
        content={`确认要${isMore ? '批量' :''}${modalMode ? '启动' : '暂停'}任务吗？`}
        icon={
          modalMode ? (
            <IconFont style={{ fontSize: '32px', color: '#1DBBFF' }} type="icon-danchuang_qidong" />
          ) : (
            <IconFont type="icon-danchuang_zanting" style={{ fontSize: '32px', color: '#1DBBFF' }} />
          )
        }
        isShow={isShow}
        onCancel={showToggle}
        onOk={onOk}
      />
    </div>
  );
}
