import { useHistory } from '@cloud-app-dev/vidc';
import { queryAisAlarmTaskList,queryAlgorithmList } from '@src/service/ais_app';
import { useAntdTable, useSize, useThrottleEffect, useSafeState, useRequest } from 'ahooks';
import { Table } from 'antd';
import { useEffect, useRef } from 'react';
import { DateToFormat, getAlgorithmNameById, TransMins, TransStatus } from '@src/ais_app/utils';
import './index.less';
import { isEmpty } from 'lodash-es';

export default function TableMode({
  params,
  setSelectedIds,
  handles,
  setStaticInfo,
  setIds,
  refsh
}: {
  setStaticInfo: (v: any) => void;
  params: any;
  setSelectedIds: (v: number[]) => void;
  handles: (v: boolean,m:boolean) => void;
  setIds:(v: number[]) => void;
  refsh:boolean;
}) {
  const {data:sflist} = useRequest(() => queryAlgorithmList({limit:1000,offset:0}))
  const ref = useRef<HTMLDivElement>(null);
  const size = useSize(ref);
  const history = useHistory();
  const [tableHeight, setTableHeight] = useSafeState<number>(0);
  useThrottleEffect(
    () => {
      setTableHeight((size && size?.height - 128) || 0);
    },
    [size],
    {
      wait: 300,
    }
  );
  const getTableData = ({ current, pageSize }: { current: number; pageSize: number }, formData: Object): Promise<any> => {
    let lastparams: any = {
      limit: pageSize,
      offset: (current - 1) * pageSize,
      isPagination:true,
      ...params,
    };
    if (isEmpty(params)) {
      return Promise.resolve(() => ({
        list: [],
        total: 0,
      }));
    } else {
      return queryAisAlarmTaskList(lastparams);
    }
  };
  const { tableProps } = useAntdTable(getTableData, {
    refreshDeps: [params,refsh],
  });
  const statusChange = (v: any) => {
    setIds([v?.id])
    handles(v?.taskState === 'STOPPED' ? true : false,false);
  };
  //显示分页器跳转
  tableProps.pagination.showQuickJumper = true;
  tableProps.pagination.showSizeChanger = true;
  tableProps.pagination.showTotal = function (total: number, range: number[]) {
    return `共${total}条数据`;
  };
  useEffect(() => {
    const runData = tableProps?.dataSource?.filter((v: any) => v.taskState === 'RUNNING');
    setStaticInfo({ total: tableProps?.dataSource?.length ?? 0, runing: runData?.length ?? 0 });
  }, [tableProps?.dataSource]);
  const columns = [
    {
      title: '任务id',
      ellipsis: true,
      dataIndex: 'id',
      render: (text: string) => text,
    },
    {
      title: '任务名称',
      ellipsis: true,
      width: 280,
      dataIndex: 'taskName',
    },
    {
      title: '算法名称',
      ellipsis: true,
      width: 200,
      dataIndex: 'algorithmId',
      render: (text: number) => getAlgorithmNameById(sflist||[],text),
    },
    {
      title: '分析频率',
      dataIndex: 'detectInterval',
      ellipsis: true,
      render: (text: string) => text =='-1' ? 'I帧' : TransMins(+text),
    },
    {
      title: '场景持续时间',
      dataIndex: 'duration',
      ellipsis: true,
      render: (text: string) => TransMins(+text) || '-',
    },
    {
      title: '运行状态',
      ellipsis: true,
      dataIndex: 'taskState',
      render: (text: string) => TransStatus(text),
    },
    {
      title: '报警数量',
      ellipsis: true,
      dataIndex: 'alarmSum',
    },
    {
      title: '最近报警时间',
      width: 160,
      ellipsis: true,
      dataIndex: 'lastAlarmTime',
      render: (text: string) => DateToFormat(text, 'YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      width: 120,
      dataIndex: 'description',
      ellipsis: true,
      render: (text: string, item: any) => (
        <div className="cz">
          <a
            onClick={() => {
              history.push(`/aisApp/testDetails?${item?.id}`);
            }}
          >
            详情
          </a>
          <a
            onClick={() => {
              statusChange(item);
            }}
          >
            {item?.taskState === 'STOPPED' ? '启动' : '暂停'}
          </a>
        </div>
      ),
    },
  ];
  return (
    <div className="TableMode" ref={ref}>
      <Table
        rowSelection={{
          type: 'checkbox',
          onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
            setSelectedIds(selectedRows.map((v) => v.id));
          },
        }}
        scroll={{ y: tableHeight }}
        columns={columns}
        rowKey="id"
        {...tableProps}
      />
    </div>
  );
}
