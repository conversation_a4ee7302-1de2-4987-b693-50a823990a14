import { IconFont, List,  useHistory ,DynamicGridList} from '@cloud-app-dev/vidc';
import { queryAisAlarmTaskList, queryAlgorithmList } from '@src/service/ais_app';
import { getAlgorithmNameById, TransStatus } from '@src/ais_app/utils';
import { useToggle, useRequest } from 'ahooks';
import { isEmpty } from 'lodash-es';
import { useMemo } from 'react';
import './index.less';

export default function CardMode({
  params,
  setSelectedIds,
  handles,
  setIds,
  setStaticInfo,
  refsh,
}: {
  setStaticInfo: (v: any) => void;
  params: any;
  setIds: (v: number[]) => void;
  setSelectedIds: (v: number[]) => void;
  handles: (v: boolean,m:boolean) => void;
  refsh: boolean;
}) {
  const history = useHistory();
  const { data: sflist } = useRequest(() => queryAlgorithmList({ limit: 1000, offset: 0 }));
  const loadPage = async (d: any): Promise<any> => {
    const res = await queryAisAlarmTaskList({ offset: (d?.page || 0) * 30, limit: 30,isPagination:true, imageFlag: true, ...params });
    const resultData = res?.list;
    d.total = res?.total
    d.page ? d.page++ : (d.page = 1);
    d.list ? (d.list = [...d.list, ...resultData]) : (d.list = resultData);
    const runData = d.list?.filter((v: any) => v.taskState === 'RUNNING');
    setStaticInfo({ total: d.list?.length ?? 0, runing: runData?.length ?? 0 });
    return new Promise((resolve) => {
      setTimeout(() => resolve(d), 500);
    });
  };
  const reloadDeps = useMemo(() => [params, refsh], [params, refsh]);
  const statusChange = (v: any) => {
    setIds([v?.id]);
    handles(v?.taskState === 'STOPPED' ? true : false,false);
  };
  return (
    <div className="CardMode">
      {!isEmpty(params) && (
        <DynamicGridList
          itemKey="id"
          itemHeight={238}
          itemWidth={280}
          reloadDeps={reloadDeps}
          isNoMore={data=>data?.list?.length === data?.total}
          loadPage={loadPage}
          renderItem={(item: any, index: any) => {
            return (
              <div className="item" key={item.id + '_' + index}>
                <div className="item-img">
                  <img src={item.imageBase64} />
                </div>
                <span className="item-adress">
                  <IconFont type="icon-gaojingzhongxin_shexiangji" /> {item.deviceName || '-'}
                </span>
                <span className="item-name" title={getAlgorithmNameById(sflist || [], item?.algorithmId)}>
                  <IconFont type="icon-renwuchakan_shebeirenwu_suanfa" /> {getAlgorithmNameById(sflist || [], item?.algorithmId)}
                </span>
                <span className="item-result" title={TransStatus(item.taskState)}>
                  <IconFont type="icon-renwuchakan_shebeirenwu_jindu" /> {TransStatus(item.taskState)}
                  <span className="alarm-box">{item.alarmSum ?? 0}个告警</span>
                </span>
                <div className="handle-box">
                  <div
                    className="details"
                    onClick={() => {
                      history.push(`/aisApp/testDetails?${item?.id}`);
                    }}
                  >
                    <IconFont type="icon-renwuchakan_shebeirenwu_xiangqing" /> <span>详情</span>{' '}
                  </div>
                  <div className="line"></div>
                  <div
                    className="handle"
                    onClick={() => {
                      statusChange(item);
                    }}
                  >
                    {item?.taskState === 'STOPPED' ? <IconFont type="icon-renwupeizhi_lunxunrenwu_qiyong" />:<IconFont type="icon-renwuchakan_shebeirenwu_zanting" />}
                    <span>{item?.taskState === 'STOPPED' ? '启动' : '暂停'}</span>
                  </div>
                </div>
              </div>
            );
          }}
        />
      )}
    </div>
  );
}
