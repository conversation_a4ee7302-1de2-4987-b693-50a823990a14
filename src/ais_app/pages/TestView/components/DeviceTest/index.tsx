import VideoLayout from '@src/components/VideoLayout'
import { useSafeState } from 'ahooks';
import DeviceTestLeft from '../DeviceTestLeft';
import DeviceTestRight from '../DeviceTestRight';
export default function DeviceTest() {
  const [params, setParams] = useSafeState<any>({});
  return <VideoLayout leftControl={<DeviceTestLeft paramsData={params} setParams={setParams} />} RightContent={<DeviceTestRight params={params} />} />;
}
