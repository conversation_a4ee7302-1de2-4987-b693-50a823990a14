import TestTabsLayout from '@src/ais_app/components/TestTabsLayout';
import LoopTest from './components/LoopTest';
import DeviceTest from './components/DeviceTest';
import './index.less';
import { useSafeState } from 'ahooks';
export default function TestView() {
  const [currentKey, setCurrentKey] = useSafeState<number>(0);
  return (
    <div className="TestView">
      {/* <TestTabsLayout tabsName={['设备任务', '轮巡任务']} setCurrentKey={setCurrentKey} currentKey={currentKey}>
        {currentKey === 0 ? <DeviceTest /> : <LoopTest />}
      </TestTabsLayout> */}
      <DeviceTest />
    </div>
  );
}
