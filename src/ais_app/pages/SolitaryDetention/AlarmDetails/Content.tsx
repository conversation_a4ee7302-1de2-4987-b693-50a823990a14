import { Button, Form, Radio, Input, message, Modal, Tag } from "antd";
import { SocketEmitter } from "@cloud-app-dev/vidc";
import { IconFont, useHistory, Picture } from "@cloud-app-dev/vidc";
import { queryMergeAlarmInfo, hideMergeAlarmInfo, processMergeAlarmInfo, queryAlgorithmList, queryLocalVideo } from "@src/service/ais_app";
import ChildTaskCarousel from "../components/ChildTaskCarousel";
import warnImg from "@src/assets/image/warn.png";
import ChildItemNode from "../components/ChildTaskCarousel/ChildItemNode";
import GradientTitle from "@src/components/GradientTitle";
import OperatingBounced from "@src/components/OperatingBounced";
import { useForm } from "antd/es/form/Form";
import { ISegmentType } from "@cloud-app-dev/vidc/es/Player/player";
import { useRequest, useSafeState, useSize, useToggle, useUpdateEffect } from "ahooks";
import { db } from "../components/AlarmRight";
import tx from "@src/assets/image/tx.png";
import { RecordItem } from "@cloud-app-dev/vidc/es/ScreenPlayer/interface";
import { useEffect, useRef, useState, useContext } from "react";
import { DateToFormat, getAlgorithmNameById, TransMins } from "@src/ais_app/utils";
import Authority from "@src/components/Authority";
import { size } from "lodash-es";
import Player from "../../../components/Player";
import { VideoContext, VideoItem } from "../../video/Context";
import dayjs from "dayjs";
import moment from "moment";
import { useLocation } from "react-router-dom";

import "./index.less";
export default function AlarmDetails() {
  const { params, set, clear, list, updateParams }: any = useContext(VideoContext);
  const [form] = useForm();
  const history = useHistory();
  const ref = useRef<HTMLDivElement>(null);
  const imgsize = useSize(ref);
  const [currentItem, setCurrentItem] = useSafeState<any>({});
  const [currentItemImg, setCurrentItemImg] = useSafeState<any>({});
  const [currentRects, setCurrentRects] = useSafeState<any>([]);
  const [isModalOpen1, setIsModalOpen1] = useState(false);
  const [currentItemdetails, setCurrentItemdetails] = useSafeState<any>({});
  const { data: sflist } = useRequest(() => queryAlgorithmList({ limit: 1000, offset: 0 }));
  const [modalshow, { toggle: modalToggle }] = useToggle(false);
  const [alarmInfo, setAlarmInfo] = useSafeState<{ list: any[]; total: number }>({ list: [], total: 0 });
  const [canCut, { setLeft, setRight }] = useToggle(true);
  const runhid = () => {
    hideMergeAlarmInfo(currentItemdetails?.id, currentItemdetails?.hideFlag ? 0 : 1).then((res) => {
      if (res.code === 0) {
        message.success("操作成功");
        modalToggle();
        SocketEmitter.emit("refrshList");
      } else {
        message.warning(res.message);
      }
    });
  };
  const handle = () => {
    form.validateFields().then((res) => {
      const params = {
        id: currentItem?.id,
        ...res,
      };
      processMergeAlarmInfo(params).then((res) => {
        if (res?.code === 0) {
          message.success("处置成功");
          SocketEmitter.emit("refrshList");
        } else {
          message.warning(res.message);
        }
      });
    });
  };
  const GetList = () => {
    db.get({ id: "alarminfo" })?.then((res: any) => {
      setAlarmInfo({ list: res?.value, total: res?.total });
    });
  };
  useEffect(() => {
    GetList();
    SocketEmitter.on("listChange", GetList);
    SocketEmitter.emit("clearMessage");
    return () => SocketEmitter.on("listChange", GetList);
  }, []);
  const { state } = useLocation();
  useUpdateEffect(() => {
    if (!state?.id) return;
    queryMergeAlarmInfo(state?.id).then((res) => {
      if (res.code === 0) {
        setCurrentItem(res?.data);
        setCurrentItemdetails(res?.data);
        setCurrentItemImg(res?.data?.aisAlarmInfo[0] ? { ...res?.data?.aisAlarmInfo[0], indexC: 0 } : {});
      } else {
        queryMergeAlarmInfo(alarmInfo?.list[0]?.id).then((res) => {
          setCurrentItem(res?.data);
          setCurrentItemdetails(res?.data);
          setCurrentItemImg(res?.data?.aisAlarmInfo[0] ? { ...res?.data?.aisAlarmInfo[0], indexC: 0 } : {});
        });
      }
    });
    // setCurrentItem(alarmInfo?.list?.find((v) => v?.id === id) || alarmInfo?.list[0])
  }, [state, alarmInfo]);
  useEffect(() => {
    history.location?.search === "?formsocket" ? setRight() : setLeft();
  }, [history.location]);
  useEffect(() => {
    form.resetFields();
  }, [currentItemdetails]);
  const onPre = () => {
    // const Index = alarmInfo?.list?.findIndex((v) => v?.id == currentItem?.id);
    const Index = currentItemdetails?.aisAlarmInfo?.findIndex((v: any) => v?.storageImageUrl == currentItemImg?.storageImageUrl);
    if (Index !== -1 && Index > 0) {
      let currentItem = currentItemdetails?.aisAlarmInfo[Index - 1];
      currentItem.indexC = Index - 1;
      setCurrentItemImg(currentItem);
      // const currentItem = alarmInfo?.list[Index - 1];
      // queryMergeAlarmInfo(currentItem?.id).then((res) => {
      //   setCurrentItem(res?.data);
      //   setCurrentItemdetails(res?.data);
      //   setCurrentItemImg(res?.data?.aisAlarmInfo[0] || {});
      //   history.replace("/aisApp/alarmDetails", { id: currentItem?.id });
      // });
    } else {
      message.warning("当前已是第一项");
    }
  };
  const onNext = () => {
    // const Index = alarmInfo?.list?.findIndex((v) => v?.id == currentItem?.id);
    const Index = currentItemdetails?.aisAlarmInfo?.findIndex((v: any) => v?.storageImageUrl == currentItemImg?.storageImageUrl);

    if (Index !== -1 && Index < size(currentItemdetails?.aisAlarmInfo) - 1) {
      let currentItem = currentItemdetails?.aisAlarmInfo[Index + 1];
      currentItem.indexC = Index + 1;
      setCurrentItemImg(currentItem);
      // const currentItem = alarmInfo?.list[Index + 1];
      // queryMergeAlarmInfo(currentItem?.id).then((res) => {
      //   setCurrentItem(res?.data);
      //   setCurrentItemdetails(res?.data);
      //   setCurrentItemImg(res?.data?.aisAlarmInfo[0] || {});
      //   history.replace("/aisApp/alarmDetails", { id: currentItem?.id });
      // });
    } else {
      message.warning("当前已是最后一项");
    }
  };
  const toPage = () => {
    return;
    if (!currentItemdetails?.taskType) {
      history.push(`/aisApp/testDetails?${currentItemdetails?.taskId}`);
    } else {
      history.push("/aisApp/testConfig", { edit: 1 });
    }
  };
  const completionSegments = (start: number, end: number, segments: ISegmentType[]): ISegmentType[] => {
    const arr = [] as ISegmentType[];
    if (segments.length > 0 && start < +segments[0].beginTime) {
      arr.push({ beginTime: start, endTime: +segments[0].beginTime });
    }
    segments.reduce((prev, current, idx) => {
      if (arr.length === 0 && idx === 0) {
        prev.push(current);
      } else if (idx > 0 && segments[idx - 1].endTime !== current.beginTime) {
        prev.push({ beginTime: segments[idx - 1].endTime, endTime: current.beginTime }, current);
      } else {
        prev.push(current);
      }

      return prev;
    }, arr);

    if (end > +segments[segments.length - 1].endTime) {
      arr.push({ beginTime: segments[segments.length - 1].endTime, endTime: end });
    }
    return arr;
  };
  const queryRecord = ({ cid, date }: any) => {
    const m = moment(date);
    // const beginTime = m.set({ hours: 0, minutes: 0, seconds: 0 }).unix();
    // const endTime = m.set({ hours: 23, minutes: 59, seconds: 59 }).unix();
    const beginTime = m.clone().subtract(30, "seconds").unix();
    const endTime = m.clone().add(30, "seconds").unix();

    return (
      queryLocalVideo({ cid, beginTime, endTime })
        .then((res) => res.data.map((v: any) => ({ url: v.play_url, beginTime: v.begin * 1000, endTime: v.end * 1000 })))
        // .then((res) => completionSegments(beginTime, endTime, res))
        .then((res) => completionSegments(beginTime * 1000, endTime * 1000, res))
        .catch((e) => {
          console.error(e);
          message.warning("未获取到录像片段");
          return Promise.reject(e);
        })
    );
  };
  useEffect(() => {
    const rects = currentItemImg?.boxCoordinate?.map((v: string) => {
      return {
        rect: v.split(","),
        type: "face",
      };
    });
    setCurrentRects(rects);
  }, [currentItemImg]);
  useEffect(() => {
    if (currentItem && currentItem.cid && isModalOpen1) {
      const item = {
        name: "",
        type: "flv",
        cid: currentItem.cid,
        date: currentItemImg.alarmTime,
        recordType: 2,
        loading: true,
        customTimeLine: false,
      } as VideoItem;
      set(item, 0);
      queryRecord(item)
        .then((segments: any) => {
          item.loading = false;
          item.segments = segments;
          set(item, 0);
        })
        .catch((e: any) => {
          console.error(e);
          set(undefined, 0);
        });
    }
  }, [currentItem, set, isModalOpen1, currentItemImg]);
  return (
    <div className="alarm-details">
      <div className="alarm-details-content">
        <div className="alarm-details-content-title">
          告警详情{" "}
          {currentItemdetails?.tagInfoList?.map((item: any) => {
            return (
              <Tag
                style={{
                  color: "#" + item?.tagFontColor,
                  background: "#" + item?.tagBgColor,
                  borderColor: "#" + item?.tagFontColor,
                }}
              >
                {item.tagName}
              </Tag>
            );
          })}
        </div>
        <div className="alarm-details-content-box">
          <div className="alarm-details-content-left">
            <div className="alarm-details-content-left-header">
              <div className="alarm-details-content-left-header-left">
                {/* 任务名称 */}
                <div>
                  <a className="value" onClick={toPage} style={{ color: "var(--primary)" }}>
                    {currentItemdetails?.taskName || "-"}
                  </a>
                </div>
                {/* 设备名称   暂无 */}
                <div>{currentItemdetails?.deviceName || "-"}</div>
                {/* 算法名称 */}
                <div>{getAlgorithmNameById(sflist || [], currentItem?.algorithmId)}</div>
                {/* 时间 */}
                <div>{DateToFormat(currentItemdetails?.updatedTime, "YYYY-MM-DD HH:mm:ss")}</div>
              </div>
              <div>
                {/* 隐藏 */}
                <Authority code="30000102">
                  <Button
                    style={{
                      borderColor: "#165DFF",
                      color: "#165DFF",
                    }}
                    onClick={modalToggle}
                  >
                    <IconFont type={currentItemdetails?.hideFlag ? "icon-browse-off" : "icon-browse"} />
                    {currentItemdetails?.hideFlag ? "隐藏" : "取消隐藏"}
                  </Button>
                </Authority>
              </div>
            </div>
            <div className="alarm-details-content-left-content">
              <div className="left">
                {currentItemdetails?.aisAlarmInfo?.length
                  ? currentItemdetails?.aisAlarmInfo.map((item: any, index: number) => {
                      return (
                        <img
                          onClick={() => setCurrentItemImg({ ...item, indexC: index })}
                          className={`left-item ${currentItemImg?.indexC === index ? "left-item-select" : ""}`}
                          src={item.storageImageUrl}
                        />
                      );
                    })
                  : null}
              </div>
              <div className="right">
                <img className="right-img" src={currentItemImg?.storageImageUrl} alt="" />
                {canCut ? (
                  <>
                    <div className="right-img-pre" onClick={onPre}>
                      <IconFont type="icon-gaojingzhongxin_shangyiye" />
                    </div>
                    <div className="right-img-next" onClick={onNext}>
                      <IconFont type="icon-gaojingzhongxin_shangyiye" />
                    </div>
                  </>
                ) : null}
              </div>
            </div>
          </div>
          <div className="alarm-details-content-right">
            <div className="alarm-details-content-right-header">
              <Button
                onClick={() => {
                  setIsModalOpen1(true);
                }}
                style={{ marginRight: 16 }}
              >
                事件视频
              </Button>
              {/* 跳转 */}
              <Button
                onClick={() => {
                  history.push("/aisApp/rtVideo", { cid: currentItem?.cid });
                }}
                type="primary"
                style={{ marginRight: 16 }}
              >
                实时视频
              </Button>
              <Button
                onClick={() => {
                  history.push("/aisApp/videoReplay", { cid: currentItem?.cid, time: currentItem?.firstAlarmTime });
                }}
                type="primary"
              >
                历史录像
              </Button>
            </div>
            <div className="alarm-details-content-right-content">
              <div className="alarm-details-content-right-content-title">基本信息</div>
              <div className="alarm-details-content-right-content-item">
                <div className="alarm-details-content-right-content-item-title">设备名称</div>
                <div className="alarm-details-content-right-content-item-data">{currentItemdetails?.deviceName || "-"}</div>
              </div>
              {/* <div className="alarm-details-content-right-content-item">
                <div className="alarm-details-content-right-content-item-title">设备地址</div>
                <div className="alarm-details-content-right-content-item-data">{currentItemdetails?.deviceAddress || "-"}</div>
              </div> */}
              <div className="alarm-details-content-right-content-item">
                <div className="alarm-details-content-right-content-item-title">告警次数</div>
                <div className="alarm-details-content-right-content-item-data">{currentItemdetails?.alarmCount || "-"}次</div>
              </div>
              <div className="alarm-details-content-right-content-item">
                <div className="alarm-details-content-right-content-item-title">持续时间</div>
                <div className="alarm-details-content-right-content-item-data">
                  {TransMins(dayjs(+currentItemdetails?.lastAlarmTime).diff(dayjs(+currentItemdetails?.firstAlarmTime), "seconds"))}
                </div>
              </div>
              <div className="alarm-details-content-right-content-item">
                <div className="alarm-details-content-right-content-item-title">首次告警时间</div>
                <div className="alarm-details-content-right-content-item-data">
                  {DateToFormat(currentItemdetails?.firstAlarmTime, "YYYY-MM-DD HH:mm:ss")}
                </div>
              </div>
              <div className="alarm-details-content-right-content-item">
                <div className="alarm-details-content-right-content-item-title">最后告警时间</div>
                <div className="alarm-details-content-right-content-item-data">
                  {DateToFormat(currentItemdetails?.lastAlarmTime, "YYYY-MM-DD HH:mm:ss")}
                </div>
              </div>

              {currentItemdetails?.processFlag === 1 ? (
                <div className="alarm-details-content-right-content-title" style={{ marginTop: 24 }}>
                  处理信息
                </div>
              ) : null}

              {currentItemdetails?.processFlag === 1 ? (
                <div>
                  <div className="alarm-details-content-right-content-details-item">
                    <div className="alarm-details-content-right-content-details-item-ic" style={{ marginRight: 48 }}>
                      <div>
                        <IconFont type="icon-user" />
                      </div>
                      <div className="alarm-details-content-right-content-details-item-ic-text">{currentItemdetails?.processUserName || "-"}</div>
                    </div>
                    {/* <div className="alarm-details-content-right-content-details-item-ic">
                      <div>
                        <IconFont type="icon-image" />
                      </div>
                      <div className="alarm-details-content-right-content-details-item-ic-text">
                        {currentItemdetails?.accurateFlag ? "准确" : "不准确"}
                      </div>
                    </div> */}
                  </div>
                  <div className="alarm-details-content-right-content-details-item">
                    <div className="alarm-details-content-right-content-details-item-ic" style={{ marginRight: 48 }}>
                      <div>
                        <IconFont type="icon-time" />
                      </div>
                      <div className="alarm-details-content-right-content-details-item-ic-text">
                        {DateToFormat(currentItemdetails?.updatedTime, "YYYY-MM-DD HH:mm:ss")}
                      </div>
                    </div>
                  </div>
                  <div className="alarm-details-content-right-content-details-item">
                    <div className="alarm-details-content-right-content-details-item-ic" style={{ marginRight: 48 }}>
                      <div>
                        <IconFont type="icon-view-module" />
                      </div>
                      <div className="alarm-details-content-right-content-details-item-ic-text">{currentItemdetails?.processDescribe || "-"}</div>
                    </div>
                  </div>
                </div>
              ) : (
                <div>
                  <Authority code="30000101">
                    <Form form={form} labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                      {/* <Form.Item name="accurateFlag">
                        <Radio.Group>
                          <Radio value={1}>准确</Radio>
                          <Radio value={0}>不准确</Radio>
                        </Radio.Group>
                      </Form.Item> */}
                      <Form.Item style={{ marginBottom: 20 }} name="processDescribe" rules={[{ max: 100, message: "字数超出范围" }]}>
                        <Input.TextArea
                          className="alarm-details-content-right-content-textArea"
                          showCount
                          placeholder="请输入描述的信息,最多输入300字"
                          maxLength={300}
                        />
                      </Form.Item>
                    </Form>
                    <div className="alarm-details-content-right-footer-bnts">
                      <Button
                        style={{ marginRight: 16 }}
                        onClick={() => {
                          history.back();
                          SocketEmitter.emit("closeTab", "/aisApp/alarmDetails");
                        }}
                      >
                        取消
                      </Button>
                      <Button type="primary" onClick={handle}>
                        确定
                      </Button>
                    </div>
                  </Authority>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <OperatingBounced
        isShow={modalshow}
        title="配置确认"
        icon={<img src={warnImg} />}
        onCancel={modalToggle}
        onOk={runhid}
        content="确认要隐藏报警吗？"
        info="报警数据隐藏后将无法在报警中心查看，且不可恢复。"
      ></OperatingBounced>
      <Modal
        title="事件视频"
        width={900}
        className="video-modal"
        open={isModalOpen1}
        destroyOnClose={true}
        onOk={() => {
          setIsModalOpen1(false);
        }}
        onCancel={() => {
          setIsModalOpen1(false);
        }}
      >
        {list?.filter((item: any) => item)?.length ? (
          list
            .filter((item: RecordItem) => item)
            .map((item: any, index: number) => {
              return item?.segments?.map((item1: any, index1: number) => {
                return <Player url={item1.url} isLive={false} type="flv" />;
              });
            })
        ) : (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "200px",
              width: "100%",
            }}
          >
            暂无录像
          </div>
        )}
      </Modal>
    </div>
  );
}
