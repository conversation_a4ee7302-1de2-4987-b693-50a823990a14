import { IconFont, useHistory } from "@cloud-app-dev/vidc";
import { useSafeState, useToggle, useRequest } from "ahooks";
import {
  queryMergeAlarmInfoList,
  queryAlgorithmList,
  getTimeDimensionStatistics,
  hideMergeAlarmInfoBatch,
  batchProcessMergeAlarmInfo,
} from "@src/service/ais_app";
import { Button, message, Space, Checkbox, Popover, Select, Modal } from "antd";
import { DBTools, SocketEmitter, useInfiniteScroll } from "@cloud-app-dev/vidc";
import handled from "@src/assets/image/handled.png";
import { useEffect, useMemo, useRef } from "react";
import { DateToFormat } from "@src/ais_app/utils";
import { divide, isEmpty, uniqBy } from "lodash-es";
import DynamicGridList from "../../../../components/DynamicGridList";
import AlarmRightListBox from "../AlarmRightListBox";
import AlarmRightItem from "../AlarmRightListBox/components/AlarmRightItem";
import { size, cloneDeep } from "lodash-es";
import Authority from "@src/components/Authority";
import Temporarily from "@src/ais_app/components/Temporarily"
import dayjs from "dayjs";
import "./index.less";
const PAGE_SIZE = 20;
export const db = new DBTools("alarmlist");
// const DynamicGridList = List.DynamicGridList;
export default function AlarmRight({ params }: { params: any }) {
  const ref = useRef<HTMLDivElement>(null);
  const { data: sflist } = useRequest(() => queryAlgorithmList({ limit: 1000, offset: 0 }));
  const [total, setTotal] = useSafeState(0);
  const [state, stateChange]: any = useSafeState({ value: 1, type: "day", label: "1天", timeDimension: "day" });
  const [selectedIds, setSelectedIds]: any = useSafeState([]);
  const [selectedIdsTime, setSelectedIdsTime]: any = useSafeState({});
  const [list, setList] = useSafeState([]);
  const [hideFlag, setHideFlag] = useSafeState(true);
  const [refsh, { toggle }] = useToggle(false);
  const [isSelect, { toggle: setIsSelect }] = useToggle(false);
  const [localrefsh, { toggle: lToggle, setLeft, setRight }] = useToggle(false);
  const loadPage = async (d: any): Promise<any> => {
    d.page ? d.page++ : (d.page = 1);
    const res = await queryMergeAlarmInfoList({
      offset: (d?.page - 1) * 30,
      limit: 30,
      ...params,
      timeStr: selectedIdsTime.timeStr,
      timeDimension: state.timeDimension || "hour",
      hideFlag: hideFlag ? null : 0,
      alarmEndTime: params.alarmEndTime || dayjs()?.valueOf(),
      algorithmTypes: [3],
      algorithmIds: ['detainInSsolation'],
      isPagination: true,
    });
    setTotal(res?.data?.totalCount);
    const resultData = res?.data?.list;

    d.total = res?.data?.totalCount;
    d.list ? (d.list = uniqBy([...d.list, ...resultData], "id")) : (d.list = resultData);
    setList(d.list ? (d.list = uniqBy([...d.list, ...resultData], "id")) : (d.list = resultData));
    db.put({ id: "alarminfo", value: d.list, total: res?.data?.totalCount }).then((res) => {
      SocketEmitter.emit("listChange");
    });
    if (localrefsh) {
      message.success("刷新成功");
      setLeft();
    }
    return d;
  };
  useEffect(() => {
    SocketEmitter.on("refrshList", toggle);
    SocketEmitter.on("alarmMessage", toggle);
    return () => SocketEmitter.on("refrshList", toggle);
  }, []);
  const hideFlagAllHandler = (hideFlag: any) => {
    Modal.confirm({
      title: `批量${!hideFlag ? "隐藏" : "取消隐藏"}`,
      content: `确定要批量${!hideFlag ? "隐藏" : "取消隐藏"}吗？`,
      okText: "确定",
      cancelText: "取消",
      onOk: async () => {
        hideMergeAlarmInfoBatch({ ids: selectedIds, hideFlag }).then((res) => {
          if (res.code === 0) {
            toggle();
            setSelectedIds([]);
            message.success("操作成功");
          }
        });
      },
    });
  };
  const getTimeDimensionStatisticsHander = (page: number, pageSize: number, oldlist: any): Promise<{ list: any; total: number }> => {
    const options = {
      offset: (page + 1) * pageSize,
      limit: pageSize,
      isPagination: true,
      sortOrder: "desc",
      timeDimension: state.timeDimension,
      // timeStr: dayjs().subtract(state.value, state.type),
      alarmEndTime: params.alarmEndTime || dayjs()?.valueOf(),
      hideFlag: hideFlag ? null : 1,
      alarmStartTime: params.alarmStartTime || dayjs().subtract(30, "day")?.valueOf(),
    };
    return getTimeDimensionStatistics(options).then(({ data }: any) => ({
      list: [...oldlist, ...(data?.list || [])],
      total: data?.totalCount || 0,
      totalPage: data?.totalPage || 0,
    }));
  };
  const { data = { list: [] } } = useInfiniteScroll(
    (d) => {
      // offset 0开始
      const page = d?.list.length ? Math.ceil(d.list.length / PAGE_SIZE) - 1 : -1;
      return getTimeDimensionStatisticsHander(page, PAGE_SIZE, d ? d?.list : []);
      // return getTimeDimensionStatistics(page, PAGE_SIZE, d ? d?.list : []);
    },
    {
      target: ref,
      reloadDeps: [state, params, hideFlag],
      isNoMore: (d) => (d ? size(d?.list) === +d?.total : false),
    },
  );
  const reloadDeps = useMemo(
    () => [params, refsh, localrefsh, selectedIdsTime, hideFlag, state],
    [params, refsh, localrefsh, selectedIdsTime, hideFlag, state],
  );
  const FetchProcessMergeAlarmInfo = () => {
    Modal.confirm({
      title: `批量处理`,
      content: `确定要批量处理告警吗？`,
      okText: "确定",
      cancelText: "取消",
      onOk: async () => {
        batchProcessMergeAlarmInfo({ idList: selectedIds }).then((res) => {
          if (res.code === 0) {
            toggle();
            setSelectedIds([]);
            message.success("操作成功");
          }
        }, (err) => {
          message.warning(err.data.message);
        });
      },
    });
  };
  return (
    <div className="AlarmRight">
      <div className="header-title">单独羁押</div>
      <div className="header">
        <div className="total">
          共&nbsp;<span>{total}</span>&nbsp;条告警
          <IconFont style={{ marginLeft: 8, cursor: "pointer" }} type="icon-refresh" onClick={setRight} />
        </div>
        {/* <div className="btn" onClick={setRight}>
          <Button>
            <IconFont type="icon-gaojingzhongxin_shuaxin" /> 刷新
          </Button>
        </div> */}
        <Space>
          {isSelect ? (
            <>
              <Checkbox
                indeterminate={selectedIds.length > 0 && selectedIds.length < list.length}
                checked={selectedIds.length === list.length && list?.length > 0}
                onClick={(e: any) => {
                  const isChecked = e.target.checked;
                  if (!isChecked) return setSelectedIds([]);
                  setSelectedIds(list.map((v: any) => v.id));
                }}
              >
                全选
              </Checkbox>
              {!hideFlag ? (
                <Button
                  onClick={() => hideFlagAllHandler(1)}
                  disabled={selectedIds.length == 0}
                  style={{ color: "var(--primary)", borderColor: "var(--primary)" }}
                >
                  取消隐藏
                </Button>
              ) : (
                <Button
                  onClick={() => hideFlagAllHandler(0)}
                  disabled={selectedIds.length == 0}
                  style={{ color: "var(--primary)", borderColor: "var(--primary)" }}
                >
                  全部隐藏
                </Button>
              )}
              <Button
                onClick={() => FetchProcessMergeAlarmInfo()}
                disabled={selectedIds.length == 0}
                style={{ color: "var(--primary)", borderColor: "var(--primary)" }}
              >
                全部忽略
              </Button>
              <Button
                onClick={() => {
                  setIsSelect();
                  setSelectedIds([]);
                }}
                style={{ color: "var(--primary)", borderColor: "var(--primary)" }}
              >
                取消
              </Button>
            </>
          ) : (
            <>
           
              <Authority code="30000102">
                <Button onClick={setIsSelect} style={{ background: "var(--primary)" }} type="primary">
                  批量处理
                </Button>
              </Authority>
              <Button onClick={() => setHideFlag(!hideFlag)}>
                <IconFont type={hideFlag ? "icon-browse-off" : "icon-browse"} />
              </Button>

              <Popover
                arrow={false}
                content={
                  <div ref={ref} className="AlarmRight_times_popover">
                    <Space style={{ marginBottom: 8 }}>
                      <div className="AlarmRight_times_popover_title">阶段划分:</div>
                      <Select
                        style={{ width: "140px" }}
                        value={state.value}
                        onChange={(e, it) => {
                          stateChange(it);
                          setSelectedIdsTime({});
                        }}
                        options={[
                          { value: 31, type: "day", label: "1月", timeDimension: "month" },
                          { value: 7, type: "day", label: "7天", timeDimension: "week" },
                          { value: 1, type: "day", label: "1天", timeDimension: "day" },
                          { value: 2, type: "hour", label: "1小时", timeDimension: "hour" },
                          // { value: 30, type: "minute", label: "30分", timeDimension: "halfHour" },
                        ]}
                      ></Select>
                    </Space>
                    <div className="AlarmRight_times_popover_list">
                      {data?.list?.length
                        ? data?.list?.map((item: any, index: number) => {
                          return (
                            <div
                              onClick={() => {
                                if (selectedIdsTime.timeStr === item.timeStr) return setSelectedIdsTime({});
                                setSelectedIdsTime(item);
                              }}
                              className={`AlarmRight_times_popover_item ${selectedIdsTime.timeStr === item.timeStr && "AlarmRight_times_popover_item_active"}`}
                            >
                              <span className="AlarmRight_times_popover_item_icon"></span>
                              {item.timeStr}（{item.alarmCount}）
                            </div>
                          );
                        })
                        : null}
                    </div>
                  </div>
                }
                placement="bottom"
                trigger="click"
              >
                <Button>
                  <IconFont type="icon-calendar" />
                </Button>
              </Popover>
            </>
          )}
        </Space>
      </div>
      <div className="content">
        {
          total === 0 ?
            <div style={{ height: '100%', width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Temporarily marginTop={0} />
            </div>
            :
           <></>
        }
        <div style={total === 0 ? { display: "none" } : {width:'100%',height:'100%'}}>
         <DynamicGridList
              itemKey="id"
              itemHeight={220}
              itemWidth={226}
              isNoMore={(data) => data?.list?.length === total}
              loadPage={loadPage}
              reloadDeps={reloadDeps}
              renderItem={(item: any, index: any) => {
                return (
                  // <div
                  //   className="item"
                  //   key={`${item.id} -${index}`}
                  //   onClick={() => {
                  //     history.push(`/aisApp/alarmDetails`, { id: item?.id });
                  //   }}
                  // >
                  //   <div className="item-img">
                  //     <img src={item.lastImageUrl} />
                  //   </div>
                  //   <span className="item-tag">{item?.algorithmName || "-"}</span>
                  //   <div className="item-adress" title={item?.deviceName || "-"}>
                  //     <IconFont type="icon-gaojingzhongxin_shexiangji" /> <div className="over">{item?.deviceName || "-"}</div>
                  //   </div>
                  //   <span className="item-name" title={DateToFormat(item.lastAlarmTime, "YYYY-MM-DD HH:mm:ss") || "-"}>
                  //     <IconFont type="icon-gaojingzhongxin_shijian" />
                  //     {DateToFormat(item.lastAlarmTime, "YYYY-MM-DD HH:mm:ss") || "-"}
                  //   </span>
                  //   {item?.processFlag === 1 && <img className="handle-img" src={handled} alt="" />}
                  // </div>
                  <div style={{ display: "flex", justifyContent: "flex-end", width: !item.timeStr ? "220px" : "217px" }}>
                    {item.timeStr ? (
                      <div className="alarm-right-item_children-left">
                        <div style={{ transform: "translateY(40px) rotate(-90deg)", width: "100%", whiteSpace: "nowrap" }}>{item.timeStr}</div>
                      </div>
                    ) : null}

                    <AlarmRightItem
                      selectedIds={selectedIds}
                      setSelectedIds={setSelectedIds}
                      isSelect={isSelect}
                      item={item}
                      DateToFormat={DateToFormat}
                    />
                  </div>
                );
              }}
            />
            </div>
      </div>
    </div>
  );
}
