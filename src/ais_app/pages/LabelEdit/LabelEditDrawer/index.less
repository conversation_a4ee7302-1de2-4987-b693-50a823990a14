.LabelEditDrawer-layout-form {
    .ant-form-item .ant-form-item-label>label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
        // display: none;
    }
    .ant-form-item .ant-form-item-label>label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::after {
        display: inline-block;
        margin-inline-end: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
    }
    .LabelEditDrawer-form-item-title {
        margin-right: 12px;
        text-align: left;
        color: rgba(0, 0, 0, 0.9);
        font-size: 16px;
        font-weight: 330;
        line-height: 24px;
        margin-bottom: 8px;
    }
    .LabelEditDrawer-form-item-title::before {
        display: inline-block;
        margin-inline-end: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "";
    }
    .ant-color-picker-trigger {
        .ant-color-picker-color-block {
            width: 100%;
        }
    }

    .LabelEditDrawer-form-item {
        margin-bottom: 28px;

        .ant-select .ant-select-arrow {
            top: 63%;
        }

        .ant-form-item-label {
            width: 80px;
            text-align: left;

            color: rgba(0, 0, 0, 0.90);
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
        }

        .ant-form-item-label>label {
            margin-right: 12px;
            text-align: left;
            color: rgba(0, 0, 0, 0.9);
            font-size: 16px;
            font-weight: 330;
            line-height: 24px;
        }

        .ant-form-item-label>label::after {
            display: none;
        }
    }

    .upload-item-img1 {
        border-radius: 4px;
        height: 174px;
        width: 309px;
        border: 0.75px dashed #DCDCDC;
        background: #EEE;
        justify-content: center;
        align-items: center;
        display: flex;
        cursor: pointer;

        img {
            margin: 0px auto;
        }

        .from-item-lable-upload1 {
            color: rgba(0, 0, 0, 0.40);
            font-size: 12px;
            font-weight: 400;
            line-height: 15px;
            margin-top: 8px;
            margin-bottom: 8px;
        }

        .from-item-lable-upload2 {
            color: rgba(0, 0, 0, 0.40);
            font-size: 12px;
            font-weight: 400;
            line-height: 15px;
            margin-top: 4px;
            margin-bottom: 4px;
        }
    }

    .upload-item-img2 {
        border-radius: 6px;
        height: 174px;
        width: 309px;
        // border: 0.75px dashed #DCDCDC;
        background: #F3F3F3;
        justify-content: center;
        align-items: center;
        display: flex;
        cursor: pointer;

        .item-Upload-img2 {
            height: 174px;
            width: 309px;
            border-radius: 6px;
        }

        .UploadList-box-item-model-box {
            display: none;
            position: absolute;
            top: -0px;
            left: 0px;
            height: 174px;
            width: 309px;
            border-radius: 6px;
            border: 0.75px dashed var(--Gray-Gray4-, #DCDCDC);
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0.60) 100%), #EEE;
            opacity: 0.9;

            color: #fff;

            .UploadList-box-item-model {
                display: flex;

                align-items: center;
                justify-content: center;

                .ant-upload-wrapper {
                    flex: 1;
                    display: flex;
                    justify-content: flex-end;

                    .ant-upload {
                        .item-model-left {
                            cursor: pointer;
                            height: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: flex-end;
                            padding-right: 20px;
                        }
                    }
                }

                .item-model-right {
                    cursor: pointer;
                    height: 12px;
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: right;
                }
            }
        }

    }

    .upload-item-img2:hover {
        .UploadList-box-item-model-box {
            display: block;
            text-align: center;
            padding-top: 66px;
        }
    }
}

.LabelEditDrawer-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .LabelEditDrawer-clear {
        cursor: pointer;
        margin-right: 16px;
        border-radius: 3px;
        background: #E5E6EB;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0px 16px;
        color: #1D2129;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
    }

    .LabelEditDrawer-ok {
        cursor: pointer;
        border-radius: 3px;
        background: #165DFF;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0px 16px;
        color: rgba(255, 255, 255, 0.90);
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
    }

    .footer-left-buttom {
        cursor: pointer;
        height: 32px;
        border-radius: 3px;
        border: 1px solid #D54941;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0px 16px;

        color: #D54941;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
    }

    .footer-left-buttom1 {
        cursor: no-drop;
        height: 32px;
        border-radius: 3px;
        border: 1px solid #FFB9B0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0px 16px;

        color: #FFB9B0;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
    }
}