import { useRef, useEffect } from "react";
import { Drawer, Input, Form, Button, ColorPicker, message } from "antd";
import DeviceAssignS from "@src/ais_app/pages/TestView/components/DeviceTestRight/components/DeviceAssign";

import "./index.less";

export default function LabelEditDrawer({ open, setOpen, onSub, dataInfo, setDataInfo, selectItems, setSelectItems }: any) {
    const [form] = Form.useForm()
    const subBottomRef: any = useRef(null)

    useEffect(() => {
        if (dataInfo.id) {
            form.setFieldsValue({ ...dataInfo, tagFontColor: `#${dataInfo.tagFontColor}`, tagBgColor: `#${dataInfo.tagBgColor}` })
        } else {
            form.setFieldsValue({ tagFontColor: '#165DFF', tagBgColor: '#BEDAFF' })
        }
    }, [dataInfo])
    const onCancel = () => {
        setDataInfo({})
        setSelectItems([])
        setOpen(false)
    }

    const handleSubmit = (value: any) => {
        // if (selectItems.length === 0) {
        //     message.info('请选择设备')
        //     return
        // }
        let a = typeof value.tagFontColor === 'string' ? value.tagFontColor : value.tagFontColor?.toHexString()
        let b = typeof value.tagBgColor === 'string' ? value.tagBgColor : value.tagBgColor?.toHexString()
        let c = selectItems?.map((item: any) => item.cid)
        let d = []
        if (dataInfo.id) {
            for (let i = 0; i < dataInfo.cids.length; i++) {
                if (!c.includes(dataInfo.cids[i])) {
                    d.push(dataInfo.cids[i])
                }
            }
        }
        onSub({ ...value, unCids: d, cids: c, tagFontColor: a.split("#")[1], tagBgColor: b.split("#")[1] })
    }

    return (
        <>
            <Drawer
                width={950}
                title={dataInfo.id ? "编辑标签" : "新增标签"}
                placement={'right'}
                closable={false}
                onClose={onCancel}
                open={open}
                keyboard={false}
                footer={<div className="LabelEditDrawer-footer">
                    <div onClick={() => onCancel()} className="LabelEditDrawer-clear">取消</div>
                    <div className="LabelEditDrawer-ok" onClick={() => subBottomRef.current.click()}>保存</div>
                </div>}
            >

                <Form
                    form={form}
                    name='loginForm'
                    onFinish={handleSubmit}
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                    className='LabelEditDrawer-layout-form'
                >
                    <Form.Item
                        className='LabelEditDrawer-form-item'
                        label="标签名称"
                        name="tagName"
                        rules={[
                            { required: true, message: '请输入标签名称' },
                        ]}
                    >
                        <Input maxLength={10} placeholder="请输入标签名称，限制10字" />
                    </Form.Item>

                    <Form.Item
                        className='LabelEditDrawer-form-item'
                        label="标签字体颜色"
                        name="tagFontColor"
                        rules={[
                            { required: true, message: '请选择标签字体颜色' },
                        ]}
                    >
                        <ColorPicker format="hex" style={{ width: '50%' }} />
                    </Form.Item>

                    <Form.Item
                        className='LabelEditDrawer-form-item'
                        label="标签背景颜色"
                        name="tagBgColor"
                        rules={[
                            { required: true, message: '请选择标签背景颜色' },
                        ]}
                    >
                        <ColorPicker format="hex" style={{ width: '50%' }} />
                    </Form.Item>

                    <div className='LabelEditDrawer-form-item'>
                        <div className="LabelEditDrawer-form-item-title">选择设备</div>
                        <DeviceAssignS
                            labelDis={true}
                            selectItems={selectItems}
                            onSelectChange={(v: any) => {
                                setSelectItems(v);
                            }}
                        />
                    </div>

                    <Form.Item style={{ display: 'none' }}>
                        <Button ref={subBottomRef} htmlType='submit'>
                            提交
                        </Button>
                    </Form.Item>
                </Form>

            </Drawer>
        </>
    );
}
