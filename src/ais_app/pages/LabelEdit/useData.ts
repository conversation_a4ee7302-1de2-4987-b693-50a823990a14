import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { useSafeState } from "ahooks";
import Service from "@src/service/system";

const useData = () => {
    const inputRef: any = useRef(null)

    const [labelEditDrawerOpen, setLabelEditDrawerOpen] = useState(false)
    const [dataInfo, setDataInfo]: any = useState({})
    const [selectItems, setSelectItems] = useSafeState<any[]>([]);
    const [data, setData] = useState([])
    const [totalCount, setTotalCount] = useState(0)
    const [params, setParams]: any = useState({
        isPagination: true,
        current: 1,
        offset: 0,
        limit: 10,
        keywords: '',
    })

    useEffect(() => {
        getList()
    }, [params])

    const getList = () => {
        Service.device.deviceTagList({ ...params }).then((res) => {
            setData(res.data.list)
            setTotalCount(res.data.totalCount)
        })
    }

    const onSubLabelEdit = (val: any) => {
        if (dataInfo.id) {
            Service.device.updateDeviceTag({ ...dataInfo, ...val }).then(() => {
                message.success('编辑标签成功', 1.5)
                setDataInfo({})
                setSelectItems([])
                setLabelEditDrawerOpen(false)
                getList()
            }).catch((err) => {
                message.warning(err.data.message)
            })
        } else {
            Service.device.saveDeviceTag({ parentId: params.orgId, ...val }).then(() => {
                message.success('新增标签成功', 1.5)
                setDataInfo({})
                setSelectItems([])
                setLabelEditDrawerOpen(false)
                getList()
            }).catch((err) => {
                message.warning(err.data.message)
            })
        }
    }

    return {
        data,
        params,
        totalCount,
        setParams,
        labelEditDrawerOpen,
        setLabelEditDrawerOpen,
        dataInfo,
        setDataInfo,
        onSubLabelEdit,
        inputRef,
        selectItems, 
        setSelectItems
    };
};

export default useData;
