import { useRef } from "react";
import { Table, Tree, Pagination, Drawer, Input, Form, Button } from 'antd';
import { useSize } from "ahooks";
import dayjs from "dayjs";
import LabelEditDrawer from './LabelEditDrawer'
import Authority from "@src/components/Authority";
import './index.less';

import useData from './useData';

export default function LabelEdit() {
    const ref = useRef<HTMLDivElement>(null);
    const size = useSize(ref);

    const {
        data,
        params,
        totalCount,
        setParams,
        labelEditDrawerOpen,
        setLabelEditDrawerOpen,
        dataInfo,
        setDataInfo,
        onSubLabelEdit,
        inputRef,
        selectItems,
        setSelectItems
    } = useData()

    const columns: any = [
        {
            title: '序号',
            dataIndex: 'dataIndex',
            fixed: 'left',
            width: 81,
            render: (text: string, i: any, t: any) => {
                let sizeC = params.current - 1
                return <div style={{ color: 'rgba(0, 0, 0, 0.90)', fontSize: 14 }}>{sizeC * params.limit + t + 1 < params.limit ? `${sizeC * params.limit + t + 1}` : sizeC * params.limit + t + 1}</div>
            },
        },
        {
            title: '标签名称',
            dataIndex: 'tagName',
            render: (text: string, i: any, t: any) => {
                return <div >{text}</div>
            },
        },
        {
            title: '在线设备数',
            dataIndex: 'onlineCount',
            render: (text: string, i: any, t: any) => {
                return <div >{text}</div>
            },
        },
        {
            title: '总设备数',
            dataIndex: 'totalCount',
            render: (text: string, i: any, t: any) => {
                return <div >{text}</div>
            },
        },
        {
            title: '标签样式',
            dataIndex: 'tagFontColor',
            render: (text: string, i: any, t: any) => {
                return <div style={{ display: 'flex' }}>
                    <div style={{ height: 24, display: 'flex', alignItems: 'center', justifyContent: 'center', color: `#${i.tagFontColor}`, backgroundColor: `#${i.tagBgColor}`, padding: '0px 8px', borderRadius: 3 }}>{i.tagName}</div>
                </div>
            },
        },
        {
            title: '更新时间',
            dataIndex: 'updatedTime',
            render: (text: string, i: any, t: any) => {
                return <div>{text ? dayjs(Number(text)).format("YYYY/MM/DD HH:mm:ss") : dayjs(Number(i.createdTime)).format("YYYY/MM/DD HH:mm:ss")}</div>
            },
        },
        {
            title: '操作',
            key: 'operation',
            fixed: 'right',
            width: 98,
            render: (res: any, i: any, t: any) => {
                return <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Authority code="30000462">
                        <a
                            style={{ fontSize: 14, color: '#165DFF' }}
                            onClick={() => {
                                setDataInfo(i)
                                setSelectItems(i.cids || [])
                                setLabelEditDrawerOpen(true)
                            }}
                        >
                            编辑
                        </a>
                    </Authority>
                </div >
            },
        },
    ];

    return (
        <div className="label-edit-page" ref={ref}>
            <div className='label-edit-page-center'>
                <div className='label-edit-page-title'>
                    <div className='label-edit-page-title-title'>
                        场所标签管理
                    </div>
                    <Input
                        allowClear
                        ref={inputRef}
                        value={params.keywords}
                        onChange={(e) => setParams({ ...params, keywords: e.target.value })}
                        style={{ width: 300 }}
                        placeholder={`请输入标签名称搜索`}
                    />
                    <Authority code="30000461">
                        <div className='label-edit-page-title-button' onClick={() => setLabelEditDrawerOpen(true)}>新增标签</div>
                    </Authority>
                </div>
                <Table
                    rowKey="id"
                    // @ts-ignore
                    columns={columns}
                    dataSource={data}
                    scroll={{ x: 'max-content', y: size?.height && size.height - 242 }}
                    pagination={false}
                />

                {
                    data.length > 0 &&
                    <div style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end' }}>
                        <Pagination
                            current={params.current}
                            total={totalCount}
                            pageSize={params.limit}
                            showSizeChanger
                            showTotal={() => `共 ${totalCount} 项数据`}
                            showQuickJumper={true}
                            onChange={(page, size) => {
                                if (size === params.limit) {
                                    setParams({
                                        ...params,
                                        current: page,
                                        limit: size,
                                        offset: (page - 1) * size,
                                    });
                                } else {
                                    setParams({
                                        ...params,
                                        current: 1,
                                        offset: 0,
                                        limit: size,
                                    });
                                }
                            }}
                        />
                    </div>
                }
            </div>

            {
                labelEditDrawerOpen &&
                <LabelEditDrawer
                    open={labelEditDrawerOpen}
                    setOpen={setLabelEditDrawerOpen}
                    dataInfo={dataInfo}
                    setDataInfo={setDataInfo}
                    dataList={data}
                    onSub={onSubLabelEdit}
                    selectItems={selectItems}
                    setSelectItems={setSelectItems}
                />
            }
        </div>
    );
}
