import PackUp from "@src/ais_app/components/PackUp";
import MyCheck from "@src/ais_app/components/MyCheck";
import { useCallback, useRef, useEffect } from "react";
import { Form, DatePicker, message, Popover, Divider } from "antd";
import { IconFont } from "@cloud-app-dev/vidc";
import dayjs from "dayjs";
import { queryAlgorithmList } from "@src/service/ais_app";
import { useForm } from "antd/lib/form/Form";
import { useSafeState, useRequest } from "ahooks";
import DevicePoints from "@src/ais_app/components/DevicePoints";
import Service from "@src/service/system";
import "./index.less";
const statusArr = [
  {
    value: "已处理",
    id: "1",
  },
  {
    value: "未处置",
    id: "0",
  },
];

export default function AlarmLeft({ setParams, paramsData }: { setParams: (v: any) => void; paramsData: any }) {
  const ref1: any = useRef(null);
  const ref2: any = useRef(null);
  const ref3: any = useRef(null);
  const [form] = useForm();
  const { data } = useRequest(queryAlgorithmList, { defaultParams: [{ limit: 1000, offset: 0 }] });
  const [timeType, setTimeType] = useSafeState(4);
  const [infoData, setInfoData] = useSafeState([]);
  //from表单变化触发请求列表
  const valuesChange = () => {
    const data = form.getFieldsValue(true);
    const params = {
      ...data,
      alarmStartTime: data?.alarmStartTime?.valueOf() || dayjs().subtract(30, "day")?.valueOf(),
      alarmEndTime: data?.alarmEndTime?.valueOf() || dayjs()?.valueOf(),
    };
    setParams((old: any) => ({ ...old, ...params }));
  };
  const onChange = (points: any[]) => {
    const cids = points?.map((v) => v.cid);
    setParams((old: any) => ({ ...old, cids, points }));
  };
  const timeFilter = [
    { type: 1, value: 1, label: "近一天" },
    { type: 2, value: 3, label: "近三天" },
    { type: 3, value: 7, label: "近七天" },
    { type: 4, value: 30, label: "近一月" },
  ];
  const onTimeClick = useCallback((item: any) => {
    const { type, value } = item;
    const dates = [dayjs().subtract(value, "day"), dayjs()];
    const [beginTime, endTime] = dates;
    form.setFieldsValue({ alarmStartTime: beginTime, alarmEndTime: endTime });
    const data = form.getFieldsValue(true);
    const params = {
      ...data,
      alarmStartTime: data?.alarmStartTime?.valueOf(),
      alarmEndTime: data?.alarmEndTime?.valueOf(),
    };
    setParams((old: any) => ({ ...old, ...params }));
    setTimeType(type);
  }, []);
  const deleteDevice = (item: any) => {
    const arr = paramsData?.points.filter((v: any) => v.cid !== item.cid);
    if (ref3.current) {
      ref3.current?.setStatePoint((old: any) => {
        return { ...old, points: arr };
      });
    }
    onChange(arr);
  };
  const getModelInfo = () => {
    Service.device.algorithmRuleInfo({ algorithmId: 'monitoringRoomLeftPost' }).then((res: any) => {
        const treeList = res.data.list?.filter((item: any) => item.configValue).map((item: any) => ({ ...item }));
        setInfoData(treeList)
    }).catch((err) => {
        message.warning(err.data.message)
    })
}
useEffect(()=>{
  getModelInfo()
},[])
  return (
    <div className="AlarmLeft">
      <div className="header">
        <div>检索条件</div>
        <Popover
          placement="bottom"
          title={null}
          content={
            <div className="header-right-details">
              <div className="header-right-details-item">
                <div className="header-right-details-item-header">
                  <div className="left">已选时间</div>
                  <div
                    onClick={() => {
                      setTimeType(4);
                      const params = {
                        alarmStartTime: undefined,
                        alarmEndTime: undefined,
                      };
                      setParams((old: any) => ({ ...old, ...params }));
                      form.setFieldsValue({ alarmStartTime: undefined, alarmEndTime: undefined });
                    }}
                    className="right"
                  >
                    重置
                  </div>
                </div>
                <div className="header-right-details-item-content">
                  {paramsData.alarmStartTime && paramsData.alarmEndTime && (
                    <div className="header-right-details-item-content-it">
                      {dayjs(paramsData.alarmStartTime).format("YYYY-MM-DD HH:mm:ss")} -{" "}
                      {dayjs(paramsData.alarmEndTime).format("YYYY-MM-DD HH:mm:ss")}
                      <IconFont
                        onClick={() => {
                          setTimeType(4);
                          const params = {
                            alarmStartTime: undefined,
                            alarmEndTime: undefined,
                          };
                          setParams((old: any) => ({ ...old, ...params }));
                          form.setFieldsValue({ alarmStartTime: undefined, alarmEndTime: undefined });
                        }}
                        type="icon-guanbi"
                        style={{ marginLeft: 8, cursor: "pointer" }}
                      ></IconFont>
                    </div>
                  )}
                </div>
              </div>
              <div className="header-right-details-item">
                <div className="header-right-details-item-header">
                  <div className="left">已选设备</div>
                  <div
                    onClick={() => {
                      onChange([]);
                    }}
                    className="right"
                  >
                    重置
                  </div>
                </div>
                <div className="header-right-details-item-content">
                  {paramsData?.points?.length
                    ? paramsData.points.map((item: any, index: number) => {
                        return (
                          <div key={index} className="header-right-details-item-content-it">
                            {item.name}
                            <IconFont onClick={() => deleteDevice(item)} type="icon-guanbi" style={{ marginLeft: 8, cursor: "pointer" }}></IconFont>
                          </div>
                        );
                      })
                    : null}
                </div>
              </div>
              <div className="header-right-details-item">
                <div className="header-right-details-item-header">
                  <div className="left">已选分析状态</div>
                  <div
                    onClick={() => {
                      setParams((old: any) => ({ ...old, processFlags: [] }));
                      if (ref2.current) {
                        ref2.current?.setCheckedList([]);
                      }
                    }}
                    className="right"
                  >
                    重置
                  </div>
                </div>
                <div className="header-right-details-item-content">
                  {statusArr
                    .filter((item) => paramsData?.processFlags?.includes(item.id))
                    .map((item, index) => {
                      return (
                        <div key={index} className="header-right-details-item-content-it">
                          {item.value}
                          <IconFont
                            onClick={() => {
                              const arr = paramsData?.processFlags.filter((v: any) => v !== item.id);
                              setParams((old: any) => ({ ...old, processFlags: arr }));
                              ref2.current?.setCheckedList(arr);
                            }}
                            type="icon-guanbi"
                            style={{ marginLeft: 8, cursor: "pointer" }}
                          ></IconFont>
                        </div>
                      );
                    })}
                </div>
              </div>
            </div>
          }
          arrow={false}
        >
          <div className="header-right">查看</div>
        </Popover>
      </div>
      <div className="content">
        <Form form={form} onValuesChange={valuesChange}>
          {/* <Form.Item name="keyWords">
            <Input placeholder="请输入设备名称/算法名称搜索" className="vidc-Input" allowClear={false} suffix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" />} />
          </Form.Item> */}
          <PackUp
            title="时间"
            clearD={
              <div
                className="choseBtn"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setTimeType(4);
                  const params = {
                    alarmStartTime: undefined,
                    alarmEndTime: undefined,
                  };
                  setParams((old: any) => ({ ...old, ...params }));
                  form.setFieldsValue({ alarmStartTime: undefined, alarmEndTime: undefined });
                }}
              >
                重置
              </div>
            }
          >
            <div className="time">
              {timeFilter.map((item, index) => (
                <span key={index} onClick={() => onTimeClick(item)} className={`time-btn ${timeType === item.type ? " time-btn-active" : ""}`}>
                  {item.label}
                </span>
              ))}
            </div>
            <div className="data">
              <Form.Item name="alarmStartTime">
                <DatePicker
                  onChange={() => {
                    setTimeType(0);
                  }}
                  format="YYYY-MM-DD HH:mm:ss"
                  style={{ width: "100%", borderRadius: 3 }}
                  placeholder="开始时间"
                  showTime={{ defaultValue: dayjs("00:00:00", "HH:mm:ss") }}
                />
              </Form.Item>
              <Form.Item name="alarmEndTime">
                <DatePicker
                  onChange={() => {
                    setTimeType(0);
                  }}
                  style={{ width: "100%", borderRadius: 3 }}
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder="结束时间"
                  showTime={{ defaultValue: dayjs("00:00:00", "HH:mm:ss") }}
                />
              </Form.Item>
            </div>
          </PackUp>
          <DevicePoints
            ref={ref3}
            packUpIsTrue={true}
            onChange={(v) => {
              onChange(v);
            }}
          />
          <PackUp
            clearD={
              <div
                // className="check-all"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (ref2.current) {
                    ref2.current.onCheckClear();
                  }
                }}
              >
                <span className="choseBtn">重置</span>
              </div>
            }
            title="处理结果"
          >
            <Form.Item name="processFlags">
              <MyCheck ref={ref2} itemcheckgroup={statusArr} />
            </Form.Item>
          </PackUp>
        </Form>
        <Divider />
        <div  style={{marginBottom:8,fontSize:16,fontWeight:500}}>算法规则</div>
        {
          infoData?.map((item:any)=>{
            return <div style={{marginBottom:8,color:'rgba(0, 0, 0, 0.60)'}}>{item.configName}{item.configValue}</div>
          })
        }
      </div>
    </div>
  );
}
