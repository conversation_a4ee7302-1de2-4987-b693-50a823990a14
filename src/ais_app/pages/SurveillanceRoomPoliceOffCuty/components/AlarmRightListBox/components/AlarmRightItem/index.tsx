import { useMemo, useState, useEffect } from "react";
import { Tag, Card, Row, Col, Input, Checkbox, AutoComplete, Modal, message } from "antd";
import { IconFont, useHistory } from "@cloud-app-dev/vidc";
import { useToggle } from "ahooks";
import { SocketEmitter } from "@cloud-app-dev/vidc";
import warnImg from "@src/assets/image/warn.png";
import OperatingBounced from "@src/components/OperatingBounced";
import { hideMergeAlarmInfo, processMergeAlarmInfo } from "@src/service/ais_app";
import Authority from "@src/components/Authority";
import { TransMins } from "@src/ais_app/utils";
import dayjs from "dayjs";
import "./index.less";

const processFlags: any = {
  0: "未处理",
  1: "已处理",
};
export default function AlarmRightItem({ item, DateToFormat, isSelect, setSelectedIds, selectedIds }: any) {
  const history = useHistory();
  const [isBatch, SetIsBatch] = useState(false);
  const [isChecked, SetIsChecked] = useState(false);
  const [modalshow, { toggle: modalToggle }] = useToggle(false);
  const [modalshow1, { toggle: modalToggle1 }] = useToggle(false);
  const selectStatusClassname = isChecked ? "evidence-card-item--selected" : ""; // 选中'evidence-card-item--selected'
  useEffect(() => {
    // SetIsChecked(isSelect)
    SetIsBatch(isSelect);
    if (!isSelect) SetIsChecked(false);
  }, [isSelect]);
  useEffect(() => {
    selectedIds.includes(item.id) ? SetIsChecked(true) : SetIsChecked(false);
  }, [selectedIds]);
  const runhid = () => {
    hideMergeAlarmInfo(item?.id, item.hideFlag ? 0 : 1).then((res) => {
      if (res.code === 0) {
        message.success("操作成功");
        modalToggle();
        SocketEmitter.emit("refrshList");
      } else {
        message.warning(res.message);
      }
    });
  };
  const runhid1 = () => {
    processMergeAlarmInfo({ id: item?.id }).then((res) => {
      if (res.code === 0) {
        message.success("操作成功");
        modalToggle1();
        SocketEmitter.emit("refrshList");
      } else {
        message.warning(res.message);
      }
    });
  };
  return (
    // <div className="alarm-right-item">
    <Card
      onClick={() => {
        if (!isBatch) return;
        SetIsChecked(!isChecked);
        setSelectedIds((old: any) => {
          return old?.includes(item.id) ? old.filter((it: any) => it !== item.id) : [...old, item.id];
        });
      }}
      className={`evidence-card-dir-item ${isBatch ? "evidence-card-item--batch" : ""} ${selectStatusClassname}`}
      cover={null}
    >
      <Card.Meta
        avatar={null}
        title={null}
        description={
          <>
            <Row align="middle" wrap={false} style={{ display: "flex", alignItems: "center", width: "100%", borderRadius: 2, overflow: "hidden" }}>
              <img alt="" src={item.lastImageUrl} className="evidence-card-item__cover" />
            </Row>

            <Row align="middle" wrap={false} justify="space-between">
              <div className="evidence-card-item__filename">{item?.deviceName || "-"}</div>
              <div
                style={{
                  position: "absolute",
                  top: "8px",
                  left: "8px",
                  width: "calc(100% - 8px)",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                {item.lastAlarmTime - item.firstAlarmTime != 0 ? (
                  <span
                    title={`持续时长${TransMins(dayjs(+item?.lastAlarmTime).diff(dayjs(+item?.firstAlarmTime), "seconds"))}`}
                    className="ellipsis"
                    style={{
                      height: "24px",
                      color: "#F6685D",
                      background: "#FFD9C2",
                      borderRadius: 3,
                      fontSize: 12,
                      display: "inline-flex",
                      alignItems: "center",
                      justifyContent: "center",
                      padding: 3,
                    }}
                  >
                    持续时长{TransMins(dayjs(+item?.lastAlarmTime).diff(dayjs(+item?.firstAlarmTime), "seconds"))}
                  </span>
                ) : (
                  <div></div>
                )}

                <div style={{ textAlign: "right" }}>
                  {[...(item?.tagInfoList || [])]?.map((it: any) => (
                    <div>
                      <Tag
                        style={{
                          color: "#" + it?.tagFontColor,
                          background: "#" + it?.tagBgColor,
                          borderColor: "#" + it?.tagFontColor,
                        }}
                      >
                        {it.tagName}
                      </Tag>
                    </div>
                  ))}
                </div>
              </div>
            </Row>
            <Row align="middle" wrap={false}>
              <span className="evidence-card-item__uploader">告警类型：{item?.algorithmName || "-"}</span>
            </Row>
            <Row
              style={{
                fontSize: 12,
              }}
              justify="space-between"
              align="middle"
            >
              <Col style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }}>
                <span className="evidence-card-dir-item-file-num" style={{ color: item.processFlag ? "#2BA471" : "#FA9550" }}>
                  {processFlags[item.processFlag]}
                </span>
                <span className="evidence-card-dir-item-file-num">{DateToFormat(item.lastAlarmTime, "YYYY-MM-DD HH:mm:ss") || "-"}</span>
              </Col>

              <Checkbox checked={isChecked} className="evidence-card-item__checkbox" />
              {/* {item.processFlag == 1 ? <div className="processFlags">已处理</div> : null} */}
              {!item.hideFlag ? (
                <div className="evidence-card-dir-item-file-hideFlag">
                  <IconFont type="icon-browse-off" />
                </div>
              ) : null}
            </Row>
          </>
        }
      />

      <Row className="evidence-card-item__actions" gutter={[0, 16]}>
        <Authority code="30000103">
          <Col
            className="evidence-card-item__action-item"
            span={12}
            onClick={() => {
              history.push(`/aisApp/surveillanceRoomPoliceOffCutyDetails`, { id: item?.id });
            }}
          >
            <IconFont type="icon-fangwen" className="evidence-card-item__action-icon" />
            <span className="evidence-card-item__action-label">详情</span>
          </Col>
        </Authority>
      
            <Authority code="30000102">
              <Col onClick={modalToggle} className="evidence-card-item__action-item" span={12}>
                <IconFont
                  type={item.hideFlag ? "icon-browse-off" : "icon-browse"}
                  style={{ fontSize: 15 }}
                  className="evidence-card-item__action-icon"
                />
                <span className="evidence-card-item__action-label">{item.hideFlag ? "隐藏" : "取消隐藏"}</span>
              </Col>
            </Authority>
            {item.processFlag == 1 ? null : (
              <Authority code="30000102">
                <Col onClick={modalToggle1} className="evidence-card-item__action-item" span={12}>
                  <IconFont type={"icon-browse-off"} style={{ fontSize: 15 }} className="evidence-card-item__action-icon" />
                  <span className="evidence-card-item__action-label">忽略</span>
                </Col>
              </Authority>
            )}
         
      </Row>
      <OperatingBounced
        isShow={modalshow}
        title="配置确认"
        icon={<img src={warnImg} />}
        onCancel={modalToggle}
        onOk={runhid}
        content={item.hideFlag ? "是否隐藏报警？" : "是否取消隐藏报警？"}
        // info="报警数据隐藏后将无法在报警中心查看，且不可恢复。"
      ></OperatingBounced>
      <OperatingBounced
        isShow={modalshow1}
        title="配置确认"
        icon={<img src={warnImg} />}
        onCancel={modalToggle1}
        onOk={runhid1}
        content={"是否确定处理该告警？"}
      ></OperatingBounced>
    </Card>
    // </div>
  );
}
