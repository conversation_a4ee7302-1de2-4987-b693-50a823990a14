import { useMemo, useState, useEffect } from "react";
import { List, Card, Row, Col, Input, Checkbox, AutoComplete, Modal, message } from "antd";
import AlarmRightItem from "../AlarmRightItem";
import dir_file from "../../../../../../../assets/image/background.png";
import "./index.less";

export default function AlarmRightDir({ data, index }: any) {
  console.log(data,index);
  const [isChecked, SetIsChecked] = useState(false);
  const [open, isOpen] = useState(true);
  return (
    <>
      {open ? (
        <div className="alarm-right-dir-item" onClick={() => isOpen(!open)}>
          <img className="alarm-right-dir-item_img" src={dir_file} alt="" />
          <div className="alarm-right-dir-item_time">2024-04-01 13:37:13</div>
          <div className="alarm-right-dir-item_num">点击查看（6）</div>
          <Checkbox checked={isChecked} className="alarm-right-dir-item_checkbox" />
        </div>
      ) : (
        <>
          <div  className="alarm-right-item_children-left" >
            <div onClick={() => isOpen(!open)} style={{ transform: "translateY(50px) rotate(-90deg)", width: "100%", whiteSpace: "nowrap" }}>2024-04-01 13:37:13</div>
          </div>
          {data.children
            ? data.children.map((item: any, index: number) => {
                return <AlarmRightItem key={index} data={item} />;
              })
            : null}
        </>
      )}
    </>
  );
}
