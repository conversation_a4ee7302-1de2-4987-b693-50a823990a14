import { useRef } from "react";
import { Table, Tree, Pagination, Drawer, Input, Form, Modal } from 'antd';
import { useSize } from "ahooks";
import dayjs from "dayjs";
import GamePointDrawer from './GamePointDrawer'
import Authority from "@src/components/Authority";
import './index.less';

import useData from './useData';

export default function GamePointEdit() {
    const ref = useRef<HTMLDivElement>(null);
    const size = useSize(ref);

    const {
        treeData,
        selectedKeys,
        onSelect,
        data,
        params,
        totalCount,
        setParams,
        gamePointDrawerOpen,
        setGamePointDrawerOpen,
        dataInfo,
        setDataInfo,
        onSubGamePoint,
        inputRef,
        onDelect,
        contextHolder,
    } = useData()

    const columns: any = [
        {
            title: '序号',
            dataIndex: 'dataIndex',
            fixed: 'left',
            width: 81,
            render: (text: string, i: any, t: any) => {
                let sizeC = params.current - 1
                return <div style={{ color: 'rgba(0, 0, 0, 0.90)', fontSize: 14 }}>{sizeC * params.limit + t + 1 < params.limit ? `${sizeC * params.limit + t + 1}` : sizeC * params.limit + t + 1}</div>
            },
        },
        {
            title: '局点名称',
            dataIndex: 'orgName',
            render: (text: string, i: any, t: any) => {
                return <div >{text}</div>
            },
        },
        {
            title: '子平台',
            dataIndex: 'mappingName',
            render: (text: string, i: any, t: any) => {
                return <div >{text || '-'}</div>
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createdTime',
            render: (text: string, i: any, t: any) => {
                return <div>{text ? dayjs(Number(text)).format("YYYY/MM/DD HH:mm:ss") : '-'}</div>
            },
        },
        {
            title: '更新时间',
            dataIndex: 'updatedTime',
            render: (text: string, i: any, t: any) => {
                return <div>{text ? dayjs(Number(text)).format("YYYY/MM/DD HH:mm:ss") : dayjs(Number(i.createdTime)).format("YYYY/MM/DD HH:mm:ss")}</div>
            },
        },
        {
            title: '操作',
            key: 'operation',
            fixed: 'right',
            width: 120,
            render: (res: any, i: any, t: any) => {
                return <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Authority code="30000442">
                        <a
                            style={{ fontSize: 14, color: '#165DFF' }}
                            onClick={() => {
                                setDataInfo(i)
                                setGamePointDrawerOpen(true)
                            }}
                        >
                            编辑
                        </a>
                    </Authority>
                    <Authority code="30000443">
                        {
                            !i.mappingId &&
                            <a
                                style={{ fontSize: 14, color: '#165DFF', marginLeft: 16 }}
                                onClick={() => {
                                    onDelect(i)
                                }}
                            >
                                删除
                            </a>
                        }
                    </Authority>
                </div >
            },
        },
    ];

    return (
        <div className="game-point-edit" ref={ref}>
            <div className='game-point-left'>
                <div className='game-point-left-title'>
                    局点管理
                </div>
                <div className='game-point-left-center'>
                    <Tree
                        selectedKeys={selectedKeys}
                        onSelect={onSelect}
                        treeData={treeData}
                        fieldNames={{ title: 'orgName', key: 'id' }}
                    />

                </div>
            </div>
            <div className='game-point-right'>
                <div className='game-point-right-title'>
                    <Input
                        allowClear
                        ref={inputRef}
                        value={params.keywords}
                        onChange={(e) => setParams({ ...params, keywords: e.target.value })}
                        style={{ width: 300 }}
                        placeholder={`请输入局点名称搜索`}
                    />
                    <Authority code="30000441">
                        <div className='game-point-right-button' onClick={() => setGamePointDrawerOpen(true)}>新增局点</div>
                    </Authority>
                </div>
                <Table
                    rowKey="id"
                    // @ts-ignore
                    columns={columns}
                    dataSource={data}
                    scroll={{ x: 'max-content', y: size?.height && size.height - 242 }}
                    pagination={false}
                />

                {
                    data.length > 0 &&
                    <div style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end' }}>
                        <Pagination
                            current={params.current}
                            total={totalCount}
                            pageSize={params.limit}
                            showSizeChanger
                            showTotal={() => `共 ${totalCount} 项数据`}
                            showQuickJumper={true}
                            onChange={(page, size) => {
                                if (size === params.limit) {
                                    setParams({
                                        ...params,
                                        current: page,
                                        limit: size,
                                        offset: (page - 1) * size,
                                    });
                                } else {
                                    setParams({
                                        ...params,
                                        current: 1,
                                        offset: 0,
                                        limit: size,
                                    });
                                }
                            }}
                        />
                    </div>
                }
            </div>

            {
                gamePointDrawerOpen &&
                <GamePointDrawer
                    open={gamePointDrawerOpen}
                    setOpen={setGamePointDrawerOpen}
                    dataInfo={dataInfo}
                    setDataInfo={setDataInfo}
                    dataList={data}
                    onSub={onSubGamePoint}
                />
            }
            {contextHolder}
        </div>
    );
}
