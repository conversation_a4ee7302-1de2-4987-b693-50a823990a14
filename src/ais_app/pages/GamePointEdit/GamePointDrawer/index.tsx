import { useRef, useEffect, useState } from "react";
import { Drawer, Input, Form, Button, Select, message } from "antd";
import Service from "@src/service/system";

import "./index.less";

export default function EditGantryCraneDrawer({ open, setOpen, onSub, dataInfo, setDataInfo }: any) {
    const [form] = Form.useForm()
    const subBottomRef: any = useRef(null)
    const [dataList, setDataList]: any = useState([])

    useEffect(() => {
        if (dataInfo.id) {
            form.setFieldsValue({ ...dataInfo })
        }

        Service.device.serviceRegistryList({ serviceType: 2 }).then((res: any) => {
            let a = res.data.list.map((item: any) => {
                return { ...item, value: item.serviceId, label: item.serviceName, disabled: item.orgId ? true : false, }
            })
            setDataList(a)
        }).catch((err) => {
            message.warning(err.data.message)
        })
    }, [dataInfo])

    const onCancel = () => {
        setDataInfo({})
        setOpen(false)
    }

    const handleSubmit = (value: any) => {
        onSub(value)
    }

    return (
        <>
            <Drawer
                width={461}
                title={dataInfo.id ? "编辑局点" : "新增局点"}
                placement={'right'}
                closable={false}
                onClose={onCancel}
                open={open}
                keyboard={false}
                footer={<div className="editGantryCraneDrawer-footer">
                    <div onClick={() => onCancel()} className="editGantryCraneDrawer-clear">取消</div>
                    <div className="editGantryCraneDrawer-ok" onClick={() => subBottomRef.current.click()}>保存</div>
                </div>}
            >

                <Form
                    form={form}
                    name='loginForm'
                    onFinish={handleSubmit}
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                    className='editGantryCraneDrawer-layout-form'
                >
                    {/* <Form.Item
                        className='editGantryCraneDrawer-form-item'
                        label="局点编号"
                        name="orgCode"
                        rules={[
                            { required: true, message: '请输入局点编号' },
                        ]}
                    >
                        <Input maxLength={10} placeholder="请输入局点编号，限制10字" />
                    </Form.Item> */}

                    <Form.Item
                        className='editGantryCraneDrawer-form-item'
                        label="局点名称"
                        name="orgName"
                        rules={[
                            { required: true, message: '请输入局点名称' },
                        ]}
                    >
                        <Input maxLength={30} placeholder="请输入局点名称，限制30字" />
                    </Form.Item>


                    <Form.Item
                        className='editGantryCraneDrawer-form-item'
                        label="子平台"
                        name="mappingId"
                    >
                        <Select
                            options={dataList}
                            placeholder="请选择子平台"
                        />
                    </Form.Item>
                    <Form.Item style={{ display: 'none' }}>
                        <Button ref={subBottomRef} htmlType='submit'>
                            提交
                        </Button>
                    </Form.Item>
                </Form>

            </Drawer>
        </>
    );
}
