import { useEffect, useRef, useState } from 'react';
import { message, Modal } from 'antd';
import { treeHelper } from '@cloud-app-dev/vidc';
import Service from "@src/service/system";

const useData = () => {
    const [modal, contextHolder] = Modal.useModal()
    const inputRef: any = useRef(null)
    const [selectedKeys, setSelectedKeys]: any = useState([])
    const [treeData, setTreeData]: any = useState([])
    const [gamePointDrawerOpen, setGamePointDrawerOpen] = useState(false)
    const [dataInfo, setDataInfo]: any = useState({})
    const [data, setData] = useState([])
    const [totalCount, setTotalCount] = useState(0)
    const [params, setParams]: any = useState({
        isPagination: true,
        current: 1,
        offset: 0,
        limit: 10,
        keywords: '',
        orgId: '',
    })

    useEffect(() => {
        getOrgTree(0)
    }, [])

    useEffect(() => {
        if (params.orgId) {
            getList()
        }
    }, [params])

    const getOrgTree = (type: any) => {
        Service.device.orgListTree({}).then((res) => {
            const treeList = res.data.list.map((item: any) => ({ ...item, name: item.organizationName, title: item.organizationName, value: item.id }));
            const treeData = treeHelper.computTreeList(treeList);
            setTreeData(treeData)
            if (!type && treeData.length > 0) {
                setSelectedKeys([treeData[0].id])
                setParams({ ...params, current: 1, offset: 0, orgId: treeData[0].id })
            }
        })
    }

    const getList = () => {
        Service.device.orgListTree({ ...params }).then((res) => {
            setData(res.data.list)
            setTotalCount(res.data.totalCount)
        })
    }

    const onSelect = (selectedKeys: any, info: any) => {
        if (selectedKeys.length > 0) {
            setSelectedKeys(selectedKeys)
            setParams({ ...params, current: 1, offset: 0, orgId: selectedKeys[0] })
        }
    };

    const onSubGamePoint = (val: any) => {
        if (dataInfo.id) {
            Service.device.userOrgUpdate({ ...dataInfo, ...val }).then(() => {
                message.success('编辑局点成功', 1.5)
                setDataInfo({})
                setGamePointDrawerOpen(false)
                getList()
                getOrgTree(1)
            }).catch((err) => {
                message.warning(err.data.message)
            })
        } else {
            Service.device.userOrgSave({ parentId: params.orgId, ...val }).then(() => {
                message.success('新增局点成功', 1.5)
                setDataInfo({})
                setGamePointDrawerOpen(false)
                getList()
                getOrgTree(1)
            }).catch((err) => {
                message.warning(err.data.message)
            })
        }
    }

    const onDelect = (item: any) => {
        modal.confirm({
            title: '删除前确认',
            content: `确定要删除吗？`,
            okText: '确认删除',
            cancelText: '取消',
            onOk: () => {
                Service.device.userOrgDelete({ id: item.id }).then((res: any) => {
                    message.success('删除成功', 1.5)
                    getList()
                    getOrgTree(1)
                }).catch((res) => {
                    message.warning(res.data.message)
                })
            }
        });
    }

    return {
        treeData,
        selectedKeys,
        onSelect,
        data,
        params,
        totalCount,
        setParams,
        gamePointDrawerOpen,
        setGamePointDrawerOpen,
        dataInfo,
        setDataInfo,
        onSubGamePoint,
        inputRef,
        onDelect,
        contextHolder,
    };
};

export default useData;
