import { Button, Form, Radio, Input, message, Tag } from "antd";
import { SocketEmitter } from "@cloud-app-dev/vidc";
import { IconFont, useHistory, Picture } from "@cloud-app-dev/vidc";
import { identityLibraryInfo, hideMergeAlarmInfo, processMergeAlarmInfo, queryAlgorithmList } from "@src/service/ais_app";
import warnImg from "@src/assets/image/warn.png";
import OperatingBounced from "@src/components/OperatingBounced";
import { useForm } from "antd/es/form/Form";
import { useRequest, useSafeState, useSize, useToggle, useUpdateEffect } from "ahooks";
import { db } from "../components/AlarmRight";
import { useEffect, useRef, useMemo } from "react";
import { DateToFormat, getAlgorithmNameById, TransMins } from "@src/ais_app/utils";
import Authority from "@src/components/Authority";
import { size } from "lodash-es";
import { useLocation } from "react-router-dom";
import RoiImageTag from "@src/ais_app/components/RoiImageTag";
import { renameDuplicates } from "@src/utils";
import "./index.less";
const modelStyle = {
  detainee: {
    color: "#d4380d",
    background: "#fff2e8",
  },
  doctor: {
    color: "#0958d9",
    background: "#e6f4ff",
  },
  police: {
    color: "#389e0d",
    background: "#f6ffed",
  },
  others: {
    color: "#531dab",
    background: "#f9f0ff",
  },
};
export default function AlarmDetails() {
  const [form] = useForm();
  const history = useHistory();
  const ref = useRef<HTMLDivElement>(null);
  const imgsize = useSize(ref);
  const [currentItem, setCurrentItem] = useSafeState<any>({});
  const [currentItemImg, setCurrentItemImg] = useSafeState<any>({});
  const [currentRects, setCurrentRects] = useSafeState<any>([]);
  const [currentItemdetails, setCurrentItemdetails] = useSafeState<any>({});
  const { data: sflist } = useRequest(() => queryAlgorithmList({ limit: 1000, offset: 0 }));
  const [modalshow, { toggle: modalToggle }] = useToggle(false);
  const [alarmInfo, setAlarmInfo] = useSafeState<{ list: any[]; total: number }>({ list: [], total: 0 });
  const [canCut, { setLeft, setRight }] = useToggle(true);
  const runhid = () => {
    hideMergeAlarmInfo(currentItemdetails?.id, currentItemdetails?.hideFlag ? 0 : 1).then((res) => {
      if (res.code === 0) {
        message.success("操作成功");
        modalToggle();
        SocketEmitter.emit("refrshList");
      } else {
        message.warning(res.message);
      }
    });
  };
  const handle = () => {
    form.validateFields().then((res) => {
      const params = {
        id: currentItem?.id,
        ...res,
      };
      processMergeAlarmInfo(params).then((res) => {
        if (res?.code === 0) {
          message.success("处置成功");
          SocketEmitter.emit("refrshList");
        } else {
          message.warning(res.message);
        }
      });
    });
  };
  const GetList = () => {
    db.get({ id: "alarminfo" })?.then((res: any) => {
      setAlarmInfo({ list: res?.value, total: res?.total });
    });
  };
  useEffect(() => {
    GetList();
    SocketEmitter.on("listChange", GetList);
    SocketEmitter.emit("clearMessage");
    return () => SocketEmitter.on("listChange", GetList);
  }, []);
  const { state } = useLocation();
  useUpdateEffect(() => {
    if (!state?.id) return;
    identityLibraryInfo(state?.id).then((res) => {
      if (res.code === 0) {
        setCurrentItem(res?.data);
        setCurrentItemdetails(res?.data);
        setCurrentItemImg(res?.data?.aisAlarmInfo[0] ? { ...res?.data?.aisAlarmInfo[0], indexC: 0 } : {});
      } else {
        identityLibraryInfo(alarmInfo?.list[0]?.id).then((res) => {
          setCurrentItem(res?.data);
          setCurrentItemdetails(res?.data);
          setCurrentItemImg(res?.data?.aisAlarmInfo[0] ? { ...res?.data?.aisAlarmInfo[0], indexC: 0 } : {});
        });
      }
    });
    // setCurrentItem(alarmInfo?.list?.find((v) => v?.id === id) || alarmInfo?.list[0])
  }, [state, alarmInfo]);
  useEffect(() => {
    history.location?.search === "?formsocket" ? setRight() : setLeft();
  }, [history.location]);
  useEffect(() => {
    form.resetFields();
  }, [currentItemdetails]);
  const onPre = () => {
    // const Index = alarmInfo?.list?.findIndex((v) => v?.id == currentItem?.id);
    const Index = currentItemdetails?.aisAlarmInfo?.findIndex((v: any) => v?.storageImageUrl == currentItemImg?.storageImageUrl);
    if (Index !== -1 && Index > 0) {
      let currentItem = currentItemdetails?.aisAlarmInfo[Index - 1];
      currentItem.indexC = Index - 1;
      setCurrentItemImg(currentItem);
      // const currentItem = alarmInfo?.list[Index - 1];
      // queryMergeAlarmInfo(currentItem?.id).then((res) => {
      //   setCurrentItem(res?.data);
      //   setCurrentItemdetails(res?.data);
      //   setCurrentItemImg(res?.data?.aisAlarmInfo[0] || {});
      //   history.replace("/aisApp/alarmDetails", { id: currentItem?.id });
      // });
    } else {
      message.warning("当前已是第一项");
    }
  };
  const onNext = () => {
    // const Index = alarmInfo?.list?.findIndex((v) => v?.id == currentItem?.id);
    const Index = currentItemdetails?.aisAlarmInfo?.findIndex((v: any) => v?.storageImageUrl == currentItemImg?.storageImageUrl);

    if (Index !== -1 && Index < size(currentItemdetails?.aisAlarmInfo) - 1) {
      let currentItem = currentItemdetails?.aisAlarmInfo[Index + 1];
      currentItem.indexC = Index + 1;
      setCurrentItemImg(currentItem);
      // const currentItem = alarmInfo?.list[Index + 1];
      // queryMergeAlarmInfo(currentItem?.id).then((res) => {
      //   setCurrentItem(res?.data);
      //   setCurrentItemdetails(res?.data);
      //   setCurrentItemImg(res?.data?.aisAlarmInfo[0] || {});
      //   history.replace("/aisApp/alarmDetails", { id: currentItem?.id });
      // });
    } else {
      message.warning("当前已是最后一项");
    }
  };
  const toPage = () => {
    return;
    if (!currentItemdetails?.taskType) {
      history.push(`/aisApp/testDetails?${currentItemdetails?.taskId}`);
    } else {
      history.push("/aisApp/testConfig", { edit: 1 });
    }
  };
  useEffect(() => {
    const rects = currentItemImg?.boxCoordinate?.map((v: string) => {
      return {
        rect: v.split(","),
        type: "face",
      };
    });
    setCurrentRects(rects);
  }, [currentItemImg]);
  const area = useMemo(() => {
    if (currentItemdetails?.resultJson) {
      const eventObjects = currentItemdetails?.resultJson?.events?.map((item: any) => {
        const dat = {
          bbox: { x: item.Rect?.Left, y: item.Rect?.Top, w: item.Rect?.Width, h: item.Rect?.Height },
          class: 0,
          confidence: item.Confidence,
          correct: 0,
          level: item.AlarmLevel,
          name: item.ObjName,
          name_cn: item.objNameCN,
        };
        return dat;
      });
      return {
        ...(currentItemdetails?.resultJson || {}),
        modelList: renameDuplicates(eventObjects || [], modelStyle),
        eventObjects: eventObjects,
      };
    } else {
      return {};
    }
  }, [currentItemdetails?.events]);
  return (
    <div className="alarm-details">
      <div className="alarm-details-content">
        <div className="alarm-details-content-title">识别详情</div>
        <div className="alarm-details-content-box">
          <div className="alarm-details-content-left">
            <div className="alarm-details-content-left-header">
              <div className="alarm-details-content-left-header-left">
                {/* 任务名称 */}
                {/* <div>
                  <a className="value" onClick={toPage} style={{ color: "var(--primary)" }}>
                    {currentItemdetails?.taskName || "-"}
                  </a>
                </div> */}
                {/* 设备名称   暂无 */}
                <div>{currentItemdetails?.deviceName || "-"}</div>
                {/* 算法名称 */}
                <div>
                  {currentItemdetails?.tagInfoList?.map((item: any) => {
                    return (
                      <Tag
                        style={{
                          color: "#" + item?.tagFontColor,
                          background: "#" + item?.tagBgColor,
                          borderColor: "#" + item?.tagFontColor,
                        }}
                      >
                        {item.tagName}
                      </Tag>
                    );
                  })}
                </div>
                {/* 时间 */}
                <div>{DateToFormat(currentItemdetails?.alarmTime, "YYYY-MM-DD HH:mm:ss")}</div>
              </div>
            </div>
            <div className="alarm-details-content-left-content">
              <div className="left" style={{ color: "#000", alignItems: "flex-start" }}>
                <div style={{ fontSize: 16, fontWeight: 500, marginBottom: 24 }}>识别结果</div>
                {currentItemdetails?.detainee ? (
                  <div>
                    <Tag color="volcano">在押人员 {currentItemdetails?.detainee}人</Tag>
                  </div>
                ) : (
                  <></>
                )}
                {currentItemdetails?.doctor ? (
                  <div>
                    <Tag color="blue">医生 {currentItemdetails?.doctor}人</Tag>
                  </div>
                ) : (
                  <></>
                )}
                {currentItemdetails?.police ? (
                  <div>
                    <Tag color="green">警察 {currentItemdetails?.police}人</Tag>
                  </div>
                ) : (
                  <></>
                )}
                {currentItemdetails?.others ? (
                  <div>
                    <Tag color="purple">其他 {currentItemdetails?.others}人</Tag>
                  </div>
                ) : (
                  <></>
                )}
                {/* {currentItemdetails?.aisAlarmInfo?.length
                  ? currentItemdetails?.aisAlarmInfo.map((item: any, index: number) => {
                      return (
                        <img
                          onClick={() => setCurrentItemImg({ ...item, indexC: index })}
                          className={`left-item ${currentItemImg?.indexC === index ? "left-item-select" : ""}`}
                          src={item.storageImageUrl}
                        />
                      );
                    })
                  : null} */}
              </div>
              <div className="right">
                {/* <img className="right-img" src={currentItemImg?.storageImageUrl} alt="" /> */}
                <RoiImageTag
                  visibleTitle={false}
                  visibleBorder={false}
                  imageView={false}
                  setImageView={() => {}}
                  imgUrl={currentItemdetails?.storageImageUrl}
                  ListName={true}
                  rois={area}
                />
                {/* {canCut ? (
                  <>
                    <div className="right-img-pre" onClick={onPre}>
                      <IconFont type="icon-gaojingzhongxin_shangyiye" />
                    </div>
                    <div className="right-img-next" onClick={onNext}>
                      <IconFont type="icon-gaojingzhongxin_shangyiye" />
                    </div>
                  </>
                ) : null} */}
              </div>
            </div>
          </div>
        </div>
      </div>
      <OperatingBounced
        isShow={modalshow}
        title="配置确认"
        icon={<img src={warnImg} />}
        onCancel={modalToggle}
        onOk={runhid}
        content="确认要隐藏报警吗？"
        info="报警数据隐藏后将无法在报警中心查看，且不可恢复。"
      ></OperatingBounced>
    </div>
  );
}
