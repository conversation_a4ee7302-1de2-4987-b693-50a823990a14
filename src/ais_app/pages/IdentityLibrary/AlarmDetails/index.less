.alarm-details {
  width: 100%;
  height: 100%;
  padding: 16px;
  .alarm-details-content {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    background: #fff;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
    background: var(--content-bg);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
   
    .alarm-details-content-title {
      color: #000;
      font-family: "PingFang SC";
      font-size: 24px;
      font-style: normal;
      font-weight: 600;
      line-height: 32px;
      margin-bottom: 24px;
    }
    .alarm-details-content-box {
      display: flex;
      height: 100%;
      justify-content: space-between;
      overflow: hidden;
      box-sizing: border-box;
      .alarm-details-content-left {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        margin-bottom: 24px;
        overflow: hidden;
        box-sizing: border-box;
        .alarm-details-content-left-header {
          color: rgba(0, 0, 0, 0.9);
          /* Body/Large */
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          display: flex;
          width: 100%;
          margin-bottom: 24px;
          .alarm-details-content-left-header-left {
            display: flex;
            width: 100%;
            & > div {
              margin-right: 16px;
            }
          }
        }
        .alarm-details-content-left-content {
          display: flex;
          height: calc(100% - 64px);

          .left {
            display: flex;
            align-items: center;
            flex: none;
            padding: 16px;
            width: 255px;
            box-sizing: border-box;
            flex-direction: column;
            background: #f3f3f3;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            margin-right: 24px;
            .left-item {
              width: 223px;
              height: 125px;
              margin-bottom: 24px;
              cursor: pointer;
              &:last-child{
                margin-bottom: 10px;
              }
            }
            .left-item-select {
              border: 3px solid #165dff;
              background:
                linear-gradient(0deg, rgba(22, 93, 255, 0.2) 0%, rgba(22, 93, 255, 0.2) 100%),
                url(<path-to-image>) lightgray 50% / cover no-repeat;
            }
          }
          .right {
            width: 100%;
            height: 100%;
            background: #f3f3f3;
            position: relative;
            .right-img {
              width: 100%;
              height: 100%;
              user-select: none;
            }
            .right-img-pre {
              width: 40px;
              height: 118px;
              display: flex;
              justify-content: center;
              align-items: center;
              background: rgba(0, 0, 0, 0.20);
              color: #FFF;
              position: absolute;
              top: 45%;
              cursor: pointer;
              &:hover {
                background: rgba(0, 0, 0, 0.40);
              }
            }
            .right-img-next {
              width: 40px;
              height: 118px;
              display: flex;
              justify-content: center;
              align-items: center;
              background: rgba(0, 0, 0, 0.20);
              position: absolute;
              color: #FFF;
              top: 45%;
              right: 0px;
              transform: rotate(180deg);
              cursor: pointer;
              &:hover {
                background: rgba(0, 0, 0, 0.40);
              }
            }
          }
        }
      }
      .alarm-details-content-right {
        display: flex;
        flex-direction: column;
        width: 388px;
        margin-left: 24px;
        .alarm-details-content-right-header {
          display: flex;
          justify-content: flex-end;
          width: 100%;
          margin-bottom: 24px;
        }
        .alarm-details-content-right-content{
          height: 100%;
          overflow: auto;
        }
        .alarm-details-content-right-content-title {
          color: #000;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px;
          margin-bottom: 22px;
        }
        .alarm-details-content-right-content-item {
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          .alarm-details-content-right-content-item-title {
            color: rgba(0, 0, 0, 0.6);
            /* Body/Medium */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            margin-right: 8px;
          }
          .alarm-details-content-right-content-item-data {
            color: rgba(0, 0, 0, 0.9);
            /* Body/Medium */
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
          }
        }
        .alarm-details-content-right-content-textArea {
          height: 200px;
          &::after {
            display: none;
          }
          .ant-input {
            border: none;
            resize: none;
            box-shadow: none;
          }
          .ant-input-suffix {
            position: absolute;
            bottom: 25px;
            right: 8px;
          }
        }
        .alarm-details-content-right-footer-bnts {
          width: 100%;
          display: flex;
          justify-content: flex-end;
          margin-top: 24px;
        }
        .alarm-details-content-right-content-details-item {
          display: flex;
          align-items: center;
          color: rgba(0, 0, 0, 0.9);
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          margin-bottom: 16px;
          &:last-child {
            margin-bottom: 0px;
          }
          .alarm-details-content-right-content-details-item-ic {
            display: flex;
            align-items: flex-start;
            .alarm-details-content-right-content-details-item-ic-text {
              margin-left: 8px;
            }
          }
        }
      }
    }
  }
}
