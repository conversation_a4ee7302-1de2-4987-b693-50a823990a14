import { useMemo, useState, useEffect } from "react";
import { List, Card, Row, Col, Input, Checkbox, Tag, Modal, message } from "antd";
import { IconFont, useHistory } from "@cloud-app-dev/vidc";
import { useToggle } from "ahooks";
import { SocketEmitter } from "@cloud-app-dev/vidc";
import warnImg from "@src/assets/image/warn.png";
import OperatingBounced from "@src/components/OperatingBounced";
import { hideMergeAlarmInfo } from "@src/service/ais_app";
import Authority from "@src/components/Authority";
import "./index.less";

export default function AlarmRightItem({ item, DateToFormat, isSelect, setSelectedIds, selectedIds }: any) {
  const history = useHistory();
  const [isBatch, SetIsBatch] = useState(false);
  const [isChecked, SetIsChecked] = useState(false);
  const [modalshow, { toggle: modalToggle }] = useToggle(false);
  const selectStatusClassname = isChecked ? "evidence-card-item--selected" : ""; // 选中'evidence-card-item--selected'
  useEffect(() => {
    // SetIsChecked(isSelect)
    SetIsBatch(isSelect);
    if (!isSelect) SetIsChecked(false);
  }, [isSelect]);
  useEffect(() => {
    selectedIds.includes(item.id) ? SetIsChecked(true) : SetIsChecked(false);
  }, [selectedIds]);
  const runhid = () => {
    hideMergeAlarmInfo(item?.id, item.hideFlag ? 0 : 1).then((res) => {
      if (res.code === 0) {
        message.success("操作成功");
        modalToggle();
        SocketEmitter.emit("refrshList");
      } else {
        message.warning(res.message);
      }
    });
  };
  return (
    // <div className="alarm-right-item">
    <Card
      // onClick={() => {
      //   if (!isBatch) return;
      //   SetIsChecked(!isChecked);
      //   setSelectedIds((old: any) => {
      //     return old?.includes(item.id) ? old.filter((it: any) => it !== item.id) : [...old, item.id];
      //   });
      // }}
      onClick={() => {
        history.push(`/aisApp/identityLibraryDetails`, { id: item?.id });
      }}
      className={`evidenceIden-card-dir-item ${isBatch ? "evidence-card-item--batch" : ""} ${selectStatusClassname}`}
      cover={null}
    >
      <Card.Meta
        avatar={null}
        title={null}
        
        description={
          <>
            <Row
              align="middle"
              wrap={false}
              style={{ display: "flex", alignItems: "center", width: "100%", borderRadius: 2, overflow: "hidden", position: "relative" }}
            >
              <img alt="" src={item.storageImageUrl} className="evidence-card-item__cover" />
              <div
                style={{
                  position: "absolute",
                  top: "4px",
                  left: "4px",
                }}
              >
                {item?.detainee ? <div><Tag color="volcano">在押人员 {item?.detainee}人</Tag></div> : <></>}
                {item?.doctor ? <div><Tag color="blue">医生 {item?.doctor}人</Tag></div> : <></>}
                {item?.police ? <div><Tag color="green">警察 {item?.police}人</Tag></div> : <></>}
                {item?.others ? <div><Tag color="purple">其他 {item?.others}人</Tag></div> : <></>}
              </div>
            </Row>
            <div style={{ height: "100%", padding: 8, paddingTop: 0, display: "flex", flexDirection: "column", justifyContent: "space-between" }}>
              <Row align="middle" wrap={false}>
                <div className="evidence-card-item__filename">{item?.deviceName || "-"}</div>
              </Row>
              <Row align="middle" wrap={false}>
                {/* <span className="evidence-card-item__uploader">{item?.algorithmName || "-"}</span> */}
                {item?.tagInfoList?.map((item: any) => {
                  return (
                    <div style={{ marginBottom: 4 }}>
                      <Tag
                        style={{
                          color: "#" + item?.tagFontColor,
                          background: "#" + item?.tagBgColor,
                          borderColor: "#" + item?.tagFontColor,
                        }}
                      >
                        {item.tagName}
                      </Tag>
                    </div>
                  );
                })}
              </Row>
              <Row
                style={{
                  fontSize: 12,
                }}
                justify="space-between"
                align="middle"
              >
                <Col>
                  <span className="evidence-card-dir-item-file-num">{DateToFormat(item.alarmTime, "YYYY-MM-DD HH:mm:ss") || "-"}</span>
                </Col>
                <Checkbox checked={isChecked} className="evidence-card-item__checkbox" />
                {item.processFlag == 1 ? <div className="processFlags">已处理</div> : null}
              </Row>
            </div>
          </>
        }
      />

      <Row className="evidence-card-item__actions" gutter={[0, 16]}>
        <Authority code="30000103">
          <Col
            className="evidence-card-item__action-item"
            span={12}
            onClick={() => {
              history.push(`/aisApp/alarmDetails`, { id: item?.id });
            }}
          >
            <IconFont type="icon-fangwen" className="evidence-card-item__action-icon" />
            <span className="evidence-card-item__action-label">详情</span>
          </Col>
        </Authority>
        <Authority code="30000102">
          <Col onClick={modalToggle} className="evidence-card-item__action-item" span={12}>
            <IconFont type={item.hideFlag ? "icon-browse-off" : "icon-browse"} style={{ fontSize: 15 }} className="evidence-card-item__action-icon" />
            <span className="evidence-card-item__action-label">{item.hideFlag ? "隐藏" : "取消隐藏"}</span>
          </Col>
        </Authority>
      </Row>
      <OperatingBounced
        isShow={modalshow}
        title="配置确认"
        icon={<img src={warnImg} />}
        onCancel={modalToggle}
        onOk={runhid}
        content={item.hideFlag ? "是否隐藏报警？" : "是否取消隐藏报警？"}
        // info="报警数据隐藏后将无法在报警中心查看，且不可恢复。"
      ></OperatingBounced>
    </Card>
    // </div>
  );
}
