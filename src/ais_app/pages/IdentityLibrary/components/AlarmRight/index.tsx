import { IconFont } from "@cloud-app-dev/vidc";
import { useSafeState, useToggle, useRequest } from "ahooks";
import { identityLibraryList, queryAlgorithmList, getTimeDimensionStatistics, hideMergeAlarmInfoBatch } from "@src/service/ais_app";
import { Button, message, Space, Checkbox, Popover, Select, Modal, Input } from "antd";
import { DBTools, SocketEmitter, useInfiniteScroll } from "@cloud-app-dev/vidc";
import { useEffect, useMemo, useRef } from "react";
import { DateToFormat } from "@src/ais_app/utils";
import { isEmpty, uniqBy } from "lodash-es";
import DynamicGridList from "../../../../components/DynamicGridList";
import AlarmRightItem from "../AlarmRightListBox/components/AlarmRightItem";
import { size } from "lodash-es";
import Temporarily from "@src/ais_app/components/Temporarily"
import dayjs from "dayjs";
import "./index.less";
const PAGE_SIZE = 20;
export const db = new DBTools("alarmlist");
export default function AlarmRight({ params, paramsNum }: any) {
  const ref = useRef<HTMLDivElement>(null);
  const { data: sflist } = useRequest(() => queryAlgorithmList({ limit: 1000, offset: 0 }));
  const [total, setTotal] = useSafeState(0);
  const [state, stateChange]: any = useSafeState({ value: "desc", label: "最近识别时间-由近到远" });
  const [selectedIds, setSelectedIds]: any = useSafeState([]);
  const [selectedIdsTime, setSelectedIdsTime]: any = useSafeState({});
  const [list, setList] = useSafeState([]);
  const [hideFlag, setHideFlag] = useSafeState(true);
  const [value, setValue] = useSafeState("");
  const [refsh, { toggle }] = useToggle(false);
  const [isSelect, { toggle: setIsSelect }] = useToggle(false);
  const [localrefsh, { toggle: lToggle, setLeft, setRight }] = useToggle(false);
  const loadPage = async (d: any): Promise<any> => {
    d.page ? d.page++ : (d.page = 1);
    const param = {
      offset: (d?.page - 1) * 30,
      limit: 30,
      ...params,
       alarmEndTime: params.alarmEndTime || dayjs()?.valueOf(),
      algorithmIds: ['personneltypes'],
      keywords: value,
      sortField: "alarmTime",
      sortOrder: state.value,
      isPagination: true,
    };
    if (paramsNum["detainee"].isChecked) {
      param.detainee = paramsNum["detainee"].value;
    }
    if (paramsNum["doctor"].isChecked) {
      param.doctor = paramsNum["doctor"].value;
    }
    if (paramsNum["police"].isChecked) {
      param.police = paramsNum["police"].value;
    }
    if (paramsNum["others"].isChecked) {
      param.others = paramsNum["others"].value;
    }
    const res = await identityLibraryList({ ...param });
    setTotal(res?.data?.totalCount);
    const resultData = res?.data?.list;

    d.total = res?.data?.totalCount;
    d.list ? (d.list = uniqBy([...d.list, ...resultData], "id")) : (d.list = resultData);
    setList(d.list ? (d.list = uniqBy([...d.list, ...resultData], "id")) : (d.list = resultData));
    db.put({ id: "alarminfo", value: d.list, total: res?.data?.totalCount }).then((res) => {
      SocketEmitter.emit("listChange");
    });
    if (localrefsh) {
      message.success("刷新成功");
      setLeft();
    }
    return d;
  };
  useEffect(() => {
    SocketEmitter.on("refrshList", toggle);
    SocketEmitter.on("alarmMessage", toggle);
    return () => SocketEmitter.on("refrshList", toggle);
  }, []);
  const hideFlagAllHandler = (hideFlag: any) => {
    Modal.confirm({
      title: `批量${!hideFlag ? "隐藏" : "取消隐藏"}`,
      content: `确定要批量${!hideFlag ? "隐藏" : "取消隐藏"}吗？`,
      okText: "确定",
      cancelText: "取消",
      onOk: async () => {
        hideMergeAlarmInfoBatch({ ids: selectedIds, hideFlag }).then((res) => {
          if (res.code === 0) {
            toggle();
            setSelectedIds([]);
            message.success("操作成功");
          }
        });
      },
    });
  };
  const getTimeDimensionStatisticsHander = (page: number, pageSize: number, oldlist: any): Promise<{ list: any; total: number }> => {
    const options = {
      offset: (page + 1) * pageSize,
      limit: pageSize,
      isPagination: true,
      sortOrder: "desc",
      timeDimension: state.timeDimension,
      // timeStr: dayjs().subtract(state.value, state.type),
      alarmEndTime: params.alarmEndTime || dayjs()?.valueOf(),
      hideFlag: hideFlag ? null : 1,
      alarmStartTime: params.alarmStartTime || dayjs().subtract(30, "day")?.valueOf(),
    };
    return getTimeDimensionStatistics(options).then(({ data }: any) => ({
      list: [...oldlist, ...(data?.list || [])],
      total: data?.totalCount || 0,
      totalPage: data?.totalPage || 0,
    }));
  };
  const { data = { list: [] } } = useInfiniteScroll(
    (d) => {
      // offset 0开始
      const page = d?.list.length ? Math.ceil(d.list.length / PAGE_SIZE) - 1 : -1;
      return getTimeDimensionStatisticsHander(page, PAGE_SIZE, d ? d?.list : []);
      // return getTimeDimensionStatistics(page, PAGE_SIZE, d ? d?.list : []);
    },
    {
      target: ref,
      reloadDeps: [state, params, hideFlag],
      isNoMore: (d) => (d ? size(d?.list) === +d?.total : false),
    },
  );
  const reloadDeps = useMemo(
    () => [params, refsh, localrefsh, selectedIdsTime, hideFlag, state, value, paramsNum],
    [params, refsh, localrefsh, selectedIdsTime, hideFlag, state, value, JSON.stringify(paramsNum)],
  );
  console.log(paramsNum, 'paramsNum')
  return (
    <div className="AlarmRight" key={JSON.stringify(paramsNum)}>
      <div className="header">
        <Select
          style={{ width: "240px" }}
          value={state.value}
          onChange={(e, it) => {
            stateChange(it);
            setSelectedIdsTime({});
          }}
          options={[
            { value: "desc", label: "最近识别时间-由近到远" },
            { value: "asc", label: "最近识别时间-由远到近" },
          ]}
        ></Select>
        <Space>
          <Input
            onChange={(e) => {
              setValue(e.target.value);
            }}
            placeholder="请输入设备名称搜索"
            style={{ width: "240px" }}
          />
        </Space>
      </div>
      <div className="content">
        {
          total === 0 ?
            <div style={{ height: '100%', width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Temporarily marginTop={0} />
            </div>
            :
            <></>
        }
        <div style={total === 0 ? { display: "none" } : {width:'100%',height:'100%'}}>
        <DynamicGridList
              itemKey="id"
              itemHeight={230}
              itemWidth={276}
              isNoMore={(data) => data?.list?.length === total}
              loadPage={loadPage}
              reloadDeps={reloadDeps}
              renderItem={(item: any, index: any) => {
                return (
                  <div style={{ display: "flex", justifyContent: "flex-end", width: !item.timeStr ? "276px" : "217px" }}>
                    <AlarmRightItem
                      selectedIds={selectedIds}
                      setSelectedIds={setSelectedIds}
                      isSelect={isSelect}
                      item={item}
                      DateToFormat={DateToFormat}
                    />
                  </div>
                );
              }}
            />
            </div>
      </div>
    </div>
  );
}
