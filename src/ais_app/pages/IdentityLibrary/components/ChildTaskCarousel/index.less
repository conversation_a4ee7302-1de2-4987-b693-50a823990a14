.Chart2bCarouselalarm {
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  flex: auto;
  height: 132px;
  .panel {
    .row {
      white-space: nowrap;
    }
  }

  .prev,
  .next {
    position: absolute;
    color: var(--primary-light);
    cursor: pointer;
    font-size: var(--fs);
  }

  .prev {
    left: -18px;
  }

  .container {
    width: 100%;
    height: 100%;

    .ant-carousel {
      position: relative;
      // top: 50%;
      // transform: translateY(-50%);
    }
  }

  .next {
    right: -18px;
  }
}
