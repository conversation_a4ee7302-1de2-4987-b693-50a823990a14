import { SocketEmitter } from "@cloud-app-dev/vidc";
import VideoLayout from "@src/components/VideoLayout";
import { useMount, useSafeState } from "ahooks";
import { useState } from "react";
import AlarmLeft from "./components/AlarmLeft";
import AlarmRight from "./components/AlarmRight";
import "./index.less";
export default function EventAlarm() {
  const [params, setParams] = useSafeState({});
  const [paramsNum, setParamsNum] = useSafeState({
    detainee: {
      value: 1,
      isChecked: false,
      title: "在押人员数量",
      key: "detainee",
    },
    doctor: {
      value: 1,
      isChecked: false,
      title: "医生数量",
      key: "doctor",
    },
    police: {
      value: 1,
      isChecked: false,
      title: "警察数量",
      key: "police",
    },
    others: {
      value: 1,
      isChecked: false,
      title: "其他人员数量",
      key: "others",
    },
  });
  useMount(() => {
    SocketEmitter.emit("clearMessage");
  });
  return (
    <div className="IdentityLibrary">
      <VideoLayout
        leftControl={<AlarmLeft setParams={setParams} paramsData={params} paramsNum={paramsNum} setParamsNum={setParamsNum} />}
        RightContent={<AlarmRight paramsNum={paramsNum} params={params} />}
      />
    </div>
  );
}
