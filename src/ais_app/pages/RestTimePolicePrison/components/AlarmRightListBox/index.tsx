import { List } from "antd";
import AlarmRightItem from "./components/AlarmRightItem";
import AlarmRightDir from "./components/AlarmRightDir";
import "./index.less";

export default function AlarmRightListBox(props: any) {
  return (
    <div className="alarm-right-list-box">
      {/* 报警列表 */}
      {
        [
            { id: 1, children: [1, 2, 3] },
            { id: 2, children: [1, 2, 3] },
            { id: 3, children: [1, 2, 3] },
          ].map((prop,index)=>{
            return <AlarmRightDir data={prop} index={index} />;
          })
      }
    </div>
  );
}
