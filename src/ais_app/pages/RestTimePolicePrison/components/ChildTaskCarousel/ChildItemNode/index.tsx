
import { DateToFormat } from '@src/ais_app/utils';
import './index.less';
interface ChildItemNodeType{
  item:any;
  index:number;
  currentAlgorithm:any;
  setCurrentAlgorithm:any;
}
const ChildItemNode = ({ index,item,currentAlgorithm,setCurrentAlgorithm }:ChildItemNodeType) => {
  return (
    <div className={`${currentAlgorithm?.storageImageUrl === item?.storageImageUrl && 'currentChart2bNodealarm' } Chart2bNodealarm`} onClick={()=>{setCurrentAlgorithm(item)}}>
      <div className='aiconbox'>
        <img src={item?.storageImageUrl} alt="" />
      </div>
      <div className='rightcoent'>
        <div className="index">{index+1}</div>
        <div className='time'>{DateToFormat(item?.alarmTime,'YYYY-MM-DD HH:mm:ss') || '-'}</div>
      </div>
    </div>
  );
};
export default ChildItemNode;
