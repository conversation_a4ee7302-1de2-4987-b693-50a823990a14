import React, { useState, useEffect } from "react";
import { getCategory } from "@src/service/ais_app";

function useDict(typeCode: any) {
  const [codes, setCodes]: any = useState([]);
  useEffect(() => {
    getCategory(typeCode).then((res: any) => {
      const data = res.data || {};

      const newDataList = Object.keys(data).map((i, d) => {
        return data[i];
      });
      setCodes(newDataList);
    });
  }, []);

  return codes;
}
export default useDict;
