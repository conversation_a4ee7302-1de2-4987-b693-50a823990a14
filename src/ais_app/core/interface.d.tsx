export interface HttpResult<T> {
  code: number;
  codeRemark: string;
  data: T;
  message?: string;
}

export type TaskItemType = {
  alarmSum: number;
  algorithmId: number;
  algorithmName: string;
  cid: number;
  createdBy: string;
  createdTime: number;
  detectInterval: number;
  deviceName: string;
  duration: number;
  id: number;
  lastAlarmTime: number;
  systemId: number;
  taskName: string;
  taskState: string;
  updatedBy: string;
  updatedTime: number;
};
