import { Carousel, Row, Col } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import { CarouselRef } from 'antd/lib/carousel';
import { IconFont } from '@cloud-app-dev/vidc';
import { chunk } from 'lodash-es';
import { ChildTaskCarouselType, ChildNode } from './index.d';
import { useSize } from 'ahooks';
import './index.less';

const Chart2bCarouselCom = ({ list, render }: ChildTaskCarouselType) => {
  const itemRef = useRef<HTMLDivElement>(null);
  const CarouselRef = useRef<CarouselRef>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const size = useSize(containerRef);
  const [cluNum, setcluNum] = useState<number>(6);
  const [currentList, setCurrentList] = useState<ChildNode[][][]>(chunk(list, cluNum * 1).map((o) => chunk(o, cluNum)));
  const timer = useRef<NodeJS.Timeout>();
  useEffect(() => {
    return () => {
      timer.current && clearTimeout(timer.current);
    };
  }, []);
  const prev = () => {
    CarouselRef.current?.goTo(currentPage - 1);
  };
  const next = () => {
    CarouselRef.current?.goTo(currentPage + 1);
  };
  const afterChange = (current: number) => {
    setCurrentPage(current);
  };
  const handle = useCallback(
    (size: any) => {
      const { width } = size;
      timer.current && clearTimeout(timer.current);
      timer.current = setTimeout(() => {
        const containerWidth = width;
        const itemWidth = 216;
        //  (itemRef.current?.offsetWidth || 0) > 216 ? 216 : itemRef.current?.offsetWidth || 0;
        let colNum = 0;
        let rowNum = 2;
        while ((colNum + 1) * itemWidth <= containerWidth) {
          colNum++;
        }
        !colNum && (colNum = 1);
        !rowNum && (rowNum = 1);
        if (colNum && rowNum) {
          setcluNum(colNum);
          setCurrentList(chunk(list, colNum * rowNum).map((o) => chunk(o, colNum)));
          setCurrentPage(0);
          CarouselRef.current?.goTo(0);
        }
      }, 200);
    },
    [list]
  );
  useEffect(() => {
    if (size?.width && size?.height) {
      handle(size);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [size, list]);
  return (
    <div className="Chart2bCarousel" ref={containerRef}>
      {currentPage > 0 && <IconFont type="icon-gaojingzhongxin_shangyige" className="prev" onClick={prev} />}
      <div className="container">
        <Carousel ref={CarouselRef} dots={false} afterChange={afterChange}>
          {currentList.map((panel, panelIndex) => {
            return (
              <div className="panel" style={{ height: `${containerRef.current?.offsetHeight || 0}px` }} key={panelIndex}>
                {panel.map((rows, rowIndex) => {
                  return (
                    <div key={rowIndex} style={{ display: 'grid', gridTemplateColumns: `repeat(${cluNum},1fr)`, gap: '16px' }}>
                      {rows.map((item, itemIndex) => {
                        return (
                          <div key={itemIndex} ref={itemRef}>
                            {render(item)}
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </Carousel>
      </div>
      {currentPage < currentList.length - 1 && <IconFont style={{transform: 'rotate(180deg)'}} type="icon-gaojingzhongxin_shangyige" className="next" onClick={next} />}
    </div>
  );
};
const ChildTaskCarousel = ({ list, render }: ChildTaskCarouselType) => {
  return <Chart2bCarouselCom list={list} render={render} />;
};
export default ChildTaskCarousel;
