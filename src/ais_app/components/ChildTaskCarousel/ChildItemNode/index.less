.currentChart2bNode.Chart2bNode {
  background: #E8F3FF;
  border: 2px solid var(--secondary2);
  padding: 9px 15px;
}
.Chart2bNode:hover {
  background: #E8F3FF;
  border: 2px solid var(--secondary2);
  padding: 9px 15px;
}
.Chart2bNode {
  padding: 10px 16px;
  box-sizing: border-box;
  height: 64px;
  cursor: pointer;
  box-sizing: border-box;
  border: 1px solid var(--gray6);
  border-radius: var(--radius2);
  margin-top: 14px;
  display: flex;
  align-items: center;
  .aiconbox {
    width: 36px;
    height: 36px;
    position: relative;
    .anticon{
      position: absolute;
      top: -7px;
      left: -7px;
      color: var(--success);
    }
    img {
      width: 100%;
      height: 100%;
    }
  }
  .rightcoent {
    margin-left: 10px;
    .title {
      color: var(--gray2);
      font-weight: 500;
    }
    .bb {
      font-size: var(--fs-small);
      color: var(--gray6);
    }
  }
}
