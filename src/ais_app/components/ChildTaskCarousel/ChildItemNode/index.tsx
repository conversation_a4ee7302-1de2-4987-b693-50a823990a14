
import { IconFont } from '@cloud-app-dev/vidc';
import './index.less';
interface ChildItemNodeType{
  item:any;
  currentAlgorithm:any;
  changeAthm:any;
  isConfig:boolean;
}
const ChildItemNode = ({ item,currentAlgorithm,changeAthm,isConfig }:ChildItemNodeType) => {
  return (
    <div className={`${currentAlgorithm?.algorithmName === item?.algorithmName && 'currentChart2bNode' } Chart2bNode`} onClick={()=>{changeAthm(item)}}>
      <div className='aiconbox'>
        <img src={`data:image/svg+xml;base64,${item?.icon}`} alt="" />
        {
          isConfig && <IconFont type='icon-renwuchakan_lunxunrenwu_yiwancheng'/>
        }
      </div>
      <div className='rightcoent'>
        <div title={item?.algorithmNoteName || '-'} className="title">{item?.algorithmNoteName||'-'}</div>
        <div className='bb'>{item?.algorithmVersion || '-'}</div>
      </div>
    </div>
  );
};
export default ChildItemNode;
