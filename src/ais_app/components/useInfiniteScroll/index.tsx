import { useEffect, useMemo, useState } from 'react';
import { useEventListener, useUpdateEffect } from 'ahooks';
import { getTargetElement } from 'ahooks/es/utils/domTarget';
import { getClientHeight, getScrollHeight, getScrollTop } from 'ahooks/es/utils/rect';
import type { BasicTarget } from 'ahooks/lib/utils/domTarget';

export type TData<T> = {
  list: T[];
  [key: string]: any;
};

function useInfiniteScroll<T>(
  fetcher: (currentData?: TData<T>) => Promise<TData<T>>,
  options: {
    target?: BasicTarget;
    isNoMore?: (data?: TData<T>) => boolean;
    threshold?: number;
    reloadDeps?: any[];
  }
) {
  const { target, isNoMore, threshold, reloadDeps = [] } = options;
  const [state, setState] = useState<{ data: TData<T>; loadding: boolean }>({ data: { list: [] }, loadding: false });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const noMore = useMemo(() => (!isNoMore ? false : isNoMore(state.data)), [state.data]);
  const loadMore = (flag?: boolean) => {
    if (!flag && (isNoMore?.(state.data) ?? false)) {
      return;
    }
    setState((old) => ({ ...old, loadding: true }));
    fetcher(flag ? { list: [] } : state.data).then((res) => setState((old) => ({ ...old, data: { ...res }, loadding: false })));
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => loadMore(true), reloadDeps);

  const scrollMethod = () => {
    const el = getTargetElement(target);
    if (!el) {
      return;
    }

    const scrollTop = getScrollTop(el);
    const scrollHeight = getScrollHeight(el);
    const clientHeight = getClientHeight(el);

    if (scrollHeight - scrollTop <= clientHeight + (threshold || 50)) {
      loadMore();
    }
  };

  useEventListener(
    'scroll',
    () => {
      if (state.loadding) {
        return;
      }
      scrollMethod();
    },
    { target }
  );

  useUpdateEffect(
    () => {
      const el = getTargetElement(target);
      if (!el) {
        return;
      }
      el.scrollTo({ left: 0, top: 0 });
    },
    reloadDeps ? reloadDeps : []
  );

  return {
    noMore,
    data: state.data,
    loading: state.loadding,
  };
}

export default useInfiniteScroll;
