.LeftControlLayout {
  width: 100%;
  height: 100%;
  position: relative;
  .topContent {
    position: absolute;
    z-index: 1;
    width: 100%;
    top: 0;
    bottom: 104px;
    min-height: 120px;
    background-color: var(--primary-dark);
    padding-top: 3px;
  }
  .drahline {
    position: absolute;
    height: 5px;
    width: 100%;
    background-color: var(--primary-dark);
    cursor: row-resize;
    user-select: none;
    z-index: 2;
    .line {
      height: 1px;
      width: 100%;
      background-color: var(--gray6);
    }
  }
  .botContent {
    position: absolute;
    width: 100%;
    bottom: 0;
    height: 100px;
    background-color: var(--primary-dark);
    .flexContent {
      height: 100%;
      width: 100%;
      background-color: var(--primary-dark);
      position: absolute;
      top: 0;
      z-index: 1;
    }
    .fixContent {
      position: absolute;
      z-index: 1;
      bottom: 0;
      width: 100%;
      height: max-content;
      background-color: var(--primary-dark);
      border-top: 1px solid var(--gray6);
      padding: 0 16px;
      .noOpen {
        width: 100%;
        height: 55px;
        color: var(--gray1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        .titlecontent {
          display: flex;
          align-items: center;
          .icon {
            font-size: 24px;
          }
          .title {
            font-size:  var(--fs-large);
            font-weight: 500;
            margin-left: 16px;
          }
        }
        .chose {
          font-size: var(--fs);
          cursor: pointer;
        }
        .open {
          transform: rotateX(180deg);
        }
        .close {
          transform: rotateX(0deg);
        }
      }
      .OpenContent {
        width: 100%;
        height: max-content;
      }
    }
  }
}
