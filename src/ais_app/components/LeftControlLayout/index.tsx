import { useSafeState, useSize } from "ahooks";
import { ReactElement, useEffect, useMemo, useRef } from "react";
import { IconFont } from "@cloud-app-dev/vidc";
import "./index.less";
export default function LeftControlLayout({
  topEle,
  BotEle,
  ControlEle,
  ControlTitle,
  setTarget,
  openChange,
  toggle,
}: {
  topEle: ReactElement;
  BotEle: ReactElement;
  ControlEle?: ReactElement;
  ControlTitle?: string;
  setTarget?: (v: HTMLElement | null) => void;
  openChange?: boolean;
  toggle?: () => void;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const size = useSize(ref);
  const botRef = useRef<HTMLDivElement>(null);
  const conRef = useRef<HTMLDivElement>(null);
  const botsize = useSize(botRef);
  const consize = useSize(conRef);
  const [state, setState] = useSafeState({
    isHResize: false,
    hNum: 500,
    hNumLimit: 35,
    lineHeight: 5,
    defaulthNum: 100,
  });
  const hCursor = useMemo(() => {
    return state.isHResize ? "row-resize" : "default";
  }, [state]);
  const hResizeDown = () => {
    setState((old) => ({ ...old, isHResize: true }));
  };
  useEffect(() => {
    setTarget && setTarget(document.getElementById("CalendarHandlePortal"));
  }, [openChange]);
  useEffect(() => {
    const { height = 0 } = consize || {};
    const { height: btoheight = 0 } = botsize || {};
    const { hNum, hNumLimit } = state;
    if (btoheight < height + hNumLimit && hNum < height + hNumLimit) {
      setState((old) => ({ ...old, hNum: height + hNumLimit }));
    }
  }, [consize]);
  const hResizeOver = (e: any) => {
    const { isHResize, hNum, hNumLimit, defaulthNum, lineHeight } = state;
    const { height = 0 } = size || {};
    const { height: conheight = 0 } = consize || {};
    const hNumLimitcopy = hNumLimit;
    if (isHResize && hNum >= hNumLimit && height - hNum >= hNumLimit) {
      let newValue = height + defaulthNum + hNumLimit - lineHeight - e.clientY;
      if (ControlTitle) {
        newValue = newValue - 19;
      } else {
        newValue = newValue + 94;
      }
      if (newValue < conheight + hNumLimit) {
        newValue = conheight + hNumLimit;
      }
      //上边距8，下边距5
      if (newValue > height - hNumLimit - 13) {
        newValue = height - hNumLimit - 13;
      }
      setState((old) => ({ ...old, hNum: newValue, hNumLimit: hNumLimitcopy }));
    }
  };
  const stopResize = () => {
    setState((old) => ({ ...old, isHResize: false }));
  };
  return (
    <div
      className="LeftControlLayout"
      onMouseMove={state.isHResize ? hResizeOver : () => {}}
      onMouseUp={stopResize}
      onMouseLeave={stopResize}
      ref={ref}
    >
      <div className="topContent" style={{ bottom: state.hNum, cursor: hCursor }}>
        {topEle}
      </div>
      <div className="drahline" style={{ bottom: state.hNum }} draggable={false} onMouseDown={hResizeDown}>
        <div className="line"></div>
      </div>
      <div className="botContent" style={{ height: state.hNum + state.lineHeight, cursor: hCursor }}>
        <div className="flexContent" ref={botRef} style={{ paddingBottom: consize && consize?.height }}>
          {BotEle}
        </div>
        {ControlTitle && (
          <div className="fixContent" ref={conRef}>
            <div className="noOpen">
              <div className="titlecontent">
                <div className="icon">
                  <IconFont type="icon-renwupeizhi_shebeirenwu_shebeimulu" />
                </div>
                <div className="title">{ControlTitle}</div>
              </div>
              <div className={openChange ? "chose open" : "chose close"} onClick={toggle}>
                <IconFont type="icon-shishiyulan_fangxiangjiantou" />
              </div>
            </div>
            {openChange && <div className="OpenContent">{ControlEle}</div>}
          </div>
        )}
      </div>
    </div>
  );
}
