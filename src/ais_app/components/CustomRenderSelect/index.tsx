import { css } from "@emotion/css";
import { useClick<PERSON><PERSON>, useToggle } from "ahooks";
import { Select, SelectProps } from "antd";
import React, { useMemo, useRef } from "react";
import { uuid } from "@cloud-app-dev/vidc";

export const customRenderSelect = css`
  position: relative;
  display: inline-flex;
  width: max-content;
  overflow: hidden;
`;

const customSelect = css`
  position: absolute;
  top: 50%;
  left: 50%;
  width: max-content;
  height: 100%;
  opacity: 0;
  transform: translate(-50%, -50%);
`;

export const customRenderbox = css`
  position: relative;
  display: inline-block;
  width: max-content;
  overflow: hidden;
`;

interface ICustomRenderSelectProps extends SelectProps {
  children?: any;
  value: any;
  customRender: (value: any) => JSX.Element;
  onChange: (value: any) => void;
  selectAotoClose?: boolean;
}

function CustomRenderSelect({
  children,
  value,
  customRender,
  popupClassName,
  getPopupContainer,
  onChange,
  selectAotoClose = true,
  ...props
}: ICustomRenderSelectProps) {
  const classname = useMemo(() => `custom-render-select-dropdown-${uuid()}`, []);
  const domRef = useRef<HTMLDivElement>(null);
  const [open, { toggle, set }] = useToggle();

  const getDoms = useMemo(
    () => [() => domRef.current?.querySelector(".custom-render-box"), () => document.querySelector(`.${classname}`)],
    [classname],
  );

  useClickAway(() => set(false), getDoms, ["click"]);

  const onSelectChange = (v: any) => {
    onChange?.(v);
    if (selectAotoClose) {
      set(false);
    }
  };

  return (
    <div className={customRenderSelect} ref={domRef}>
      <Select
        {...props}
        value={value}
        className={customSelect}
        popupClassName={`${classname} ${popupClassName}`}
        getPopupContainer={getPopupContainer ? getPopupContainer : () => domRef.current?.parentElement?.parentElement ?? document.body}
        open={open}
        onChange={onSelectChange}
      >
        {children}
      </Select>
      <div className={customRenderbox} onClick={toggle}>
        {customRender(value)}
      </div>
    </div>
  );
}

export default CustomRenderSelect;
