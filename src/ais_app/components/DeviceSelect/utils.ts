import { cache } from '@cloud-app-dev/vidc';
import { Service } from '@cloud-app-dev/vidc';

export type GroupItemType = {
  code: string;
  name: string;
};

export interface TreeItemType {
  id: string;
  groupName: string;
  offLine: number;
  onLine: number;
}

function getHeader() {
  return { Authorization: cache.getCache('token', 'session') };
}

export function fetchTree(): Promise<TreeItemType[]> {
  return Service.http({
    headers: getHeader(),
    url: '/api/dvia-app-scene-server/device/deviceBuzGroup/deviceBuzGroupList/query',
  }).then((res) => res.data ?? []);
}

export function fetchList(data:any) {
  return Service.http({
    headers: getHeader(),
    url: '/api/dvia-app-scene-server/device/systemDevice/deviceList/query',
    data,
    method: 'post',
  }).then((res) => res.data ?? []);
}
