.device-select {
  display: flex;
  min-height: 500px;
  .select-content-part {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    // border: 1px solid var(--bd);
    &.org-tree-select {
      .tree-content {
        padding: 10px;
        overflow: auto;
        height: 100%;
      }
    }
    &.devive-list {
      border-left: none;
      display: flex;
      flex-direction: column;
      .l-c-dynamic-list-layout {
        border: none;
        flex: 1;
      }
      &.devive-assign-list {
        border-left: 1px solid var(--gray3);
      }
    }
  }
  .tree-title {
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    background-color: transparent;
    border-bottom: 1px solid var(--bd);
    & > span {
      cursor: pointer;
    }
  }
  .device-item {
    padding: 0 10px;
    .name{
      display: block;
      width: 215px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .list-from {
    padding: 10px;
    & > div {
      margin-bottom: 10px;
    }
  }
}
