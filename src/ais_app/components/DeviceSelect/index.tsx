import { useRequest, useUpdateEffect } from "ahooks";
import { Checkbox, Modal, Tree, Input } from "antd";
import { useMemo, useState } from "react";
import { treeHelper } from "@cloud-app-dev/vidc";
import { fetchList, fetchTree } from "./utils";
import { IconFont } from "@cloud-app-dev/vidc";
import DynamicList from "@cloud-app-dev/vidc/es/DynamicList";
import "./index.less";

interface IDeviceSelectProps {
  value?: string[];
  onChange?: (cids: string[]) => void;
}
interface IDeviceSelectState {
  group?: string;
  treeKey?: string;
  expandedKeys?: string[];
  fouceKey?: number;
}

// 定义一个名为 DeviceSelect 的函数组件，接收两个 props：onChange 和 value
function DeviceSelect({ onChange, value }: IDeviceSelectProps) {
  // 使用 useState 钩子初始化组件状态，初始状态包含 group、treeKey 和 expandedKeys
  const [state, setState] = useState<IDeviceSelectState>({ group: undefined, treeKey: undefined, expandedKeys: [] });
  // 使用 useRequest 钩子发送请求获取树形数据，默认值为空数组
  const { data: treeRes = [] } = useRequest(fetchTree);
  // 使用 useMemo 钩子计算树形数据，依赖 treeRes，调用 treeHelper.computTreeList 方法处理 treeRes
  const treeData: any = useMemo(() => treeHelper.computTreeList(treeRes), [treeRes]);
  // 定义 stateChange 函数，用于更新组件状态，合并旧状态和新数据
  const stateChange = (data: IDeviceSelectState = {}) => setState((old) => ({ ...old, ...data }));
  // 使用 useUpdateEffect 钩子在 treeData 更新时执行，初始化 expandedKeys 为 treeData 第一个节点的 id
  useUpdateEffect(() => {
    stateChange({ expandedKeys: treeData[0] ? [treeData[0].id] : [] });
  }, [treeData]);
  // 渲染组件的 JSX 结构
  return (
    <div className="device-select">
      <div className="org-tree-select select-content-part">
        <div className="tree-title">设备目录</div>
        <div className="tree-content">
          {/* 渲染 Tree 组件，传入 treeData、expandedKeys、onExpand、onSelect 等属性 */}
          <Tree
            treeData={treeData as any}
            expandedKeys={state.expandedKeys}
            onExpand={(ids) => stateChange({ expandedKeys: ids as any })}
            onSelect={(treeKeys) => stateChange({ treeKey: treeKeys[0] as string })}
            switcherIcon={<IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" style={{ transform: "rotate(90deg)" }} />}
            fieldNames={{ title: "groupName", key: "id" }}
            defaultExpandedKeys={treeData[0] ? [treeData[0].id] : []}
            titleRender={(node: any) => (
              <div>
                <span>{node.groupName}</span>
              </div>
            )}
          />
        </div>
      </div>

      {/* 渲染 DeviceList 组件，传入 treeKey、value 和 onChange */}
      <DeviceList treeKey={state.treeKey} value={value} onChange={onChange} />
    </div>
  );
}

function DeviceList({ treeKey, value, onChange }: { treeKey?: string; value?: string[]; onChange?: (selectPoints: any[]) => void }) {
  const [state, setState] = useState({ keyword: "", selectPoints: value ?? ([] as any[]) });
  const loadPage = async (d: any) => {
    const pageSize = 30;
    d.page ? d.page++ : (d.page = 1);
    const options = {
      buzGroupId: treeKey,
      offset: (d.page - 1) * pageSize,
      limit: pageSize,
      keywords: state.keyword,
    };
    const resultData = await fetchList(options);
    d.count = resultData.totalCount;
    d.list ? (d.list = [...d.list, ...resultData.list]) : (d.list = resultData.list);
    if (d) return d;
  };

  const onChecked = (data: any) => {
    setState((old) => {
      if (old.selectPoints.findIndex((v) => v?.cid === data.cid) !== -1) {
        const arr = old.selectPoints.filter((v) => v.cid !== data.cid);
        return { ...old, selectPoints: arr };
      } else {
        return { ...old, selectPoints: [...old.selectPoints, data] };
      }
    });
  };

  useUpdateEffect(() => onChange?.(state.selectPoints), [state.selectPoints]);

  return (
    <div className="devive-list select-content-part">
      <div className="tree-title">设备列表</div>
      <div className="list-from">
        <div>
          <Input
            value={state.keyword}
            placeholder="请输入设备名称/设备IP"
            onChange={(e) => setState((old) => ({ ...old, keyword: e.target.value }))}
          />
        </div>
      </div>
      <DynamicList
        renderItem={(item: any) => (
          <div className="device-item" key={item?.cid}>
            <Checkbox
              style={{ width: "100%" }}
              checked={state.selectPoints.map((v) => v.cid).includes(item.data.cid)}
              onClick={() => onChecked(item.data)}
            >
              <span className="name" title={item.data.name}>
                {item.data.name}
              </span>
            </Checkbox>
          </div>
        )}
        itemHeight={30}
        loadPage={loadPage}
        reloadDeps={[state.keyword, treeKey]}
      />
    </div>
  );
}

export function ModleDeviceSelect() {
  return (
    <Modal open={true} width={1000} title="设备选择">
      <DeviceSelect></DeviceSelect>
    </Modal>
  );
}

export default DeviceSelect;
