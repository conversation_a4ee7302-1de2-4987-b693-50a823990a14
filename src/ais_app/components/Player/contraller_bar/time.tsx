import { css } from '@emotion/css';
import { useTimes } from '../timeline';
import { timeStamp } from '../util';

const classname = css`
  float: left;
  padding: 0 6px;
  font-size: 10px;
  line-height: 28px;
  color: #fff;
`;

function Time() {
  const [current, , duration] = useTimes();
  return (
    <span className={classname}>
      {timeStamp(current)}/{timeStamp(duration || 0)}
    </span>
  );
}

export default Time;
