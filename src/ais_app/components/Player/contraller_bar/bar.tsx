import { css } from '@emotion/css';
import React from 'react';

interface IBarProps extends React.HTMLAttributes<HTMLSpanElement> {
  visibel?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const contrallerBarItem = css`
  display: flex;
  float: left;
  width: 28px;
  height: 28px;
  justify-content: center;
  align-items: center;
  .anticon {
    cursor: pointer;
    &:hover {
      color: var(--primary);
    }
  }
`;

function Bar({ visibel = true, className = '', children, ...props }: IBarProps) {
  if (visibel === false) {
    return null;
  }
  return (
    <span className={`${contrallerBarItem} ${className}`} {...props}>
      {children}
    </span>
  );
}

export default Bar;
