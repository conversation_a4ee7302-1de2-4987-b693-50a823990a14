import { css } from '@emotion/css';
import React from 'react';
import LeftBar from './left_bar';
import RightBar from './right_bar';

interface IContrallerBarProps {
  rightExtContents: React.ReactNode;
  rightMidExtContents: React.ReactNode;
  visibel?: boolean;
  leftExtContents: React.ReactNode;
  leftMidExtContents: React.ReactNode;
  reload: () => void;
  hideTimeProgress?: boolean;
  oneFpsPlay?: boolean;
}

const contrallerBarLayoutCss = css`
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 10;
  display: flex;
  width: 100%;
  height: 28px;
  background: rgb(29 33 53 / 80%);
  opacity: 1;
  align-items: center;
  justify-content: space-between;
  transition: opacity 0.3s;
  .anticon {
    cursor: pointer;
  }
`;

function ContrallerBar({ rightExtContents, rightMidExtContents, visibel, leftExtContents, leftMidExtContents, reload, hideTimeProgress, oneFpsPlay }: IContrallerBarProps) {
  return (
    <>
      <div className={contrallerBarLayoutCss} style={!visibel ? { opacity: 0 } : undefined}>
        <LeftBar oneFpsPlay={oneFpsPlay} hideTimeProgress={hideTimeProgress} reload={reload} leftMidExtContents={leftMidExtContents} leftExtContents={leftExtContents} />
        <RightBar rightExtContents={rightExtContents} rightMidExtContents={rightMidExtContents} />
      </div>
    </>
  );
}

export default ContrallerBar;
