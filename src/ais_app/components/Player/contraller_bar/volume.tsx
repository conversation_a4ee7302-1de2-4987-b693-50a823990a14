import { css } from "@emotion/css";
import { useUpdate, useUpdateEffect } from "ahooks";
import { Slide<PERSON>, Toolt<PERSON> } from "antd";
import React, { useState } from "react";
import type Api from "../api";
import IconFont from "../iconfont";

const classname = css`
  .ant-tooltip-inner {
    display: flex;
    width: 20px;
    height: 130px;
    padding: 12px 6px;
    text-align: center;
    border-radius: 3px;
    justify-content: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
    background-color: #FFF !important;
  }

  .ant-slider-vertical {
    margin: 0;
  }
  .ant-slider-rail{
    width: 6px;
    background-color: #EEE;
  }
  .ant-slider-track {
    background-color: var(--primary);
  }
  .ant-slider:hover .ant-slider-track {
    background-color: var(--primary);
  }
  .ant-slider-handle {
    // background-color: var(--primary);
    // border-color: var(--gray1);
    display: none;
  }
`;

function Volume({ api, style }: { api?: Api; style?: React.CSSProperties }) {
  const [val, setVal] = useState(Math.round(api?.getVolume() ?? 0 * 100));
  const update = useUpdate();
  const volume = api?.muted ? 0 : val;
  const onChange = (num: number) => {
    if (api?.muted) {
      api?.unmute();
    }
    setVal(num);
    update();
  };
  const toggleMuted = () => {
    if (!api) {
      return;
    }
    if (api.muted) {
      api.unmute();
      setVal(100);
    } else {
      api.mute();
      setVal(0);
    }
    update();
  };

  useUpdateEffect(() => api?.setVolume(val / 100), [val]);

  return (
    <Tooltip trigger='click' arrow={false} overlayClassName={classname} title={<Slider onChange={onChange} style={{ height: 100 }} vertical value={val} />}>
      <IconFont style={style} type={volume !== 0 ? "lm-player-volume-open" : "lm-player-volume-close"} onClick={toggleMuted} />
    </Tooltip>
  );
}

export default React.memo(Volume);
