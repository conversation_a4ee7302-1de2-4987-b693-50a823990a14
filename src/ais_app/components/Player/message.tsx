import { css, keyframes } from "@emotion/css";
import { useContext, useMemo, useRef, useState } from "react";
import { Context } from "./context";
import { useRegisterPlayerEvent, useVideoEvent } from "./event";
import EventName from "./event/eventName";
import IconFont from "./iconfont";

const lmPlayerMessageMaskCss = css`
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  display: flex;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background: transparent;
  opacity: 0;
  transition: all 0.5s;
  align-items: center;
  justify-content: center;
  flex-direction: column;
`;

const lmPlayerLoadingIconCss = css`
  position: relative;
  z-index: -1;
  font-size: 48px;
  visibility: hidden;
`;

const lmPlayerMaskLoadingAnimationCss = css`
  z-index: 1;
  background: rgb(0 0 0 / 60%);
  opacity: 1;
`;

const loadingAnimationFrames = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

const lmPlayerLoadingAnimationCss = css`
  z-index: 9;
  visibility: visible;
  animation: ${loadingAnimationFrames} 1s linear infinite;
`;

const lmPlayerLoadfailCss = css`
  z-index: 9;
  visibility: visible;
`;

const lmPlayerMessageCss = css`
  padding: 10px;
  font-size: 16px;
  color: #fff;
`;

function VideoMessage() {
  const { api } = useContext(Context);
  const [state, setState] = useState({ status: null, errorTimer: 1, loading: false });
  const timeRef = useRef<NodeJS.Timer>();

  const message = useMemo(() => {
    if (state.status === "fail") {
      console.warn(`视频错误，请手动刷新重试！`);
      return "请稍后重试";
    }
    if (state.status === "reload") {
      console.warn(`第${state.errorTimer}次重连`);
      return `正在刷新...`;
    }
    return "";
  }, [state.errorTimer, state.status]);

  const openLoading = () => {
    clearTimeout(timeRef.current as any);
    timeRef.current = setTimeout(() => setState((old) => ({ ...old, loading: true })), 200);
  };
  const closeLoading = () => {
    clearTimeout(timeRef.current as any);
    setState((old) => ({ ...old, loading: false }));
  };
  const errorReload = (timer?: number) => {
    clearTimeout(timeRef.current as any);
    setState(() => ({ status: "reload", errorTimer: timer, loading: true }) as any);
  };
  const reloadFail = () => setState((old) => ({ ...old, status: "fail" }) as any);
  const reloadSuccess = () => setState((old) => ({ ...old, status: null }));
  const reload = () => setState((old) => ({ ...old, status: "reload", loading: true }) as any);
  const playEnd = () => {
    clearTimeout(timeRef.current as any);
    setState((old) => ({ ...old, status: null, loading: false }));
    api?.pause();
  };
  useVideoEvent("loadstart", openLoading);
  useVideoEvent("loadeddata", closeLoading);
  useVideoEvent("canplay", closeLoading);
  useRegisterPlayerEvent(EventName.ERROR_RELOAD, errorReload);
  useRegisterPlayerEvent(EventName.RELOAD_FAIL, reloadFail);
  useRegisterPlayerEvent(EventName.RELOAD_SUCCESS, reloadSuccess);
  useRegisterPlayerEvent(EventName.RELOAD, reload);
  useRegisterPlayerEvent(EventName.HISTORY_PLAY_END, playEnd);
  useRegisterPlayerEvent(EventName.CLEAR_ERROR_TIMER, reloadSuccess);

  const { loading, status } = state;

  return (
    <div className={`${lmPlayerMessageMaskCss} ${loading || status === "fail" ? lmPlayerMaskLoadingAnimationCss : ""}`}>
      <IconFont
        type={status === "fail" ? "lm-player-YesorNo_No_Dark" : "lm-player-Loading"}
        className={`${lmPlayerLoadingIconCss} ${loading && status !== "fail" ? lmPlayerLoadingAnimationCss : status === "fail" ? lmPlayerLoadfailCss : ""}`}
      />
      <span className={lmPlayerMessageCss}>{message}</span>
    </div>
  );
}

export const NoSource = () => {
  return (
    <div className={`${lmPlayerMessageMaskCss} ${lmPlayerMaskLoadingAnimationCss}`}>
      <IconFont style={{ fontSize: 80 }} type="lm-player-PlaySource" title="请选择视频源"></IconFont>
    </div>
  );
};

export default VideoMessage;
