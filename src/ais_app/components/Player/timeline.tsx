import { css } from "@emotion/css";
import React, { useContext, useMemo, useState } from "react";
import { Context } from "./context";
import useBarStatus from "./contraller_bar/useBarStatus";
import { useVideoEvent } from "./event";

export const playerTimelineLayoutCss = css`
  position: absolute;
  bottom: 28px;
  left: 0;
  z-index: 9;
  display: flex;
  width: 100%;
  height: 12px;
  cursor: pointer;
  background: rgb(0 0 0 / 100%);
  opacity: 0.8;
  justify-content: center;
  align-items: center;
  transition: all 0.1s;
`;

export const hideTimeLineCss = css`
  bottom: 0;
  height: 4px;
`;

export const bufferLineCss = css`
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  max-width: 100%;
  pointer-events: none;
  background-color: rgb(103 103 103);
`;

export const currentLineCss = css`
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
  pointer-events: none;
  background-color: rgb(70 97 255 / 100%);
`;

export function useTimes() {
  const { api, isFpsPlay } = useContext(Context);
  const [state, setState] = useState({ currentTime: 0, buffered: 0 });
  const getCurrentTime = () => setState((old) => ({ ...old, currentTime: api?.getCurrentTime() ?? 0, buffered: api?.getSecondsLoaded() ?? 0 }));
  const getBuffered = () => setState((old) => ({ ...old, buffered: api?.getSecondsLoaded() ?? 0 }));
  const seekendPlay = () => !isFpsPlay && api?.play();

  useVideoEvent("timeupdate", getCurrentTime);
  useVideoEvent("progress", getBuffered);
  useVideoEvent("suspend", getBuffered);
  useVideoEvent("seeked", seekendPlay);

  return useMemo(() => [state.currentTime, state.buffered, api?.getDuration() ?? 0], [state.currentTime, state.buffered, api]);
}

function TimeLine() {
  const { api } = useContext(Context);
  const status = useBarStatus();
  const [currentTime, buffered, duration] = useTimes();

  const playPercent = useMemo(() => (currentTime / duration) * 100, [currentTime, duration]);
  const bufferedPercent = useMemo(() => (buffered / duration) * 100, [buffered, duration]);

  const seekWithLine = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const current = e.pageX - rect.left;
    const cTime = (current / rect.width) * (api?.getDuration() ?? 0);
    api?.seekTo(cTime);
  };

  return (
    <div className={`${playerTimelineLayoutCss} ${status === 0 ? hideTimeLineCss : ""}`} onClick={seekWithLine}>
      <div className={bufferLineCss} style={{ width: `${bufferedPercent}%` }}></div>
      <div className={currentLineCss} style={{ width: `${playPercent}%` }}></div>
    </div>
  );
}

export default TimeLine;
