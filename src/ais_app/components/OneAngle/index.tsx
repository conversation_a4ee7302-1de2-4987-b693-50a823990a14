import { InputN<PERSON>ber, Button, Space } from "antd";
import { OneAngleType } from "./index.d";
import "./index.less";
const OneAngle = ({ step = "1", value = [], onChange }: OneAngleType) => {
  const changestep1 = (value1: number) => {
    onChange([value1||0, isNaN(value[1]) ? 0: value[1]||0]);
  };
  const changestep2 = (value1: number) => {
    onChange([value[0]||0, isNaN(value1) ? 0 : value1||0]);
  };
  return (
    <div className="oneangle-threshold-value">
      <Space>
        <Button
          style={{ padding: "4px 10px" }}
          onClick={() => {
            const currNum = (value[0]||0) + 1;
            onChange([currNum, value[1]]);
          }}
        >
          +
        </Button>
        <InputNumber value={value[0]||0} keyboard={true} step={step} onStep={changestep1} />
        <Button
          style={{ padding: "4px 10px" }}
          onClick={() => {
            const currNum = (value[0]||0) - 1;
            onChange([currNum, value[1]]);
          }}
        >
          -
        </Button>
      </Space>
      <Space style={{ marginLeft: 24 }}>
        <Button
          style={{ padding: "4px 10px" }}
          onClick={() => {
            const currNum = (value[1]||0) + 1;
            onChange([value[0], currNum]);
          }}
        >
          +
        </Button>
        <InputNumber value={value[1]||0} keyboard={true} step={step} onStep={changestep2} />
        <Button
          style={{ padding: "4px 10px" }}
          onClick={() => {
            const currNum = (value[1]||0) - 1;
            onChange([value[0], currNum]);
          }}
        >
          -
        </Button>
      </Space>
    </div>
  );
};
export default OneAngle;
