import { IconFont } from '@cloud-app-dev/vidc';
import { DeviceType } from '@src/ais_app/core/device';
import { useMemo } from 'react';

interface IDeviceIconProps extends React.HTMLAttributes<HTMLSpanElement> {
  state: number;
  type: number;
}

export default function DeviceIcon({ state, type, style = {}, ...props }: IDeviceIconProps) {
  const item = useMemo(() => DeviceType.find((v) => v.code === type) ?? DeviceType[0], [type]);
  return <IconFont {...props as any} type={item.icon} style={{ ...style, color: state ? 'var(--primary)' : '#999' }} />;
}
