import { css } from '@emotion/css';
import { Select } from 'antd';
import { TimeModeLibs } from './utils';

interface ITimeModeProps {
  timeMode: number;
  onChange: (timeMode: number) => void;
}

const timeModeSelect = css`
  font-size: var(--fs-small);
  color: var(--gray1);

  .ant-select-selector {
    background-color: transparent !important;
    border-radius: var(--radius1) !important;
  }

  .anticon {
    color: var(--gray1);
  }
`;

const customSelectDropdown = css`
  font-size: var(--fs-small);

  .ant-select-item {
    font-size: var(--fs-small);
  }
`;

function TimeMode({ timeMode, onChange }: ITimeModeProps) {
  return (
    <Select value={timeMode} onChange={onChange} className={timeModeSelect} popupClassName={customSelectDropdown} placement="topLeft">
      {TimeModeLibs.map((item) => (
        <Select.Option value={item.name} key={item.name}>
          <span style={{ display: 'inline-block', width: 20, textAlign: 'center' }}>{item.name}</span>小时
        </Select.Option>
      ))}
    </Select>
  );
}

export default TimeMode;
