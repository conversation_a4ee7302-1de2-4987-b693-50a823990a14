import { css } from '@emotion/css';
import { useFullscreen, useInterval, useUpdate } from 'ahooks';
import React from 'react';
import Volume from '../Player/contraller_bar/volume';
import IconFont from '../Player/iconfont';
import { ExportPlayerType } from '../Player/player';
import { PlayModeType } from '../PlayerExt';
import ScreenSelect from './ScreenSelect';

export const playerToolsCss = css`
  position: relative;
  display: flex;
  height: 42px;
  padding: 0 10px;
  background-color: #17192A;
  align-items: center;
  justify-content: space-between;
  .player-tools-item {
    display: flex;
    width: 32px;
    height: 32px;
    margin: 0;
    font-size: 18px;
    color: var(--gray1);
    cursor: pointer;
    align-items: center;
    justify-content: center;

    .antion {
      font-size: 20px;
    }

    &:hover {
      background-color: var(--gray11);
      border-radius: var(--radius1);
    }
  }
`;

export const playerToolsLeftCss = css`
  display: grid;
  width: 35%;
  grid-template-columns: repeat(auto-fill, 32px);
  grid-gap: 10px;
`;

export const playerToolsRightCss = css`
  width: 35%;
  display: flex;
  justify-content: flex-end;
  margin-right: 0;
  margin-left: 4px;
  align-items: center;
`;

export const playerToolsMid = css`
  display: flex;
  align-items: center;
  justify-self: center;
  height: 100%;
`;

interface IToolsProps {
  containerRef: React.RefObject<HTMLDivElement>;
  updateState: (state: { screenNum?: number; mode?: PlayModeType }) => void;
  screenNum: number;
  mode?: PlayModeType;
  getPlayerItem: () => ExportPlayerType | undefined;
  fit?: string;
  toggleFit?: () => void;
  onClose?: () => void;
  onCloseAll?: () => void;
  snapshot?: (base64: string) => void;
  /**
   * 单窗口拓展工具条,左侧
   */
  oneWinExtTools?: JSX.Element;

  /**
   * 全局窗口工具条，右侧
   */
  allWinExtTools?: JSX.Element;

  hasPlugin?: boolean;
}

function LiveTools({ containerRef, oneWinExtTools, allWinExtTools, screenNum, mode = 1, fit, hasPlugin, ...props }: IToolsProps) {
  const [isFullscreen, { toggleFullscreen }] = useFullscreen(containerRef);
  const update = useUpdate();
  // 播放状态控制
  const playToggle = () => {
    const player = props.getPlayerItem();
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    player ? (player.video.paused ? player.api.play() : player.api.pause()) : undefined;
    update();
  };

  // 重连
  const reload = () => {
    const player = props.getPlayerItem();
    player?.api.reload();
  };

  const snapshotaction = () => {
    const player = props.getPlayerItem();
    const base64 = player?.api.snapshot();
    if (base64) {
      props.snapshot?.(base64);
    }
  };

  const player = props.getPlayerItem();

  /**
   * 定时获取play状态
   */
  useInterval(() => update(), 2000);

  return (
    <div className={playerToolsCss}>
      <div className={playerToolsLeftCss}>
        <span className="player-tools-item">
          <Volume api={player?.api} />
        </span>
        <span className="player-tools-item">
          <IconFont type="lm-player-xiangji1fill" title="截图" onClick={snapshotaction} />
        </span>
        {/* <span className="player-tools-item" onClick={reload}>
          <IconFont type="lm-player-Refresh_Main" title="重载" />
        </span> */}
        {hasPlugin && (
          <span className="player-tools-item" onClick={() => props.updateState({ mode: mode !== 2 ? 2 : 1 })}>
            <IconFont type="lm-player-S_Device_shezhi" className="icon-chajian" style={mode === 2 ? { color: 'var(--primary)' } : undefined} title={`切换${mode === 1 ? '插件' : '浏览器'}模式`} />
          </span>
        )}
        <div className="player-tools-item" onClick={props.onClose} style={{ marginRight: 6 }}>
          <IconFont type="lm-player-close" title="关闭" />
        </div>
        {oneWinExtTools}
      </div>
      {/* <div className={playerToolsMid}>
        <div className="player-tools-item" onClick={playToggle} style={{ marginLeft: 6 }}>
          {player && !player.video.paused ? <IconFont type="lm-player-Pause_Main" title="暂停" /> : <IconFont type="lm-player-bofang" title="播放" />}
        </div>
      </div> */}
      <div className={playerToolsRightCss}>
        {allWinExtTools}
        <span className="player-tools-item" onClick={props.onCloseAll}>
          <IconFont type="lm-player-quanbuguanbi" title="关闭所有" />
        </span>
        <ScreenSelect screenNum={screenNum} updateState={props.updateState} />
        <span className="player-tools-item" onClick={props.toggleFit}>
          {fit === 'fill' ? <IconFont type="lm-player-huamianshiying" title="自适应"></IconFont> : <IconFont type="lm-player-huamianshiying" title="填充"></IconFont>}
        </span>

        <span className="player-tools-item" onClick={toggleFullscreen} style={{ marginRight: 0 }}>
          {isFullscreen ? <IconFont type="lm-player-quanping" title="全屏"></IconFont> : <IconFont type="lm-player-quanping" title="全屏"></IconFont>}
        </span>
      </div>
    </div>
  );
}

export default LiveTools;
