import type React from 'react';
import type { ExportPlayerType, ISegmentType } from '../Player/player';
import type { PlayModeType } from '../PlayerExt';

export type RecordItem = {
  type?: 'flv' | 'hls' | 'native';
  date?: number; //视频窗口日期
  endDate?: number;
  cid?: string;
  segments?: ISegmentType[];
  url?: string;
  recordType?: 1 | 2; //1云录像 2前端录像
  mode?: PlayModeType;
  loading?: boolean; // 录像获取状态
};

export interface IRecordPlayerProps {
  /**
   * 播放对象
   */
  list?: RecordItem[];
  children?: JSX.Element;

  /**
   * 窗口索引变化，后续基于索引传入播放必要数据
   */
  onIndexChange?: (idx: number) => void;

  /**
   * 关闭单个窗口
   */
  onClose?: () => void;

  /**
   * 关闭所有窗口
   */
  onCloseAll?: () => void;
  /**
   * 录像下载回调
   */
  download?: (segments: ISegmentType[]) => void;

  /**
   * 截图
   */
  snapshot?: (base64: string) => void;

  /**
   * 窗口变化
   */
  screenChange?: (num: number) => void;

  /**
   * 默认窗口数量
   */
  defaultScreen?: 1 | 4 | 6 | 9 | 16;

  /**
   * 窗口数量，若有值数量将由外部控制
   */
  screenNum?: 1 | 4 | 6 | 9 | 16;

  /**
   * 默认选中窗口
   * @default 0
   */
  defaultSelectIndex?: number;
  /**
   * 单窗口拓展工具条,左侧
   */
  oneWinExtTools?: JSX.Element;

  /**
   * 全局窗口工具条，右侧
   */
  allWinExtTools?: JSX.Element;

  fpsDelay?: number;
  fps?: number;

  /**
   * 自定义合成前端录像
   */
  getLocalRecordUrl?: (options: { url: URL; begin: number; end: number; screenIndex: number }) => Promise<string>;

  /**
   * 插件下载地址
   */
  pluginDownloadUrl?: string;

  /**
   * 轴发生变化
   */
  onTimeLineChange?: (time: number) => void;

  /**
   * 用户控制seek频率
   */
  seekLoading?: boolean;

  /**
   * 控制播放倍数数组
   */
  multipleList?: number[];
}

export interface IRecordPlayerState {
  /**
   * 窗口数量
   */
  screenNum: number;

  /**
   * 当前窗口索引
   */
  selectIndex: number;

  /**
   * 插件OR浏览器
   */
  modes: { [key: string]: PlayModeType };

  /**
   * 时间轴开始时间
   */
  currentTimes: { [key: string]: number };

  /**
   *
   */
  begins: { [key: string]: number };

  /**
   * 需要seek的时间针对当前窗口，为0时忽略
   */
  seekTo?: { [key: string]: number };

  /**
   * 录像时间轴单页绘制时长单位（hour）
   */
  timeMode: number;
}

export type ScreenItemLivePlayerType = {
  url?: string;
  type?: 'flv' | 'hls' | 'native';
  mode?: PlayModeType;
  cid?: string;
};

export interface ILivePlayerProps {
  list?: ScreenItemLivePlayerType[];
  children?: JSX.Element;

  /**
   * 关闭单个窗口
   */
  onClose?: () => void;

  /**
   * 关闭所有窗口
   */
  onCloseAll?: () => void;
  /**
   * 窗口索引变化，后续基于索引传入播放必要数据
   */
  onIndexChange?: (idx: number) => void;

  /**
   * 截图
   */
  snapshot?: (base64: string) => void;

  /**
   * 窗口变化
   */
  screenChange?: (num: number) => void;

  /**
   * 默认窗口数量
   */
  defaultScreen?: 1 | 4 | 6 | 9 | 16;

  /**
   * 窗口数量，若有值由外部来完全控制
   */
  screenNum?: 1 | 4 | 6 | 9 | 16;

  /**
   * 默认选中窗口
   * @default 0
   */
  defaultSelectIndex?: number;

  /**
   * 单窗口拓展工具条,左侧
   */
  oneWinExtTools?: JSX.Element;

  /**
   * 全局窗口工具条，右侧
   */
  allWinExtTools?: JSX.Element;

  pluginDownloadUrl?: string;
}

export interface ILivePlayerState {
  screenNum: number;
  selectIndex: number;
  modes: { [key: string]: PlayModeType };
}

export const RecordPlayer: React.FC<IRecordPlayerProps>;

export type PlayItemMapType = { [key: string]: React.MutableRefObject<ExportPlayerType> };
