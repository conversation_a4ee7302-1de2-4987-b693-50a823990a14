import { css } from '@emotion/css';
import { useThrottleFn } from 'ahooks';
import moment from 'dayjs';
import { useRef } from 'react';
import { ISegmentType } from '../Player/player';
import useTimeSlider from './useTimeSlider';

interface ISegmentTimeLineProps {
  begin?: number;
  segments?: ISegmentType[];
  onTimeChange: (options: { currentTime?: number; begin?: number }) => void;
  timeMode: number;
  updateState: (state: { timeMode: number }) => void;
  currentTime?: number;
}

const recordTimeLine = css`
  position: relative;
  width: 100%;
  height: 46px;
`;

const canvas = css`
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  cursor: pointer;
  background-color: #030917;
`;

function SegmentTimeLine({ begin, segments, onTimeChange, timeMode, updateState, currentTime }: ISegmentTimeLineProps) {
  const ref = useRef<HTMLCanvasElement>(null);
  const { run } = useThrottleFn(onTimeChange, { wait: 300 });
  useTimeSlider(ref, {
    begin: begin ?? moment(currentTime).set('hours', 0).set('minutes', 0).set('seconds', 0).valueOf() - (timeMode / 2) * 3600 * 1000,
    currentTime: currentTime ?? begin ?? moment().set('hours', 0).set('minutes', 0).set('seconds', 0).valueOf(),
    onTimeChange: run,
    segments,
    hoursPer: timeMode,
    onHoursPerChange: (hour: number) => updateState({ timeMode: hour }),
  });

  return (
    <div className={recordTimeLine}>
      <canvas ref={ref} className={canvas}></canvas>
    </div>
  );
}

export default SegmentTimeLine;
