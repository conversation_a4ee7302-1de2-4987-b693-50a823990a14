import { useToggle } from 'ahooks';
import React, { useEffect, useMemo } from 'react';

export default function useVideoFit(containerRef: React.RefObject<HTMLDivElement>, deps: any[] = []) {
  const [fit, { toggle }] = useToggle('fill', 'contain');
  useEffect(() => {
    if (!containerRef.current) {
      return;
    }
    const videos = containerRef.current.querySelectorAll('video');
    Array.from(videos).forEach((item) => {
      item.style.objectFit = fit;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [...deps, fit]);

  return useMemo(() => ({ fit, toggleFit: toggle }), [fit, toggle]);
}
