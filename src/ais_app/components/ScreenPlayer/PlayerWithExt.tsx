import { css } from "@emotion/css";
import { useFullscreen, useLatest, useMemoizedFn, useUpdate } from "ahooks";
import moment from "dayjs";
import React, { useEffect, useRef, useState } from "react";
import FrontendPlayer from "../Player/frontend_player";
import type { ExportPlayerType, ISegmentType, ISinglePlayerProps } from "../Player/player";
import SegmentPlayer from "../Player/segment_player";
import LivePlayer from "../Player/single_player";
import type { PlayModeType } from "../PlayerExt";
import ExtModel from "../PlayerExt";

interface ILivePlayerWithExtProps extends ISinglePlayerProps {
  mode?: PlayModeType;
  style?: React.CSSProperties;
  onClick?: () => void;
  updatePlayer: (obj: React.MutableRefObject<ExportPlayerType>) => void;
  pluginDownloadUrl?: string;
}

const playerWithExtLayout = css`
  display: flex;

  .lm-player-ext-layout {
    border: 1px solid var(--gray12);
    background:#2E2E2E;
    box-sizing: border-box;
  }

  &.player-current-index {
    .lm-player-ext-layout {
      border: 1px solid #FAD619;
    }
  }
`;

export function LivePlayerWithExt({
  mode,
  url,
  type,
  style,
  className,
  updatePlayer,
  onClick,
  pluginDownloadUrl,
  ...props
}: ILivePlayerWithExtProps) {
  const forceUpdate = useUpdate();
  const ref = useRef<ExportPlayerType>();
  const update = () => {
    updatePlayer(ref as React.MutableRefObject<ExportPlayerType>);
    forceUpdate();
  };
  const [, { toggleFullscreen }] = useFullscreen(ref.current?.container);
  return (
    <div className={`${playerWithExtLayout} ${className} player-with-ext-layout`} style={style} onClick={onClick} onDoubleClick={toggleFullscreen}>
      <ExtModel url={url} mode={mode} pluginDownloadUrl={pluginDownloadUrl}>
        <LivePlayer
          {...props}
          type={type}
          url={url}
          isLive={true}
          hideContrallerBar
          onCanPlayerInit={update}
          ref={ref as React.MutableRefObject<ExportPlayerType>}
        />
      </ExtModel>
    </div>
  );
}

interface ISegmentPlayerWithExtProps extends Omit<ILivePlayerWithExtProps, "url"> {
  segments?: ISegmentType[];
  begin?: number;
  fpsDelay?: number;
  fps?: number;
  httpLoading: boolean;
  screenIndex?: number;
  playKey?: any;
}

export function SegmentPlayerWithExt({ begin, style, className, segments, mode, fpsDelay, fps, httpLoading, ...props }: ISegmentPlayerWithExtProps) {
  const forceUpdate = useUpdate();
  const ref = useRef<ExportPlayerType>();
  const update = () => {
    props.updatePlayer(ref as React.MutableRefObject<ExportPlayerType>);
    forceUpdate();
  };
  const [, { toggleFullscreen }] = useFullscreen(ref.current?.container);
  return (
    <div
      className={`${playerWithExtLayout} ${className} player-with-ext-layout`}
      style={style}
      onClick={props.onClick}
      onDoubleClick={toggleFullscreen}
    >
      <ExtModel mode={mode} loading={httpLoading} segments={segments}>
        <SegmentPlayer
          {...props}
          segments={segments}
          type="hls"
          hideContrallerBar
          forwordRef={ref as React.MutableRefObject<ExportPlayerType>}
          begin={begin}
          onCanPlayerInit={update}
          fps={fps}
          fpsDelay={fpsDelay}
          customTimeLine={<></>}
        />
      </ExtModel>
    </div>
  );
}

interface IFrontendPlayerWithExtProps extends Omit<ILivePlayerWithExtProps, "url"> {
  segments?: ISegmentType[];
  begin?: number;
  httpLoading: boolean;
  getLocalRecordUrl?: (options: { url: URL; begin: number; end: number; screenIndex: number }) => Promise<string>;
  screenIndex: number;
  playKey?: any;
}

export function FrontendPlayerWithExt({
  mode,
  style,
  className,
  segments,
  pluginDownloadUrl,
  screenIndex,
  httpLoading,
  ...props
}: IFrontendPlayerWithExtProps) {
  const [state, setState] = useState<{ begin: number; end: number; url?: string }>({ begin: 0, end: 0, url: undefined });
  const latestUrl = useLatest(state.url as string);

  useEffect(() => {
    if (!(Array.isArray(segments) && segments.length > 0)) {
      return undefined;
    }
    const index = segments.findIndex((v) => !!v.url);
    if (index === -1) {
      return undefined;
    }
    const [begin, end] = [segments[0].beginTime, segments[segments.length - 1].endTime];
    const videoUrl = new URL(segments[index]?.url ?? "");
    let url: string;
    if (props.getLocalRecordUrl) {
      props
        .getLocalRecordUrl({ url: videoUrl, begin: moment(begin).unix(), end: moment(end).unix(), screenIndex })
        .then((url) => setState({ begin, end, url }));
    } else {
      videoUrl.searchParams.set("begin", `${moment(begin).unix()}`);
      videoUrl.searchParams.set("end", `${moment(end).unix()}`);
      url = videoUrl.toString();
      setState({ begin, end, url });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [segments]);

  const seekTo = useMemoizedFn((time: number) => {
    if (!latestUrl.current) {
      return;
    }
    const end = moment().unix();
    const begin = moment(time).unix();
    const videoUrl = new URL(latestUrl.current);
    if (props.getLocalRecordUrl) {
      props.getLocalRecordUrl({ url: videoUrl, begin, end, screenIndex }).then((url) => setState((old) => ({ ...old, url })));
    } else {
      videoUrl.searchParams.set("begin", `${begin}`);
      videoUrl.searchParams.set("end", `${end}`);
      const url = videoUrl.toString();
      setState((old) => ({ ...old, url }));
    }
  });

  const forceUpdate = useUpdate();
  const ref = useRef<ExportPlayerType>();
  const update = useMemoizedFn(() => {
    props.updatePlayer(ref as React.MutableRefObject<ExportPlayerType>);
    forceUpdate();
  });

  const [, { toggleFullscreen }] = useFullscreen(ref.current?.container);

  return (
    <div
      className={`${playerWithExtLayout} ${className} player-with-ext-layout`}
      style={style}
      onClick={props.onClick}
      onDoubleClick={toggleFullscreen}
    >
      <ExtModel url={state.url} mode={mode} pluginDownloadUrl={pluginDownloadUrl} loading={httpLoading}>
        <FrontendPlayer
          onSeek={seekTo}
          url={state.url}
          hideContrallerBar
          forwordRef={ref as React.MutableRefObject<ExportPlayerType>}
          extActions={{ seekTo }}
          begin={state.begin}
          end={state.end}
          onCanPlayerInit={update}
          customTimeLine={<></>}
          type="flv"
        />
      </ExtModel>
    </div>
  );
}
