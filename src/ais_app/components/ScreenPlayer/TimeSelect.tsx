import { css } from '@emotion/css';
import { useUpdateEffect } from 'ahooks';
import { Button, InputNumber, Popover } from 'antd';
import moment from 'dayjs';
import { useMemo, useState } from 'react';

interface ITimeSelectProps {
  time?: number;
  onChange?: (time: number) => void;
}

const playerTimeSelect = css`
  display: flex;
  width: 190px;
  justify-content: space-around;
  align-items: center;
`;

const playerRecordTime = css`
  padding: 0 16px;
  margin: 0 20px;
  font-size: 16px;
  line-height: 42px;
  color: var(--gray1);
  text-align: center;
  background-color: var(--gray11);
`;

function TimeSelect({ time, onChange }: ITimeSelectProps) {
  const [values, setValues] = useState(() => {
    const m = time ? moment(time) : moment().set('hours', 0).set('minutes', 0).set('seconds', 0);
    return [m.hour(), m.minute(), m.second()] as (number | null)[];
  });
  const timeStr = useMemo(() => {
    const m = time ? moment(time) : moment().set('hours', 0).set('minutes', 0).set('seconds', 0);
    return m.format('HH:mm:ss');
  }, [time]);

  const onChangeTime = () => {
    const m = time ? moment(time) : moment().set('hours', 0).set('minutes', 0).set('seconds', 0);
    const newTime = m
      .set('hours', values[0] as number)
      .set('minutes', values[1] as number)
      .set('seconds', values[2] as number)
      .valueOf();
    onChange?.(newTime);
  };

  useUpdateEffect(() => {
    const m = time ? moment(time) : moment().set('hours', 0).set('minutes', 0).set('seconds', 0);
    setValues([m.hour(), m.minute(), m.second()]);
  }, [time]);

  const formatter = (value?: number) => (String(value).length === 1 ? `0${value}` : value + '');
  return (
    <Popover
      content={
        <div className={playerTimeSelect}>
          <InputNumber size="small" style={{ width: 36 }} formatter={formatter} controls={false} value={values[0]} onChange={(v) => setValues((old) => [v, old[1], old[2]])} />
          :
          <InputNumber size="small" style={{ width: 36 }} formatter={formatter} controls={false} value={values[1]} onChange={(v) => setValues((old) => [old[0], v, old[2]])} />
          :
          <InputNumber size="small" style={{ width: 36 }} formatter={formatter} controls={false} value={values[2]} onChange={(v) => setValues((old) => [old[0], old[1], v])} />
          <Button size="small" type="primary" onClick={onChangeTime}>
            确定
          </Button>
        </div>
      }
    >
      <div className={playerRecordTime}>{timeStr}</div>
    </Popover>
  );
}

export default TimeSelect;
