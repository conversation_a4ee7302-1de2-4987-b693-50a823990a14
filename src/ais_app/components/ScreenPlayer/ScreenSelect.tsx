import { css } from "@emotion/css";
import { Select } from "antd";
import { useMemo } from "react";
import CustomRenderSelect from "../CustomRenderSelect";
import IconFont from "../Player/iconfont";
import { ScreenType } from "./utils";
import './ScreenSelect.less'

const customSelectDropdown = css`
  font-size: var(--fs-small);
  border-radius: 3px;
  .ant-select-item {
    font-size: var(--fs-small);
  }
  .ant-select-item-option-selected{
    color: var(--primary) !important;
  }
`;

const selectCurrentScreenItem = css`
  display: flex;
  width: 32px;
  height: 32px;
  font-size: 18px;
  color: var(--gray1);
  text-align: center;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  position: relative;
  .anticon {
    font-size: 20px;
  }

  &:hover {
    background-color: var(--gray11);
    border-radius: var(--radius1);
  }
`;

interface IScreenSelect {
  screenNum: number;
  updateState: (arg: any) => void;
}

function ScreenSelect({ screenNum, updateState }: IScreenSelect) {
  const icon = useMemo(() => ScreenType.find((v) => v.name === screenNum)?.icon, [screenNum]);

  return (
    <CustomRenderSelect
      value={screenNum}
      popupClassName={customSelectDropdown}
      onChange={(v) => updateState({ screenNum: v })}
      placement="topLeft"
      customRender={() => (
        <span className={selectCurrentScreenItem}>
          <IconFont type={icon} />
        </span>
      )}
    >
      {ScreenType.map((item) => (
        <Select.Option className='Screen-op' key={`${item.name}`} value={item.name}>
          <span style={{ paddingRight: 4,  position: "relative" }}>
            <IconFont type={item.icon} />
          </span>
          {item.name}分屏
        </Select.Option>
      ))}
    </CustomRenderSelect>
  );
}

export default ScreenSelect;
