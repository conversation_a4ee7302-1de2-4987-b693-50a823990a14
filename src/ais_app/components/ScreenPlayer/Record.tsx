import { useMemoizedFn, useUpdateEffect } from "ahooks";
import { cloneDeep } from "lodash-es";
import React, { useEffect, useMemo, useRef, useState } from "react";
import DisableMark from "../DisableMark";
import type { ExportPlayerType } from "../Player/player";
import type { PlayModeType } from "../PlayerExt";
import { FrontendPlayerWithExt, SegmentPlayerWithExt } from "./PlayerWithExt";
import RecordTools from "./RecordTools";
import SegmentTimeLine from "./SegmentTimeLine";
import containerStyle, { playerLayoutRecord, splitScreenPlayerWrapper } from "./containerStyle";
import type { IRecordPlayerProps, IRecordPlayerState, PlayItemMapType, RecordItem } from "./interface";
import useVideoFit from "./useVideoFit";
import { ScreenType, mergeFill, sleep } from "./utils";

const defaultState: IRecordPlayerState = { screenNum: 4, selectIndex: 0, modes: {}, currentTimes: {}, seekTo: {}, timeMode: 24, begins: {} };

const getKey = (item: RecordItem) => `${item.cid}-${item.date}-${item.recordType}`;

/**
 * @desc 录像设计的时间全部需要到毫秒
 * @param param0
 * @returns
 */
function RecordPlayer({ list, children, oneWinExtTools, allWinExtTools, fpsDelay, fps, seekLoading, multipleList, ...props }: IRecordPlayerProps) {
  const [state, setState] = useState<IRecordPlayerState>({
    ...cloneDeep(defaultState),
    screenNum: props.defaultScreen ?? defaultState.screenNum,
  });
  // 若外部来控制screenNum 则由外部完全控制
  const screenNum = useMemo(() => props.screenNum ?? state.screenNum, [props.screenNum, state.screenNum]);

  const domRef = useRef<HTMLDivElement>(null);
  const screenType = useMemo(() => ScreenType.find((v) => v.name === screenNum), [screenNum]);

  // 填充数据扩充list.length
  const screenList = useMemo(() => mergeFill(screenNum, list, { recordType: 1 } as RecordItem), [screenNum, list]);

  const { fit, toggleFit } = useVideoFit(domRef, []);

  // 缓存所有player对象
  const playerRef = useRef<PlayItemMapType>({});

  // 获取选中player对象
  const getPlayerItem = () => {
    const item = screenList[state.selectIndex];
    return item ? playerRef.current?.[`${getKey(item)}`]?.current : undefined;
  };

  // 当前窗口信息
  const segmentItem = useMemo(() => screenList[state.selectIndex] || {}, [state.selectIndex, screenList]);

  /**
   * 时间轴开始时间
   */
  const timeBegin = useMemo(() => state.begins[`${getKey(segmentItem)}`], [segmentItem, state.begins, state.timeMode]);

  /**
   * 时间轴
   */
  const currentTime = useMemo(() => state.currentTimes[`${getKey(segmentItem)}`] ?? segmentItem.date, [state.currentTimes, segmentItem]);

  /**
   * @desc seek hook
   * 处理seek相关的包括索引和video current time
   */
  useEffect(() => {
    if (!state.seekTo) {
      return;
    }

    // 寻找需要seek的item
    const key = Object.keys(state.seekTo ?? {}).find((k) => state.seekTo?.[k] !== 0);
    if (!key) {
      return;
    }
    const seekTime = state.seekTo[key];
    const item = list?.find((v) => !!v && getKey(v) === key);
    const index = item?.segments?.findIndex((v) => seekTime >= v.beginTime && seekTime < v.endTime) ?? -1;
    if (index === -1) {
      return;
    }

    // list变化导致ref被销毁，这里设计了一个处理机制，1s内重试5次，尝试获取新的ref，播放器初始化很快正常情况下都会获取到，还未获取到那么丢弃
    let timer = 0;
    async function getPlay(mapkey: string): Promise<ExportPlayerType | undefined> {
      const playRef = playerRef.current?.[mapkey];
      if (playRef && playRef.current && playRef.current.api) {
        return playRef.current;
      } else {
        if (timer < 5) {
          timer++;
          await sleep(200);
          return sleep(200).then(() => getPlay(mapkey));
        }
        return Promise.reject("seek执行失败！");
      }
    }

    getPlay(key).then((play) => {
      if (play) {
        play.api.seekTo(seekTime);
      }
      setState((old) => ({ ...old, seekTo: { ...old.seekTo, [key]: 0 } }));
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.seekTo, list]);

  // 更新状态
  const updateState = (newState: { screenNum?: number; mode?: PlayModeType; timeMode?: number }) => {
    const obj = {} as IRecordPlayerState;
    if (newState.hasOwnProperty("screenNum")) {
      if (props.screenNum) {
        // 若外部控制，不需要update state
        props.screenChange?.(newState.screenNum as number);
      } else {
        obj.screenNum = newState.screenNum as number;
      }

      // 修正选中索引
      if (newState.screenNum && newState.screenNum < state.selectIndex) {
        obj.selectIndex = 0;
      }
    }
    if (newState.hasOwnProperty("mode")) {
      const item = screenList[state.selectIndex];
      const newModes = { ...state.modes };
      newModes[`${getKey(item)}`] = newState.mode as PlayModeType;
      obj.modes = newModes;
    }
    if (newState.hasOwnProperty("timeMode")) {
      obj.timeMode = newState.timeMode as number;
    }
    setState((old) => ({ ...old, ...obj }));
  };

  /**
   * @desc currentTime两种情况：
   * 1:time在片断内，修改cuurentTime，更新seekTo交给seek hook去处理；
   * 2:不在片断内，查询新的片段，更新mergeSegments和seekTo后交给seek hook处理
   *
   * @desc begin 处理时间轴开始绘制的刻度
   *
   */
  const onTimeChange = useMemoizedFn((options: { currentTime?: number; begin?: number }, outTimeline?: boolean) => {
    if (options.currentTime) {
      const time = options.currentTime;
      if (!Array.isArray(segmentItem.segments) || seekLoading) {
        return;
      }
      if (outTimeline && segmentItem.recordType === 1) {
        // 云录像 若点击了缺失的片段，直接忽略
        console.warn("当前录像片段缺失！");
        return;
      }

      if (time > Date.now()) {
        console.warn("查询时间超出正常范围！");
        return;
      }
      const index = segmentItem.segments.findIndex((v) => time >= v.beginTime && time < v.endTime);
      if (index === -1) {
        // 触发回调
        props.onTimeLineChange?.(time);
      }

      //更新time
      setState((old) => {
        const currentTimes = { ...old.currentTimes };
        const item = screenList[old.selectIndex];
        currentTimes[`${getKey(item)}`] = time;
        return { ...old, currentTimes, seekTo: { ...old.seekTo, [`${getKey(item)}`]: time } };
      });
    }
    if (options.begin) {
      //更新begin
      setState((old) => {
        const begins = { ...old.begins };
        const item = screenList[old.selectIndex];
        begins[`${getKey(item)}`] = options.begin as number;
        return { ...old, begins };
      });
    }
  });

  const updatePlayer = (player: React.MutableRefObject<ExportPlayerType>, index: number) => {
    const item = screenList[index];
    playerRef.current[`${getKey(item)}`] = player;
    setState((old) => ({ ...old }));
  };

  /**
   * 同步外部的selectIndex变化
   */
  useEffect(() => {
    if (typeof props.defaultSelectIndex !== "number") {
      return;
    }
    setState((old) => {
      if (old.selectIndex !== props.defaultSelectIndex) {
        return { ...old, selectIndex: props.defaultSelectIndex as number };
      }
      return old;
    });
  }, [props.defaultSelectIndex]);

  // index变化同步到父组件
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useUpdateEffect(() => {
    props.onIndexChange?.(state.selectIndex);
  }, [state.selectIndex]);
  /**
   * 通知screenNum变化
   */
  useUpdateEffect(() => {
    props.screenChange?.(state.screenNum);
  }, [state.screenNum]);

  // 单窗口关闭，清楚缓存key
  const onClose = useMemoizedFn(() => {
    setState((old) => {
      const item = screenList[old.selectIndex];
      if (item && item.cid && item.date) {
        const key = `${getKey(item)}`;
        const currentTimes = { ...old.currentTimes };
        const modes = { ...old.modes };
        delete currentTimes[key];
        delete modes[key];
        return { ...old, currentTimes, modes };
      } else {
        return old;
      }
    });
    props.onClose?.();
  });

  // 关闭所以，清楚缓存key
  const onCloseAll = useMemoizedFn(() => {
    setState((old) => ({ ...old, modes: {}, currentTimes: {} }));
    props.onCloseAll?.();
  });

  return (
    <div className={`${splitScreenPlayerWrapper} split-screen-player-wrapper-record`}>
      <div className={`${playerLayoutRecord} ${(containerStyle as any)[`container${screenType?.name ?? 1}`]}`} ref={domRef}>
        {screenList.map((item, index) => {
          const className =
            state.selectIndex === index ? `item${screenType?.name}-${index + 1}  player-current-index` : `item${screenType?.name}-${index + 1}`;
          const key = item.date && item.cid ? getKey(item) : `${index}`;
          const mode = state.modes[getKey(item)] ?? item.mode;
          const itemClick = () => setState((old) => ({ ...old, selectIndex: index }));
          return item.recordType === 1 ? (
            <SegmentPlayerWithExt
              {...item}
              segments={item?.segments || []}
              key={key}
              className={className}
              updatePlayer={(player) => updatePlayer(player, index)}
              onClick={itemClick}
              mode={mode}
              fps={fps}
              fpsDelay={fpsDelay}
              httpLoading={item.loading as boolean}
              screenIndex={index}
            />
          ) : (
            <FrontendPlayerWithExt
              {...item}
              className={className}
              segments={item?.segments || []}
              updatePlayer={(player) => updatePlayer(player, index)}
              onClick={itemClick}
              mode={mode}
              key={key}
              httpLoading={item.loading as boolean}
              getLocalRecordUrl={props.getLocalRecordUrl}
              pluginDownloadUrl={props.pluginDownloadUrl}
              screenIndex={index}
            />
          );
        })}
      </div>
      <div style={{ position: "relative", height: 88 }}>
        <DisableMark disabled={!segmentItem.cid} width="70%">
          <RecordTools
            time={currentTime}
            fit={fit}
            toggleFit={toggleFit}
            getPlayerItem={getPlayerItem}
            screenNum={screenNum}
            mode={state.modes[getKey(segmentItem)] || segmentItem.mode}
            containerRef={domRef}
            updateState={updateState}
            onTimeChange={onTimeChange}
            onClose={onClose}
            onCloseAll={onCloseAll}
            timeMode={state.timeMode}
            download={props.download ? () => props.download?.(segmentItem.segments ?? []) : undefined}
            snapshot={props.snapshot}
            oneWinExtTools={oneWinExtTools}
            allWinExtTools={allWinExtTools}
            hasPlugin={!!props.pluginDownloadUrl}
            multipleList={multipleList}
          />
        </DisableMark>
        <DisableMark disabled={!segmentItem.cid}>
          <SegmentTimeLine
            begin={timeBegin}
            currentTime={currentTime}
            updateState={updateState}
            segments={segmentItem.segments}
            onTimeChange={onTimeChange}
            timeMode={state.timeMode}
          />
        </DisableMark>
      </div>
      {children && React.cloneElement(children, { selectIndex: state.selectIndex })}
    </div>
  );
}

export default RecordPlayer;
