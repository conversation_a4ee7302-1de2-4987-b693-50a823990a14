import { css } from "@emotion/css";

export const splitScreenPlayerWrapper = css`
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  user-select: none;
  min-width: 600px;
  border-radius: 6px;
  overflow: hidden;
`;

export const playerLayout = css`
  height: calc(100% - 42px);
`;

export const playerLayoutRecord = css`
  height: calc(100% - 88px);
`;

const container1 = css`
  display: grid;
  grid-template-columns: repeat(1, 100%);
  grid-template-rows: repeat(1, 100%);
  grid-auto-flow: row dense; //排列顺序先行后列 紧密排列
  place-content: center;
`;

/* ------------------------4分屏---------------------- */
const container4 = css`
  display: grid;
  grid-template-columns: repeat(2, 50%);
  grid-template-rows: repeat(2, 50%);
  grid-auto-flow: row dense; //排列顺序先行后列 紧密排列
  place-content: center;
`;

/* ------------------------6分屏---------------------- */
const container6 = css`
  display: grid;
  grid-template-columns: repeat(3, 33.3333%);
  grid-template-rows: repeat(2, 50%);
  grid-auto-flow: row dense; //排列顺序先行后列 紧密排列
  place-content: center;
`;

/* -----------------------8分屏---------------------- */
const container8 = css`
  display: grid;
  grid-template-columns: repeat(24, 4.1667%);
  grid-template-rows: repeat(24, 4.1667%);
  grid-auto-flow: row dense; //排列顺序先行后列 紧密排列
  place-content: center;
  .item8-1 {
    grid-column: 1 / 11;
    grid-row: 1 / 13;
  }

  .item8-2 {
    grid-column: 11 / 18;
    grid-row: 1 / 9;
  }

  .item8-3 {
    grid-column: 18 / 25;
    grid-row: 1 / 9;
  }

  .item8-4 {
    grid-column: 11 / 18;
    grid-row: 9 / 17;
  }

  .item8-5 {
    grid-column: 18 / 25;
    grid-row: 9 / 17;
  }

  .item8-6 {
    grid-column: 1 / 11;
    grid-row: 13 / 25;
  }

  .item8-7 {
    grid-column: 11 / 18;
    grid-row: 17 / 25;
  }

  .item8-8 {
    grid-column: 18 / 25;
    grid-row: 17 / 25;
  }
`;

const container9 = css`
  display: grid;
  grid-template-columns: repeat(3, 33.3333%);
  grid-template-rows: repeat(3, 33.3333%);
  grid-auto-flow: row dense; //排列顺序先行后列 紧密排列
  place-content: center;
`;

/* ------------------------10分屏---------------------- */

const container10 = css`
  display: grid;
  grid-template-columns: repeat(4, 25%);
  grid-template-rows: repeat(4, 25%);
  grid-auto-flow: row dense; //排列顺序先行后列 紧密排列
  place-content: center;
  .item10-1 {
    grid-column: 1 / 3;
    grid-row: 1 / 3;
  }

  .item10-2 {
    grid-column: 3 / 5;
    grid-row: 1 / 3;
  }
`;

const container13 = css`
  display: grid;
  grid-template-columns: repeat(4, 25%);
  grid-template-rows: repeat(4, 25%);
  grid-auto-flow: row dense; //排列顺序先行后列 紧密排列
  place-content: center;
  .item13-1 {
    grid-column: 1 / 3;
    grid-row: 1 / 3;
  }
`;

const container16 = css`
  display: grid;
  grid-template-columns: repeat(4, 25%);
  grid-template-rows: repeat(4, 25%);
  grid-auto-flow: row dense; //排列顺序先行后列 紧密排列
  place-content: center;
`;

export default { container1, container4, container6, container8, container9, container10, container13, container16 };
