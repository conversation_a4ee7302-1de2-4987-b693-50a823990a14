/* eslint-disable @typescript-eslint/no-unused-expressions */
import { useFullscreen, useRafInterval, useToggle, useUpdate, useUpdateEffect } from 'ahooks';
import React from 'react';
import Volume from '../Player/contraller_bar/volume';
import IconFont from '../Player/iconfont';
import { ExportPlayerType } from '../Player/player';
import { PlayModeType } from '../PlayerExt';
import { playerToolsCss, playerToolsLeftCss, playerToolsMid, playerToolsRightCss } from './LiveTools';
import RatePick from './RatePick';
import ScreenSelect from './ScreenSelect';
import TimeSelect from './TimeSelect';
import Authority from "@src/components/Authority";


interface IToolsProps {
  containerRef: React.RefObject<HTMLDivElement>;
  screenNum: number;
  mode: PlayModeType;
  fit?: string;
  timeMode: number;
  time?: number;
  updateState: (state: { screenNum?: number; mode?: PlayModeType; timeMode?: number }) => void;
  onTimeChange?: (options: { currentTime?: number; begin?: number }) => void;
  toggleFit?: () => void;
  onClose?: () => void;
  onCloseAll?: () => void;
  getPlayerItem: () => ExportPlayerType | undefined;
  download?: () => void;
  snapshot?: (base64: string) => void;
  multipleList?: number[];
  /**
   * 单窗口拓展工具条,左侧
   */
  oneWinExtTools?: JSX.Element;

  /**
   * 全局窗口工具条，右侧
   */
  allWinExtTools?: JSX.Element;

  hasPlugin?: boolean;
}

function RecordTools({ containerRef, screenNum, mode = 1, fit, time, oneWinExtTools, allWinExtTools, hasPlugin, multipleList, ...props }: IToolsProps) {
  const [isFullscreen, { toggleFullscreen }] = useFullscreen(containerRef);
  const [isFpsPlay, { toggle }] = useToggle(false);
  const update = useUpdate();
  // 播放状态控制
  const playToggle = () => {
    const player = props.getPlayerItem();
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    player ? (player.video.paused ? player.api.play() : player.api.pause()) : undefined;
    update();
  };

  // 重连
  const reload = () => {
    const player = props.getPlayerItem();
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    player ? (player.reload ? player.reload() : player.api.reload()) : undefined;
  };

  const snapshotaction = () => {
    const player = props.getPlayerItem();
    const base64 = player?.api.snapshot();
    if (base64) {
      props.snapshot?.(base64);
    }
  };

  const ratechange = (value: number) => {
    const player = props.getPlayerItem();
    player?.api.setPlaybackRate(value);
    update();
  };

  const player = props.getPlayerItem();

  useUpdateEffect(() => {
    const player = props.getPlayerItem();
    if (player) {
      isFpsPlay ? player.api.openFpsPlay() : player.api.closeFpsPlay();
    }
  }, [isFpsPlay]);

  /**
   * 定时获取play状态
   */
  useRafInterval(() => update(), 2000);

  return (
    <div className={playerToolsCss}>
      <div className={playerToolsLeftCss}>
        <span className="player-tools-item">
          <Volume api={player?.api} />
        </span>
        <span className="player-tools-item">
          <IconFont type="lm-player-xiangji1fill" title="截图" onClick={snapshotaction} />
        </span>
        {/* <span className="player-tools-item" onClick={reload}>
          <IconFont type="lm-player-Refresh_Main" title="重载" />
        </span> */}
        <Authority code="30000321">
        <span className="player-tools-item" onClick={props.download}>
          <IconFont type="lm-player-xiazai" title="下载当前录像" />
        </span>
        </Authority>
        {hasPlugin && (
          <span className="player-tools-item" onClick={() => props.updateState({ mode: mode !== 2 ? 2 : 1 })}>
            <IconFont type="lm-player-S_Device_shezhi" style={mode === 2 ? { color: 'var(--primary)' } : undefined} className="icon-chajian" title={`切换${mode === 1 ? '插件' : '浏览器'}模式`} />
          </span>
        )}
        <span className="player-tools-item" onClick={toggle}>
          <IconFont type="lm-player-zhuzhenplay" style={isFpsPlay ? { color: 'var(--primary)' } : undefined} title="逐帧播放" />
        </span>
        <RatePick onChange={ratechange} multipleList={multipleList} value={player?.video?.playbackRate ?? 1} />
        {oneWinExtTools}
        <div style={{marginLeft:30}} className="player-tools-item" onClick={props.onClose}>
          <IconFont type="lm-player-close" title="停止" />
        </div>
      </div>
      <div className={playerToolsMid}>
        {/* <div className="player-tools-item" onClick={props.onClose}>
          <IconFont type="lm-player-tingzhi" title="停止" style={{ fontSize: 16 }} />
        </div> */}
        <TimeSelect time={time} onChange={(time) => props.onTimeChange?.({ currentTime: time })} />
        <div className="player-tools-item" onClick={playToggle}>
          {player && !player.video?.paused ? <IconFont type="lm-player-Pause_Main" title="暂停" style={{ fontSize: 22 }} /> : <IconFont type="lm-player-bofang" title="播放" />}
        </div>
      </div>
      <div className={playerToolsRightCss}>
        {allWinExtTools}
        <span className="player-tools-item" onClick={props.onCloseAll}>
          <IconFont type="lm-player-quanbuguanbi" title="关闭所有" />
        </span>

        <span className="player-tools-item" onClick={props.toggleFit}>
          {fit === 'fill' ? <IconFont type="lm-player-huamianshiying" title="自适应"></IconFont> : <IconFont type="lm-player-huamianshiying" title="填充"></IconFont>}
        </span>
        <ScreenSelect screenNum={screenNum} updateState={props.updateState} />
        <span className="player-tools-item" onClick={toggleFullscreen}>
          {isFullscreen ? <IconFont type="lm-player-quanping" title="全屏"></IconFont> : <IconFont type="lm-player-quanping" title="全屏"></IconFont>}
        </span>
      </div>
    </div>
  );
}

export default RecordTools;
