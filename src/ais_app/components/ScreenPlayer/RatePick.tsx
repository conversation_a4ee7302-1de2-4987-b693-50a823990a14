import { css } from '@emotion/css';
import { Select } from 'antd';
import CustomRenderSelect from '../CustomRenderSelect';
import './ScreenSelect.less'
interface IRatePick {
  onChange: (rate: number) => void;
  value: number;
  multipleList?: number[];
}

const selectCurrentRateItem = css`
  position: relative;
  top: 4px;
  display: inline-block;
  width: 64px;
  padding: 2px 0;
  font-size: 12px;
  color: var(--gray1);
  text-align: center;
  cursor: pointer;
  border: 1px solid var(--gray1);
  border-radius: 2px;
  margin-right: 10px;
`;

function RatePick({ onChange, value, multipleList = [8, 6, 4, 2, 1.5, 1, 0.5] }: IRatePick) {
  return (
    <CustomRenderSelect popupClassName='CustomRenderSelect' value={value} customRender={() => <span className={selectCurrentRateItem}>{`倍速 x${value}`}</span>} onChange={onChange} placement="topLeft" style={{ width: 80 }}>
      {multipleList?.map((v, i) => (
        <Select.Option  className='Screen-op' value={v} key={i}>
          x{v}倍
        </Select.Option>
      ))}
    </CustomRenderSelect>
  );
}

export default RatePick;
