import { ISegmentType } from '../Player/player';

export const ScreenType = [
  { name: 1, icon: 'lm-player-fenping1' },
  { name: 4, icon: 'lm-player-fenping4' },
  { name: 6, icon: 'lm-player-fenping6' },
  { name: 8, icon: 'lm-player-fenping8' },
  { name: 9, icon: 'lm-player-fenping9' },
  { name: 10, icon: 'lm-player-fenping10' },
  { name: 13, icon: 'lm-player-fenping13' },
  { name: 16, icon: 'lm-player-fenping16' },
];

export const TimeModeLibs = [{ name: 1 }, { name: 6 }, { name: 12 }, { name: 24 }];

export function mergeFill<T, S>(len: number, mergeArr: T[] = [], fillItem: S) {
  return new Array(len).fill(fillItem).map((v, i) => (mergeArr[i] ? mergeArr[i] : v)) as (T | S)[];
}

/**
 * unix时间戳
 * @param start
 * @param end
 * @param segments
 */
export const completionSegments = (start: number, end: number, segments: ISegmentType[]): ISegmentType[] => {
  const arr = [] as ISegmentType[];
  if (segments.length > 0 && start < +segments[0].beginTime) {
    arr.push({ beginTime: start, endTime: +segments[0].beginTime });
  }
  segments.reduce((prev, current, idx) => {
    if (arr.length === 0 && idx === 0) {
      prev.push(current);
    } else if (idx > 0 && segments[idx - 1].endTime !== current.beginTime) {
      prev.push({ beginTime: segments[idx - 1].endTime, endTime: current.beginTime }, current);
    } else {
      prev.push(current);
    }

    return prev;
  }, arr);

  if (end > +segments[segments.length - 1].endTime) {
    arr.push({ beginTime: segments[segments.length - 1].endTime, endTime: end });
  }
  return arr;
};

export function sleep(time: number) {
  // eslint-disable-next-line no-promise-executor-return
  return new Promise((reslove) => setTimeout(() => reslove(time), time));
}
