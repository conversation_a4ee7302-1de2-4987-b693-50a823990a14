import { useEventListener, useSize, useUpdateEffect } from "ahooks";
import moment from "dayjs";
import { useEffect, useMemo, useRef, useState } from "react";
import { nextTick } from "@cloud-app-dev/vidc";
import type { ISegmentType } from "../Player/player";

export interface ITimeSliderOptions {
  hoursPer: number;
  begin: number;
  segments?: TimeCellItem[];
  currentTime: number;
  onTimeChange: (options: { currentTime?: number; begin?: number }, outTimeline?: boolean) => void;
  onHoursPerChange: (hour: number) => void;
}

export type ITimeSliderState = {
  hoursPer: number;
  begin: number;
  currentTime: number;
  hover: { x?: number; time?: number };
};

export type TimeCellItem = ISegmentType;

const hours = [1, 6, 12, 24];
const currentColor = "#FF000A";
const normalLineColor = "#ffffff";
const hoverLineColor = "#319BFF";
const minPerStep = [1, 2, 5, 10, 15, 20, 30, 60, 120, 180, 240, 360, 720, 1440];
const minStepSize = 20;
const distance_between_gtitle = 80;

/**
 * 下一个刻度
 */
function ms_to_next_step(timestamp: number, step: number) {
  const remainder = timestamp % step;
  return remainder ? step - remainder : 0;
}

/**
 * 返回 2018-01-01 10:00:00 格式时间
 * @param {*} time
 */
function formartTimestemp(time: number) {
  return moment(time).format("YYYY-MM-DD HH:mm:ss");
}
/**
 * 返回时间轴上刻度的时间
 * @param {*} datetime new Date 格式
 */
function graduation_title(datetime: number) {
  const mom = moment(datetime);
  const h = mom.get("hours");
  const m = mom.get("minutes");
  const s = mom.get("seconds");
  if (h === 0 && m === 0 && s === 0) {
    return mom.format("YYYY.MM.DD");
  }
  return mom.format("HH:mm");
}

/**
 * 绘制线条
 */
function drawLine(ctx: CanvasRenderingContext2D, beginX: number, beginY: number, endX: number, endY: number, color: string, width: number) {
  ctx.beginPath();
  ctx.moveTo(beginX, beginY);
  ctx.lineTo(endX, endY);
  ctx.strokeStyle = color;
  ctx.lineWidth = width;
  ctx.stroke();
}

function drawText(
  ctx: CanvasRenderingContext2D,
  { fillStyle, font, text, x, y }: { fillStyle: string; font: string; text: string; x: number; y: number },
) {
  ctx.fillStyle = fillStyle;
  ctx.font = font;
  ctx.fillText(text, x, y);
}

function useTimeSlider(
  canvasRef: React.RefObject<HTMLCanvasElement>,
  { hoursPer, begin, currentTime, segments = [], onTimeChange, onHoursPerChange }: ITimeSliderOptions,
) {
  const [state, setState] = useState<ITimeSliderState>({ hoursPer, begin, currentTime, hover: { x: 0, time: undefined } });
  const extRef = useRef<{ mousedownTime?: number; mousedownX: number }>({ mousedownTime: undefined, mousedownX: 0 });
  const size = useSize(canvasRef);
  const canvas = canvasRef.current;
  const { width, height } = useMemo(() => (size ? size : { width: 0, height: 0 }), [size]);

  const ctx = canvas?.getContext("2d") as CanvasRenderingContext2D;

  const update = (params = {}) => setState((old) => ({ ...old, ...params }));

  const clearCanvas = () => ctx?.clearRect(0, 0, width, height);

  /**
   * 绘制覆盖时间刻度颜色，表示有录像的部分
   */
  const drawOverlay = () => {
    segments.forEach((item) => {
      const px_ms = width / (state.hoursPer * 60 * 60 * 1000); // px/ms
      const beginX = (item.beginTime - state.begin) * px_ms;
      const scale_width = (item.endTime - item.beginTime) * px_ms;
      if (ctx) {
        ctx.fillStyle = item.url ? item.style?.background || hoverLineColor : "#242C3D";
        ctx.fillRect(beginX, height - 10, scale_width + 1, height);
      }
    });
  };

  /**
   * 绘制刻度相关的
   */
  const drawScale = () => {
    const px_per_min = width / (state.hoursPer * 60); // px/min
    const px_per_ms = width / (state.hoursPer * 60 * 60 * 1000); // px/ms
    let px_per_step = minStepSize; // px/格 默认最小值20px
    let min_per_step = px_per_step / px_per_min; // min/格

    for (let i = 0; i < minPerStep.length; i++) {
      if (min_per_step <= minPerStep[i]) {
        //让每格时间在minutes_per_step规定的范围内
        min_per_step = minPerStep[i];
        px_per_step = px_per_min * min_per_step;
        break;
      }
    }

    let medium_step = 30;
    for (let i = 0; i < minPerStep.length; i++) {
      if (distance_between_gtitle / px_per_min <= minPerStep[i]) {
        medium_step = minPerStep[i];
        break;
      }
    }
    const num_steps = width / px_per_step; //总格数
    const ms_offset = ms_to_next_step(state.begin, min_per_step * 60 * 1000); //开始的偏移时间 ms
    const px_offset = ms_offset * px_per_ms; //开始的偏移距离 px
    const ms_per_step = px_per_step / px_per_ms; // ms/step

    let graduation_left, graduation_time, lineH;

    for (let i = 0; i < num_steps; i++) {
      graduation_left = px_offset + i * px_per_step; // 距离=开始的偏移距离+格数*px/格
      graduation_time = state.begin + ms_offset + i * ms_per_step; //时间=左侧开始时间+偏移时间+格数*ms/格
      const date = moment(graduation_time);
      if (date.get("hours") === 0 && date.get("minutes") === 0) {
        lineH = 10;
        const big_date = graduation_title(date.valueOf());
        const x = graduation_left - 30;
        drawText(ctx, { fillStyle: normalLineColor, font: "10px Arial", x, y: 30, text: big_date });
      } else if ((graduation_time / (60 * 1000)) % medium_step === 0) {
        lineH = 10;
        const middle_date = graduation_title(date.valueOf());
        const x = graduation_left - (middle_date.length > 5 ? 24 : 12);
        drawText(ctx, { fillStyle: normalLineColor, font: "10px Arial", x, y: 30, text: middle_date });
      } else {
        lineH = 5;
      }
      drawLine(ctx, graduation_left, height - lineH, graduation_left, height, normalLineColor, 2);
    }
  };

  /**
   * 绘制当前刻度
   * @returns
   */
  const drawCurrentLine = () => {
    const { currentTime, begin, hoursPer } = state;
    const time = currentTime;
    const range = [begin, currentTime + hoursPer * 3600 * 1000];
    if (time < range[0] || time > range[1]) {
      return;
    }
    const ms_current = time - begin;
    const px_per_ms = width / (hoursPer * 60 * 60 * 1000); // px/ms
    const left = px_per_ms * ms_current;
    drawText(ctx, { fillStyle: "#ffffff", font: "12px Arial", x: left - 64, y: 14, text: formartTimestemp(time) });
    drawLine(ctx, left, 20, left, height, currentColor, 2); //中间当前点线
  };

  /**
   * 绘制提示时间
   */
  const drawHoverLine = () => {
    drawText(ctx, {
      fillStyle: hoverLineColor,
      font: "12px Arial",
      x: (state.hover.x ?? 0) - 50,
      y: 12,
      text: formartTimestemp(state.hover.time ?? 0),
    });
    drawLine(ctx, state.hover.x ?? 0, 20, state.hover.x ?? 0, height, hoverLineColor, 1);
  };

  /**
   * 获取鼠标posx
   * @param {*} e
   */
  const get_cursor_x_position = (e: MouseEvent) => e.offsetX;

  /**
   * 更新画布大小
   */
  useUpdateEffect(() => {
    if (canvas) {
      canvas.width = width;
      canvas.height = height;
    }
  }, [canvas, height, width]);

  useEffect(() => {
    if (canvas) {
      clearCanvas();
      drawOverlay();
      drawScale();
      drawCurrentLine();
      if (state.hover.time) {
        drawHoverLine();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canvas, state.begin, state.currentTime, state.hoursPer, segments, state.hover, size]);

  useUpdateEffect(() => update({ begin }), [begin]);
  useUpdateEffect(() => update({ hoursPer }), [hoursPer]);
  useUpdateEffect(() => update({ currentTime }), [currentTime]);

  /**
   * 触发绘制范围单位（hour）
   */
  useEventListener(
    "mousewheel",
    (event) => {
      event.preventDefault();
      const delta = Math.max(-1, Math.min(1, event.wheelDelta || -event.detail));
      const middle_time = state.begin + (state.hoursPer * 3600 * 1000) / 2; //ms 记住当前中间的时间
      let new_hoursPer = state.hoursPer;
      const index = hours.indexOf(new_hoursPer);
      if (delta < 0) {
        // 缩小
        if (index === hours.length - 1) {
          new_hoursPer = hours[index];
        } else {
          new_hoursPer = hours[index + 1];
        }
      } else if (delta > 0) {
        // 放大
        if (index === 0) {
          new_hoursPer = hours[0];
        } else {
          new_hoursPer = hours[index - 1];
        }
      }
      const new_begin = middle_time - (state.hoursPer * 3600 * 1000) / 2; //new_begin = 当前中间的时间 - hoursPer/2
      setState((old) => ({ ...old, begin: new_begin, hoursPer: new_hoursPer }));
      onTimeChange?.({ begin: new_begin });
      nextTick(() => onHoursPerChange?.(new_hoursPer));
    },
    { target: canvasRef },
  );

  /**
   * 用于处理canvas移除hover效果
   */
  useEventListener("mouseout", () => setState((old) => ({ ...old, hover: { x: 0, time: undefined } })), { target: canvasRef });

  /**
   * hover效果绘制
   */
  useEventListener(
    "mousemove",
    (e) => {
      if (extRef.current.mousedownTime) {
        setState((old) => ({ ...old, hover: old.hover.time ? { x: 0, time: undefined } : old.hover }));
      } else {
        // 触发绘制hover tips
        const pos_x = get_cursor_x_position(e);
        const ms_per_px = (state.hoursPer * 3600 * 1000) / width; // ms/px
        const current_timestamp = state.begin + pos_x * ms_per_px;
        setState((old) => ({ ...old, hover: { x: pos_x, time: current_timestamp } }));
      }
    },
    { target: canvasRef },
  );

  /**
   * 标记点击、拖动的触发器
   */
  useEventListener(
    "mousedown",
    (e) => {
      extRef.current.mousedownTime = Date.now();
      extRef.current.mousedownX = get_cursor_x_position(e); //记住mousedown的位置
    },
    { target: canvasRef },
  );

  /**
   * 处理点击、结束拖动的处理
   */
  useEventListener(
    "mouseup",
    (e) => {
      if (!extRef.current.mousedownTime) {
        // 未在canvas上触发mousedown 过滤
        return;
      }
      const clickDelay = Date.now() - extRef.current.mousedownTime;
      if (clickDelay < 200 && e.target === canvas) {
        // click 事件
        const pos_x = get_cursor_x_position(e); //鼠标距离 px
        const ms_per_px = (state.hoursPer * 3600 * 1000) / width; // ms/px
        const new_current = state.begin + pos_x * ms_per_px;

        const segmentItem = segments.find((v) => new_current >= v.beginTime && new_current < v.endTime);
        // 当前片段没有录像的时候
        const outTimeline = segmentItem && !segmentItem.url;
        // const new_begin = new_current - (state.hoursPer * 60 * 60 * 1000) / 2;

        // setState((old) => ({ ...old, begin: new_begin, current: new_current }));
        setState((old) => ({ ...old, current: new_current }));
        nextTick(() => onTimeChange?.({ currentTime: new_current }, outTimeline));
      }
      // 清楚拖拽关联信息
      extRef.current.mousedownTime = undefined;
      extRef.current.mousedownX = 0;
    },
    { target: document },
  );

  const moveTimerRef = useRef<NodeJS.Timeout>();

  /**
   * 拖动处理
   */
  useEventListener(
    "mousemove",
    (e) => {
      if (!extRef.current.mousedownTime) {
        return;
      }
      clearTimeout(moveTimerRef.current);
      // 触发拖动
      const pos_x = get_cursor_x_position(e);
      const px_per_ms = width / (state.hoursPer * 60 * 60 * 1000); // px/ms
      const diff_x = pos_x - extRef.current.mousedownX;
      const new_begin = state.begin - Math.round(diff_x / px_per_ms);
      extRef.current.mousedownX = pos_x;
      update({ begin: new_begin });
      moveTimerRef.current = setTimeout(() => onTimeChange?.({ begin: new_begin }), 300);
    },
    { target: document },
  );
}

export default useTimeSlider;
