import { useMemoizedFn, useUpdateEffect } from 'ahooks';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import DisableMark from '../DisableMark';
import type { PlayModeType } from '../PlayerExt';
import Tools from './LiveTools';
import { LivePlayerWithExt } from './PlayerWithExt';
import containerStyle, { playerLayout, splitScreenPlayerWrapper } from './containerStyle';
import type { ILivePlayerProps, ILivePlayerState, PlayItemMapType, ScreenItemLivePlayerType } from './interface';
import useVideoFit from './useVideoFit';
import { ScreenType, mergeFill } from './utils';

function LivePlayer({ list, children, oneWinExtTools, allWinExtTools, ...props }: ILivePlayerProps) {
  const [state, setState] = useState<ILivePlayerState>({
    screenNum: props.defaultScreen ?? 4,
    selectIndex: props.defaultSelectIndex ?? 0,
    modes: {},
  });
  const domRef = useRef<HTMLDivElement>(null);

  const { fit, toggleFit } = useVideoFit(domRef, []);

  // 若外部来控制screenNum 则由外部完全控制
  const screenNum = useMemo(() => props.screenNum ?? state.screenNum, [props.screenNum, state.screenNum]);

  // 缓存所有player对象
  const playerRef = useRef<PlayItemMapType>({});
  const screenType = useMemo(() => ScreenType.find((v) => v.name === screenNum), [screenNum]);
  const screenList = useMemo(() => mergeFill(screenNum, list, {} as ScreenItemLivePlayerType), [list, screenNum]);

  // 获取选中player对象
  const getPlayerItem = () => {
    const item = list ? list[state.selectIndex] : ({} as ScreenItemLivePlayerType);
    const cid = item?.cid;
    return cid ? playerRef.current?.[cid]?.current : undefined;
  };

  // 更新状态
  const updateState = (newState: { screenNum?: number; mode?: PlayModeType }) => {
    const obj = {} as ILivePlayerState;
    if (Object.prototype.hasOwnProperty.call(newState, 'screenNum')) {
      if (props.screenNum) {
        // 若外部控制，不需要update state
        props.screenChange?.(newState.screenNum as number);
      } else {
        obj.screenNum = newState.screenNum as number;
      }

      // 修正选中索引
      if (newState.screenNum && newState.screenNum < state.selectIndex) {
        obj.selectIndex = 0;
      }
    }
    if (Object.prototype.hasOwnProperty.call(newState, 'mode')) {
      const item = list?.[state.selectIndex];
      const newModes = { ...state.modes };
      const cid = item?.cid;
      if (cid) {
        newModes[cid] = newState.mode as PlayModeType;
        obj.modes = newModes;
      }
    }
    setState((old) => ({ ...old, ...obj }));
  };

  // 当前窗口信息
  const segmentItem = useMemo(() => list?.[state.selectIndex] || {}, [state.selectIndex, list]);

  /**
   * 同步外部的selectIndex变化
   */
  useEffect(() => {
    if (typeof props.defaultSelectIndex !== 'number') {
      return;
    }
    setState((old) => {
      if (old.selectIndex !== props.defaultSelectIndex) {
        return { ...old, selectIndex: props.defaultSelectIndex as number };
      }
      return old;
    });
  }, [props.defaultSelectIndex]);

  // index变化同步到父组件
  useUpdateEffect(() => props.onIndexChange?.(state.selectIndex), [state.selectIndex]);

  /**
   * 通知screenNum变化
   */
  useUpdateEffect(() => props.screenChange?.(state.screenNum), [state.screenNum]);

  const onClose = useMemoizedFn(() => {
    setState((old) => {
      const item = screenList[old.selectIndex];
      if (item && item.cid) {
        const modes = { ...old.modes };
        delete modes[`${item.cid}`];
        return { ...old, modes };
      } else {
        return old;
      }
    });
    props.onClose?.();
  });

  const onCloseAll = useMemoizedFn(() => {
    setState((old) => ({ ...old, modes: {}, currentTimes: {} }));
    props.onCloseAll?.();
  });

  return (
    <div className={`${splitScreenPlayerWrapper}`}>
      <div className={`${playerLayout} ${(containerStyle as any)[`container${screenType?.name ?? 1}`]}`} ref={domRef}>
        {screenList.map((item, index) => {
          const className = state.selectIndex === index ? `item${screenType?.name}-${index + 1}  player-current-index` : `item${screenType?.name}-${index + 1}`;
          const itemClick = () => setState((old) => ({ ...old, selectIndex: index }));
          return (
            <LivePlayerWithExt
              {...item}
              mode={item.cid && state.modes[item.cid] ? state.modes[item.cid] : item.mode}
              key={item.url ?? `${index}`}
              updatePlayer={(player) => {
                if (item.cid) {
                  playerRef.current[item.cid] = player;
                }
              }}
              className={className}
              onClick={itemClick}
              pluginDownloadUrl={props.pluginDownloadUrl}
            />
          );
        })}
      </div>
      <DisableMark disabled={!segmentItem.url} width="70%">
        <Tools
          onClose={onClose}
          onCloseAll={onCloseAll}
          fit={fit}
          toggleFit={toggleFit}
          getPlayerItem={getPlayerItem}
          screenNum={screenNum}
          mode={segmentItem?.cid ? state.modes[segmentItem?.cid] : segmentItem?.mode}
          containerRef={domRef}
          updateState={updateState}
          snapshot={props.snapshot}
          oneWinExtTools={oneWinExtTools}
          allWinExtTools={allWinExtTools}
          hasPlugin={!!props.pluginDownloadUrl}
        />
        {children && React.cloneElement(children, { selectIndex: state.selectIndex })}
      </DisableMark>
    </div>
  );
}

LivePlayer.defaultProps = {
  list: [],
};

export default LivePlayer;
