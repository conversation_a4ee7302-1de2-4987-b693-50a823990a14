import { IconFont } from '@cloud-app-dev/vidc';
import { useSafeState, useToggle } from 'ahooks';
import { triggerPollingTask, updatePollingTaskState } from '@src/service/ais_app';
import OperatingBounced from '@src/components/OperatingBounced';
import { SocketEmitter } from '@cloud-app-dev/vidc';
import React, { ReactNode, useEffect } from 'react';
import './index.less';
import { message } from 'antd';

interface TestTabsLayoutType {
  tabsName: string[];
  children: ReactNode;
  setCurrentKey: (v: number) => void;
  currentKey: number;
  EditToggle?: () => void;
  isEdit?: boolean;
  isEmpty?: boolean;
}
export default function TestTabsLayout({ isEdit, isEmpty, tabsName, children, currentKey, setCurrentKey, EditToggle }: TestTabsLayoutType) {
  const [isShow, { toggle }] = useToggle(false);
  const [statusShow, { toggle: sToggle }] = useToggle(false);
  const [taskInfo, setTaskInfo] = useSafeState<any>({});
  useEffect(() => {
    SocketEmitter.on('changeTask', setTaskInfo);
    return ()=>SocketEmitter.on('changeTask', setTaskInfo);
  }, []);
  const runstop = () => {
    sToggle();
    const params = {
      id: taskInfo?.id,
      taskState: taskInfo?.taskState === 1 ? 0 : 1,
    };
    updatePollingTaskState(params).then((res) => {
      if (res.code === 0) {
        message.success(`${taskInfo?.taskState === 1 ? '暂停' : '启动'}成功`);
        SocketEmitter.emit('statusChange', taskInfo?.taskState === 1);
      } else {
        message.warning(res.message);
      }
    });
  };
  const handRun = () => {
    toggle();
    triggerPollingTask(taskInfo?.id).then((res) => {
      if (res.code === 0) {
        message.success('手动执行成功！');
      } else {
        message.warning(res.message);
      }
    });
  };
  return (
    <div className="TestTabsLayout">
      <div className="tabRow">
        {currentKey === 1 && !isEdit && !isEmpty && EditToggle && (
          <div className="loopbtns">
            <IconFont title="手动执行" type="icon-renwupeizhi_lunxunrenwu_shoudongzhihang" onClick={toggle} />
            {taskInfo?.taskState === 1 ? (
              <IconFont title="暂停" type="icon-renwupeizhi_lunxunrenwu_tingyong" onClick={sToggle} />
            ) : (
              <IconFont title="启动" type="icon-renwupeizhi_lunxunrenwu_qiyong" onClick={sToggle} />
            )}
            <IconFont title="编辑" type="icon-renwupeizhi_lunxunrenwu_bianji" onClick={EditToggle} />
          </div>
        )}
        <div className="tabcenter">
          {tabsName?.map((name, index) => (
            <React.Fragment key={name}>
              {index !== 0 && <div className="dliine"></div>}
              <div
                className={`tabItem ${currentKey === index && 'activeItem'}`}
                key={index}
                onClick={() => {
                  setCurrentKey(index);
                }}
              >
                {name}
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>
      <div className="contentRow">{children}</div>
      <OperatingBounced
        isShow={statusShow}
        title={`${taskInfo?.taskState === 1 ? '暂停' : '启动'}确认`}
        icon={
          taskInfo?.taskState !== 1 ? (
            <IconFont style={{ fontSize: '32px', color: '#1DBBFF' }} type="icon-danchuang_qidong" />
          ) : (
            <IconFont type="icon-danchuang_zanting" style={{ fontSize: '32px', color: '#1DBBFF' }} />
          )
        }
        onCancel={sToggle}
        onOk={runstop}
        content={`确认要${taskInfo?.taskState === 1 ? '暂停' : '启动'}轮巡任务吗？`}
      ></OperatingBounced>
      <OperatingBounced
        isShow={isShow}
        title="配置确认"
        icon={<IconFont style={{ fontSize: '32px', color: 'var(--primary-light)' }} type="icon-danchuang_shoudongzhihang"></IconFont>}
        onCancel={toggle}
        onOk={handRun}
        content="确认要手动执行一次轮巡任务吗？"
      ></OperatingBounced>
    </div>
  );
}
