.TestTabsLayout{
    width: 100%;
    height: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
    .tabRow{
        width: 100%;
        height: 40px;
        background: var(--content-bg);
        box-shadow: 0px 5px 14px rgba(0, 0, 0, 0.05);
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        .loopbtns{
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            .anticon{
                margin-left: 16px;
                color: var(--primary-light);
                cursor: pointer;
            }
        }
        .tabcenter{
            width: max-content;
            display: flex;
            align-items: center;
            .tabItem{
                width: max-content;
                height: 42px;
                padding: 6px 20px 0px;
                text-align: center;
                color: var(--gray1);
                font-size: var(--fs-large);
                position: relative;
                border-bottom: 2px solid transparent;
                cursor: pointer;
            }
            .dliine{
                width: 1px;
                height: 16px;
                background-color: var(--gray5);
                margin: 0 8px;
            }
            .activeItem{
                font-weight: 600;
                color: var(--secondary2);
                border-bottom: 2px solid var(--secondary2);;
            }
        }
    }
    .contentRow{
        flex: 1;
        width: 100%;
        margin-top: 16px;
        overflow: hidden;
        .VideoLayout{
            padding: 0;
        }
    }
}