import { queryAlgorithmType } from '@src/service/ais_app';
import { useRequest,useSafeState } from 'ahooks';
import { useEffect } from 'react';

import './index.less';
export default function Scenes({onChange}:{onChange:(v:number)=>void}) {
  const {data:sceneArr} = useRequest(queryAlgorithmType)
  const [currentType,setCurrentType] = useSafeState<number>(0)
  useEffect(()=>{
    onChange(currentType)
  },[currentType])  
  return (
    <div className="scenes">
      {sceneArr?.map((v, i) => (
        <div
          className={`sceneItem ${v?.algorithmType === currentType && 'currentItem'}`}
          key={i}
          onClick={() => {
            setCurrentType(v?.algorithmType);
          }}
        >
          {v?.algorithmTypeName}
        </div>
      ))}
    </div>
  );
}
