import { useSize } from 'ahooks';
import { BasicTarget, getTargetElement } from 'ahooks/lib/utils/domTarget';
import { useEffect } from 'react';

function useROI(target: BasicTarget<HTMLCanvasElement>, { saveArea }:{ saveArea: [number, number][][];}) {
  const size = useSize(target);
  useEffect(() => {
    const canvas:any = getTargetElement(target);
    canvas.width = size?.width;
    canvas.height = size?.height;
  }, [size?.height, size?.width]);

  const clearCanvas = () => {
    const canvas :any= getTargetElement(target);
    const ctx = canvas.getContext('2d');
    // 重置画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
  };

  const drawSavaArea = (area: [number, number][][]) => {
    const canvas:any = getTargetElement(target);
    const ctx:any = canvas.getContext('2d');
    // 绘制已完成的图形
    area.forEach((item) => {
      // 绘制坐标线
      const [startPos, ...pos] = item;
      ctx.beginPath();
      ctx.moveTo(...startPos);
      pos.forEach(([x, y]) => ctx.lineTo(x, y));
      ctx.lineTo(...startPos);
      ctx.strokeStyle = '#f32d37';
      ctx.lineWidth = 2;
      ctx.fillStyle = 'rgba(243,45,55,0.5)';
      ctx.fill();
      ctx.stroke();
    });
  };


  //绘制hook
  useEffect(() => {
    if(saveArea?.length !== 0){
        clearCanvas();
        drawSavaArea(saveArea);
    }
  }, [saveArea]);
}

export default useROI;
