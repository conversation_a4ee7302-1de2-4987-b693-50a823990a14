import React from "react";
import { IconFont } from "@cloud-app-dev/vidc";
import "./index.less";
import { useToggle } from "ahooks";

interface IPackUpProps {
  title: string | React.ReactNode;
  id?: string;
  children: React.ReactNode;
  className?: string;
  packUpIsTrue?: boolean;
  clearD?: any;
}

const PackUp = ({ title, id, children, packUpIsTrue, clearD = <></> }: IPackUpProps) => {
  const [state, { toggle }] = useToggle(true);
  return (
    <div className="packup" id={id}>
      <div
        className="titlebox"
        onClick={() => {
          if (packUpIsTrue) return;
          toggle();
        }}
      >
        <div className="icon">
          <IconFont
            type="icon-chevron-right"
            style={{ transform: state ? "rotate(90deg)" : "rotate(0deg)", fontSize: 16, color: "rgba(0, 0, 0, 0.4)" }}
          ></IconFont>
        </div>
        <div className="title">
          {title}
          {clearD}
        </div>
      </div>
      <div className="contentbox">{state && children}</div>
    </div>
  );
};
export default PackUp;
