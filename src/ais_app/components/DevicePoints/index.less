.device-points {
  color: rgba(0, 0, 0, 0.9);
  .device-points-PackUp {
    display: flex;
    align-items: center;
    .device-points-PackUp-title {
    }
    .device-points-PackUp-bnt {
      display: flex;
      padding: 2px 8px;
      justify-content: center;
      align-items: center;
      border-radius: 3px;
      border: 1px solid #165dff;
      background: #fff;
      color: #165dff;
      text-align: center;
      /* Body/Small */
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin-left: 8px;
      cursor: pointer;
    }
  }
  .device-content {
    .selected-list {
      border: 1px solid #dcdcdc;
      border-radius: var(--radius1);
      margin: 10px 0;
      max-height: 242px;
      flex: 1;
      min-height: 100px;
      position: relative;
      overflow: hidden;
      .show-content {
        max-height: 242px;
        padding: 8px 0 40px;
        overflow: auto;
        .item {
          cursor: pointer;
          .close {
            display: none;
            cursor: pointer;
            color: var(--primary-light);
            font-size: 12px;
          }
        }
        .item:hover {
          .close {
            display: block;
          }
        }
        & > div {
          line-height: 24px;
          padding: 0 10px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          & > span:first-child {
            flex: 1;
            overflow: hidden;
            display: flex;
            align-items: center;
          }
        }
      }
      .clear-box {
        background: var(--content-bg);
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 32px;
        border-top: 1px solid #dcdcdc;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.9);
        .clear-btn {
          cursor: pointer;
        }
      }
    }
  }
  .choseBtn {
    cursor: pointer;
    // width: 60px;
    // height: 28px;
    // text-align: center;
    // line-height: 27px;
    // color: var(--primary-light);
    // font-size: var(--fs-small);
    // font-weight: 500;
    // border: 1px solid var(--primary-light);
    // position: absolute;
    // right: 8px;
    // top: -43px;
    color: #165dff;
    /* Body/Medium */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    // border-radius: var(--radius1);
  }
}

.device-select-modal {
  .ant-modal-content {
    padding: 20px;
  }
}
