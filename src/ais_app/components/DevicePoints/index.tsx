import React, { forwardRef, useImperative<PERSON>andle, useEffect } from "react";
import { useMemoizedFn, useSafeState } from "ahooks";
import PackUp from "@src/ais_app/components/PackUp";
import DeviceSelect from "@src/ais_app/components/DeviceAssign";
import { Modal, Segmented, TreeSelect } from "antd";
import { tagDeviceTree, groupDeviceTree } from "@src/service/ais_app";
import { treeHelper } from "@cloud-app-dev/vidc";
import "./index.less";

const selectTreeOption = [
  { label: "设备分组", value: 1 },
  { label: "场所标签", value: 2 },
];
const DevicePoints = forwardRef(({ onChange, packUpIsTrue = false }: { onChange: (v: any[]) => void; packUpIsTrue?: boolean }, ref) => {
  const [state, setState] = useSafeState({ listVisible: false, points: [] as any[] });
  const [segmetedValue, setSegmentedValue] = useSafeState(1);
  const [treeData, setTreeData] = useSafeState([]);
  const [treeData1, setTreeData1] = useSafeState([]);
  const deleteDevice = (item: any) => {
    setState((old) => {
      const arr = old.points.filter((v) => v.cid !== item.cid);
      onChange(arr);
      return { ...old, points: [...arr] };
    });
  };
  const onOk = useMemoizedFn(() => {
    setState((old) => ({ ...old, listVisible: false }));
    onChange(state?.points);
  });
  useImperativeHandle(ref, () => ({ setStatePoint: setState, segmetedValue }));
  const [value, setValue] = useSafeState([]);
  const tProps = {
    treeData: segmetedValue === 1 ? treeData1 : treeData,
    value:state.points?.map((i) => i.cid),
    onChange: (e: any,l:any,extra:any) => {
      setValue(e);
      const currList = e.map((i:string,index:number)=>{
        return {
          cid:i,
          name:l[index]
        }
      })
      setState((old) => ({ ...old, points: currList }));
      onChange(currList);
    },
    treeCheckable: true,
    placeholder: "请选择设备",
    style: {
      width: "100%",
      marginTop: 8,
    },
  };
  const getFetchSelectTree = () => {
    const handler: any = (data: any[]) => {
      const arr = data.map((i: any) => {
        return {
          title: i.tagName || i.name,
          value: i.tagId || i.cid,
          key:Date.now().toString(36),
          children: i.deviceInfoList?.length ? handler(i.deviceInfoList) : undefined,
        };
      });
      return arr;
    };
    tagDeviceTree({}).then((res) => {
      if (res.code === 0) {
        const list = res.data?.list || [];
        setTreeData(handler(list));
      }
    });
  };
  const getFetchSelectTree1 = () => {
    const handler: any = (data: any[]) => {
      const arr = data.map((i: any) => {
        return {
          title: i.groupName || i.name,
          value: i.groupId || i.cid,
          key:Date.now().toString(36),
          children: i.deviceInfoList?.length ? handler(i.deviceInfoList) : i.children?.length? handler(i.children) :undefined,
        };
      });
      return arr;
    };
    groupDeviceTree({}).then((res) => {
      if (res.code === 0) {
        const list = res.data?.list || [];
        setTreeData1(handler(treeHelper.computTreeList(list, undefined, undefined, true)));
      }
    });
  };
  useEffect(() => {
    getFetchSelectTree();
    getFetchSelectTree1();
  }, []);
  return (
    <div className="device-points">
      <PackUp
        // packUpIsTrue={packUpIsTrue}
        title={
          <div className="device-points-PackUp">
            <div className="device-points-PackUp-title">设备</div>
            {/* <div className="device-points-PackUp-bnt" onClick={() => setState((old) => ({ ...old, listVisible: true }))}>
              选择设备
            </div> */}
          </div>
        }
        clearD={
          <div
            className="choseBtn"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setState((old) => ({ ...old, points: [] }));
              onChange([]);
              setValue([])
            }}
          >
            重置
          </div>
        }
      >
        <Segmented
          options={selectTreeOption}
          block
          value={segmetedValue}
          onChange={(e:any) => {
            setSegmentedValue(e);
            setValue([]);
            setState((old) => ({ ...old, points: [] }));
            onChange([]);
          }}
        />
        <TreeSelect {...tProps} />
        {/* <div className="device-content">
          {state.points.length > 0 && (
            <div className="selected-list">
              <div className="show-content">
                {state.points.map((v: any) => (
                  <div key={v.cid} className="item">
                    <span>
                      <DeviceIcon style={{ marginRight: 8 }} state={v.state} type={v.type} />
                      {v.name}
                    </span>
                    <span onClick={() => deleteDevice(v)} className="close">
                      <IconFont type="icon-guanbi"></IconFont>
                    </span>
                  </div>
                ))}
              </div>
              <div className="clear-box">
                <div
                  className="clear-btn"
                  onClick={() => {
                    setState((old) => ({ ...old, points: [] }));
                    onChange([]);
                  }}
                >
                  <IconFont type="icon-gaojingzhongxin_qingkong" /> 清空设备
                </div>
              </div>
            </div>
          )}
        </div> */}
      </PackUp>
      <Modal
        destroyOnClose
        open={state?.listVisible}
        width={850}
        title="设备选择"
        onOk={onOk}
        onCancel={() => setState((old) => ({ ...old, listVisible: false }))}
        className="device-select-modal"
      >
        <DeviceSelect
          selectItems={state.points}
          onSelectChange={(points: any, list: any) => {
            setState((old) => ({ ...old, points: list }));
          }}
        />
      </Modal>
    </div>
  );
});
export default DevicePoints;
