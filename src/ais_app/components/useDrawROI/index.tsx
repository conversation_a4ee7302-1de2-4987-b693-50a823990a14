import { useSimpleState } from '@cloud-app-dev/vidc';
import { useEventListener, useLatest, useMemoizedFn, usePrevious, useSize, useUpdateEffect } from 'ahooks';
import { BasicTarget, getTargetElement } from 'ahooks/lib/utils/domTarget';
import { useEffect, useMemo, useRef } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';

interface ROIOptions {
  row: number;
  rowItems: number;
  defaultAreas?: [number, number][][];
}
interface RIOStates {
  pos: [number, number][];
  tempPos: number[];
  saveArea: [number, number][][];
  isOpen: boolean;
}

function useDrawROI(target: BasicTarget<HTMLCanvasElement>, { row, rowItems, defaultAreas = [] }: ROIOptions) {
  const [state, updateState, setState] = useSimpleState<RIOStates>({ pos: [], tempPos: [], saveArea: defaultAreas ?? [], isOpen: false });
  const isOpenRef = useLatest(state.isOpen);
  const size = useSize(target);
  const oldSize = usePrevious(size);
  const updateAreas = useMemoizedFn((areas: [number, number][][]) => {
    updateState({ saveArea: areas });
  });

  useEffect(() => {
    if (!size) {
      return;
    }
    const canvas = getTargetElement(target) as HTMLCanvasElement;
    canvas.width = size?.width;
    canvas.height = size?.height;
    if (!oldSize) {
      return;
    }
    const scale = size.width / oldSize.width;
    setState((old) => {
      const area = old.saveArea.map((ar) => ar.map((v) => [v[0] * scale, v[1] * scale])) as [number, number][][];
      return { ...old, saveArea: [...area], pos: [], tempPos: [] };
    });
  }, [size?.height, size?.width]);

  useEffect(() => {
    if (defaultAreas.length > 0) {
      updateState({ saveArea: [...defaultAreas] });
    }
  }, [defaultAreas]);

  useEventListener(
    'click',
    (event: MouseEvent) => {
      if (!isOpenRef.current) {
        return;
      }
      const p_x = event.offsetX;
      const p_y = event.offsetY;
      setState((old) => {
        let pos = old.pos;

        // 去掉重复点位,dbclick导致重复触发
        if (!pos.find(([x, y]) => x === p_x && y === p_y)) {
          pos = [...pos, [p_x, p_y]];
        }
        return { ...old, pos };
      });
    },
    { target }
  );

  useEventListener(
    'dblclick',
    (event: MouseEvent) => {
      const p_x = event.offsetX;
      const p_y = event.offsetY;
      setState((old) => {
        let saveArea = old.saveArea;
        let pos = old.pos;
        let tempPos = old.tempPos;
        if (pos.length > 2) {
          // 去掉重复点位
          if (!pos.find(([x, y]) => x === p_x && y === p_y)) {
            pos = [...pos, [p_x, p_y]];
          }
          saveArea = [...saveArea, pos];
          pos = [];
          tempPos = [];
        }
        return { ...old, pos, saveArea, tempPos };
      });
    },
    { target }
  );

  useEventListener(
    'mousemove',
    (event: MouseEvent) => {
      if (!isOpenRef.current || state.pos.length === 0) {
        return;
      }
      const p_x = event.offsetX;
      const p_y = event.offsetY;
      setState((old) => ({ ...old, tempPos: [p_x, p_y] }));
    },
    { target }
  );

  const clearCanvas = () => {
    const canvas = getTargetElement(target) as any;
    const ctx = canvas.getContext('2d');
    // 重置画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
  };

  const drawSavaArea = (area: [number, number][][]) => {
    const canvas = getTargetElement(target) as any;
    const ctx = canvas.getContext('2d') as any;
    // 绘制已完成的图形
    area.forEach((item) => {
      // 绘制坐标线
      const [startPos, ...pos] = item;
      ctx.beginPath();
      ctx.moveTo(...startPos);
      pos.forEach(([x, y]) => ctx.lineTo(x, y));
      ctx.lineTo(...startPos);
      ctx.strokeStyle = '#f32d37';
      ctx.lineWidth = 2;
      ctx.fillStyle = 'rgba(243,45,55,0.5)';
      ctx.fill();
      ctx.stroke();
    });
  };

  const canvasRendering = (points: [number, number][], tempPos: number[]) => {
    if (points.length === 0) {
      return;
    }
    const canvas = getTargetElement(target) as any;
    const ctx = canvas.getContext('2d') as any;
    // 绘制坐标点
    points.forEach(([x, y]) => {
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fillStyle = 'red';
      ctx.fill();
    });

    // 绘制坐标线
    const [startPos, ...pos] = points;
    ctx.beginPath();
    ctx.moveTo(...startPos);
    pos.forEach(([x, y]) => ctx.lineTo(x, y));
    const [x, y] = tempPos;
    if (typeof x === 'number' && typeof y === 'number') {
      ctx.lineTo(x, y);
    }
    ctx.lineTo(...startPos);
    ctx.strokeStyle = '#f32d37';
    ctx.lineWidth = 2;
    ctx.fillStyle = 'rgba(243,45,55,0.5)';
    ctx.fill();
    ctx.stroke();
  };

  //绘制hook
  useEffect(() => {
    clearCanvas();
    drawSavaArea(state.saveArea);
    canvasRendering(state.pos, state.tempPos);
  }, [state.pos, state.saveArea, state.tempPos]);

  const status = useMemo(() => (state.isOpen ? 'open' : 'close'), [state.isOpen]);
  const open = () => setState((old) => ({ ...old, isOpen: true }));
  const close = () => setState((old) => ({ ...old, isOpen: false }));
  const clear = () => setState((old) => ({ ...old, pos: [], saveArea: [], tempPos: [] }));

  //快捷键处理
  useHotkeys('esc', () => setState((old) => ({ ...old, pos: [], tempPos: [] })));
  useHotkeys('enter', () => setState((old) => {
    if(!old?.pos.length) return ({ ...old, pos: [], tempPos: [] });
    return ({ ...old, saveArea: [...old.saveArea, old.pos], pos: [], tempPos: [] })
  }));

  const deleteArea = (area: number[][]) => {
    setState((old) => {
      const newArea = old.saveArea.filter((v) => v.join('-') !== area.join('-'));
      return { ...old, saveArea: [...newArea] };
    });
  };

  return { open, close, clear, status, areas: state.saveArea, deleteArea, updateAreas };
}

export default useDrawROI;
