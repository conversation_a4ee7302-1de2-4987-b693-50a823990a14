import { css } from '@emotion/css';
import React, { useEffect, useMemo, useState } from 'react';
import { ISegmentType } from '../Player/player';
import IconFont from '../Player/iconfont';

export type PlayModeType = 1 | 2; //模式1浏览器模式，2插件模式

export interface IPluginProps {
  url?: string;

  children: JSX.Element;
  /**
   * @description 模式1浏览器模式，2插件模式
   * @default 1
   */
  mode?: PlayModeType;

  /**
   * @description 插件下载地址
   */
  pluginDownloadUrl?: string;

  /**
   * @description 插件模式下传递的参数 ’&’开头
   * @default ''
   */
  pluginParams?: string;

  segments?: ISegmentType[];

  /**
   * 正在获取视频数据
   */
  loading?: boolean;
}

/**
 * 客户端插件模式，随机端口
 */
export const LOCAL_PORT = ['15080', '15081', '15082', '15083', '15084', '15085', '15086', '15087', '15088', '15089'];

/**
 * 随机获取端口号
 * @returns
 */
export function getLocalPort() {
  return LOCAL_PORT[Math.floor(Math.random() * LOCAL_PORT.length)];
}

const classname = css`
  display: flex;
  width: 100%;
  height: 100%;
  background-color: #000;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.55);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;

/**
 * @desc 转码插件模式下的url
 * @param url
 * @param params
 * @returns
 */
export function getLocalPlayPath(url: string, params: string = '') {
  const URL_TEMPLATE = `http://127.0.0.1:<port>/video/v1/transcoding?uri=<pull_uri>`;
  const port = getLocalPort();
  const pull_uri = window.btoa(url).replace(/=/g, '').replace(/\//, '_').replace('+', '-');
  return URL_TEMPLATE.replace('<port>', port).replace('<pull_uri>', pull_uri) + params;
}

export function usePlugin(mode?: PlayModeType, key?: any) {
  const [state, setState] = useState({ installed: false });

  useEffect(() => {
    if (mode !== 2) {
      return undefined;
    }
    const port = getLocalPort();
    const url = `http://127.0.0.1:${port}/video/v1/state`;

    const thenFn = (res: Response) => (res.status === 200 ? setState({ installed: true }) : undefined);
    fetch(url)
      .then(thenFn)
      .catch((e) => console.error('插件未安装', e));
  }, [mode, key]);

  return {
    needInstall: useMemo(() => mode === 2 && !state.installed, [state.installed, mode]),
  };
}

function NeedInstallPlugin({ pluginDownloadUrl, retry }: any) {
  return (
    <div className={classname}>
      <div style={{ marginTop: -60, textAlign: 'center' }}>
        <div>
          <IconFont type="lm-player-PlaySource" style={{ fontSize: 66 }} />
        </div>
        <div>
          <span>
            请
            <a target="_blank" href={pluginDownloadUrl} style={{ textDecoration: 'none', padding: '0 4px' }} download="PlayerPlugin.exe" rel="noopener noreferrer">
              下载
            </a>
            播放插件，
          </span>

          <span>
            若已安装请点击
            <a style={{ padding: '0 4px' }} onClick={retry}>
              重试
            </a>
          </span>
        </div>
      </div>
    </div>
  );
}

function Loading() {
  return (
    <div className={`${classname} lm-player-ext-layout `}>
      <div style={{ marginTop: -60, textAlign: 'center' }}>
        <div>
          <IconFont type="lm-player-PlaySource" style={{ fontSize: 66 }} />
        </div>
        <div>正在获取视频数据，请稍等...</div>
      </div>
    </div>
  );
}

function Empty() {
  return (
    <div className={`${classname} lm-player-ext-layout `}>
      <IconFont type="lm-player-PlaySource" style={{ fontSize: 66 }} />
      空闲状态
    </div>
  );
}

export function ExtModel({ url, children, mode, pluginDownloadUrl, pluginParams, loading, segments }: IPluginProps): JSX.Element {
  const [state, setState] = useState({ forceKey: Date.now() });
  const hasUrl = useMemo(() => !!url || (Array.isArray(segments) && segments.findIndex((v) => v.url) > -1), [segments, url]);
  const { needInstall } = usePlugin(mode, state.forceKey);

  const playUrl = useMemo(() => (mode === 2 && url ? getLocalPlayPath(url, pluginParams) : url), [url, mode]);
  const playSegments = useMemo(() => (mode === 2 && segments ? segments.map((v) => ({ ...v, url: v.url ? getLocalPlayPath(v.url, pluginParams) : undefined })) : segments), [segments, mode]);
  if (needInstall) {
    return <NeedInstallPlugin pluginDownloadUrl={pluginDownloadUrl} retry={() => setState((old) => ({ ...old, forceKey: Date.now() }))} />;
  }

  if (loading) {
    return <Loading />;
  }

  if (!hasUrl) {
    return <Empty />;
  }

  return <div className={`${classname} lm-player-ext-layout `}>{React.cloneElement(children, mode === 2 ? { url: playUrl, segments: playSegments } : {})}</div>;
}

export { ExtModel as default };
