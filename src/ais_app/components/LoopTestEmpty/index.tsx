import empty from '@src/assets/image/empty.png';
import { Button } from 'antd';
import './index.less';

export default function LoopTestEmpty({ onOK, haveDesc, canEdit, icon,mode='轮巡' }: {mode?:string, onOK?: () => void; haveDesc?: boolean; canEdit: boolean; icon?: any }) {
  return (
    <div className="LoopTestEmpty">
      <div className="image-box">
        <img src={icon ? icon : empty} alt="" />
      </div>
      <div className="title">{canEdit ? `还未配置${mode}任务！` : `还未配置${mode}任务，请联系管理员配置任务!`}</div>
      {haveDesc && (
        <div className="desc">
          <p className="title">轮巡任务是什么？</p>
          <p className="info">
            指定任务有效期和执行时间，批量选择设备和算法，快速完成任务创建。任务创建后，在有效期内，每天按照任务执行时间点
            定时完成对每一个设备每一种算法的分析工作。所以设备所有算法任务分析完毕算一轮，下一个执行时间点自动开启第二轮。
          </p>
        </div>
      )}
      {canEdit && (
        <Button type="primary" onClick={onOK}>
          立刻配置
        </Button>
      )}
    </div>
  );
}
