import React, { useMemo, useRef } from "react";
import GridList from "@cloud-app-dev/vidc/es/GridList";
import { GridListCell, GridListItemData } from "@cloud-app-dev/vidc/es/GridList/interface";
import useInfiniteScroll, { TData } from "../useInfiniteScroll";
import { message } from "antd";
import { useUpdateEffect } from "ahooks";

export interface IDynamicGridListProps<T> {
  itemHeight?: number;

  className?: string;

  /**
   * 重载条件
   */
  reloadDeps?: any[];

  /**
   * render列表
   * @param options
   */
  renderItem: (item: T, cell: GridListCell<T>) => React.ReactNode;

  loadPage(d?: TData<T>): Promise<TData<T>>;

  itemWidth: number;

  itemKey: string;

  getGridGap?: (...args: any) => number;

  isNoMore?: (data?: TData<T>) => boolean;

  threshold?: number;
}

function DynamicGridList<T>({
  renderItem,
  itemHeight,
  itemKey,
  itemWidth,
  reloadDeps,
  getGridGap,
  loadPage,
  isNoMore,
  threshold,
  ...props
}: IDynamicGridListProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);

  const { data, noMore } = useInfiniteScroll<T>(loadPage, { target: containerRef, threshold, isNoMore, reloadDeps });

  useUpdateEffect(() => {
    noMore && message.success("数据已全部加载完成！");
  }, [noMore]);

  function getItemData(data: any) {
    return { key: data[itemKey], height: itemHeight, width: itemWidth } as GridListItemData;
  }

  function getColumnCount(elementWidth: number, gridGap: number) {
    return Math.floor((elementWidth + gridGap) / (itemWidth + gridGap));
  }

  function getWindowMargin(windowHeight: number) {
    return Math.round(windowHeight * 1.5);
  }

  const dataList = useMemo<T[]>(() => (data ? data.list || [] : []) as T[], [data]);

  return (
    <GridList
      className={`cloudapp-dynamic-grid-list ${props.className}`}
      items={dataList}
      ref={containerRef}
      getItemData={getItemData}
      getColumnCount={getColumnCount}
      getWindowMargin={getWindowMargin}
      renderItem={renderItem as any}
      getGridGap={getGridGap}
    />
  );
}

DynamicGridList.defaultProps = { reloadDeps: [], className: "", getGridGap: () => 20, itemHeight: 300, itemWidth: 250, threshold: 50 } as any;

export default DynamicGridList;
