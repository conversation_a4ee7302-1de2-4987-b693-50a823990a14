import { css } from '@emotion/css';
import React from 'react';

interface IDisableMarkProps {
  children: React.ReactNode;
  disabled: boolean;
  width?: string | number;
}

const classname = css`
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  cursor: not-allowed;
`;

export default function DisableMark({ children, disabled, width }: IDisableMarkProps) {
  return (
    <div style={{ width: '100%', height: 'max-content', position: 'relative' }}>
      {disabled ? <div className={classname} style={{ width }}></div> : null}
      {children}
    </div>
  );
}
