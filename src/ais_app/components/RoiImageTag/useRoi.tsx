import { BasicTarget } from "ahooks/lib/utils/domTarget";
import { useEffect } from "react";
import { getModelCol } from "@src/utils";
function useROI(
  target: BasicTarget<HTMLCanvasElement>,
  { saveArea, modelList, modleBody, imgUrl, setImgSrc, setImgListSpin, ListName, visibleTitle, visibleBorder }: any,
) {
  const modelStyle = {
    detainee: {
      color: "#d4380d",
      background: "#fff2e8",
    },
    doctor: {
      color: "#0958d9",
      background: "#e6f4ff",
    },
    police: {
      color: "#389e0d",
      background: "#f6ffed",
    },
    others: {
      color: "#531dab",
      background: "#f9f0ff",
    },
  };
  const drawSavaArea = async (area: any) => {
    console.log(area,'area')
    const blobUrl = await fetch(imgUrl)
      .then((res) => res.blob())
      .then((res) => URL.createObjectURL(res));
    const image = saveArea.image;
    const canvas = document.createElement("canvas");
    const ctx: any = canvas.getContext("2d");
    const img = document.createElement("img");
    // img.setAttribute("crossOrigin", "anonymous");
    // img.src = imgUrl;
    img.src = blobUrl;
    new Promise((resolve, reject) => {
      img.onerror = function (err) {
        console.error(err);
        reject(new Error("Image load failed"));
      };
      img.onload = function () {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        // 绘制已完成的图形
        area.forEach((item: any) => {
          //   // 绘制坐标线
          ctx.beginPath();
          const { width = 0, height = 0 } = canvas || {};
          const bbox = item.bbox;
          // 计算缩放比例
          const scale_width = width / image.width;
          const scale_height = height / image.height;
          // 应用缩放比例计算新的位置
          const new_bbox = {
            h: bbox.h * scale_height,
            w: bbox.w * scale_width,
            x: bbox.x * scale_width,
            y: bbox.y * scale_height,
            confidence: item?.confidence ? item?.confidence?.toFixed(2) : "",
          };
          let currentModel = getModelCol(modelStyle, item.name) || {};
          if (!visibleBorder) {
            // 目标圈
            ctx.lineWidth = 4 * scale_width;
            ctx.strokeStyle = currentModel?.style?.color;
            ctx.fillStyle = "transparent";
            ctx.fillRect(new_bbox.x, new_bbox.y, new_bbox.w, new_bbox.h);
            ctx.strokeRect(new_bbox.x, new_bbox.y, new_bbox.w, new_bbox.h);
            ctx.fill();
            ctx.stroke();
          }

          // 标题
          if (!visibleTitle) {
            const title = ListName ? item.name_cn : currentModel.title;
            const title1 = `(${new_bbox.confidence})`;
            ctx.fillStyle = currentModel?.style?.background;
            ctx.strokeStyle = currentModel?.style?.color;
            // x,y,w,h
            const font = 34;
            const x = new_bbox.x;
            const y = new_bbox.y - (font + 10);
            const w = font * (title?.length + title1?.length / 2 + 0.4);
            const h = font + 10;
            const upX = width - x;
            const upW = w - upX;
            // ctx.globalAlpha = 0.7;
            ctx.fillRect(upX > w ? x : x - upW, y > h ? y : y + Math.abs(y), w + 10, h + 10);
            ctx.strokeRect(upX > w ? x : x - upW, y > h ? y : y + Math.abs(y), w + 10, h + 10);
            ctx.lineWidth = 4 * scale_width;
            ctx.fillStyle = currentModel?.style?.color;
            ctx.strokeStyle = currentModel?.style?.color;
            ctx.font = font * scale_width + "px Arial";
            ctx.strokeStyle = currentModel?.style?.color;
            ctx.textAlign = "conter";
            const title2X = x + font * (title?.length || 0) * scale_width;
            // 标签超出的X

            // x是算法起点
            // title2X是置信度起点
            if (new_bbox.confidence) {
              ctx.fillText(title, upX > w ? x : x - upW, y > h ? new_bbox.y - 3 : y + Math.abs(y) + h - 3);
              // 后缀置信度
              ctx.fillStyle = "rgba(0, 0, 0, 0.6)";
              ctx.fillText(`（${new_bbox.confidence}）`, upX > w ? title2X : title2X - upW, y > h ? new_bbox.y - 3 : y + Math.abs(y) + h - 3);
            }
          }
        });
        setTimeout(() => {
          canvas.remove();
        });
        resolve(canvas.toDataURL());
        setImgListSpin(false);
      };
    }).then((res) => {
      setImgSrc(res);
    });
  };

  useEffect(() => {
    if (imgUrl) {
      setImgSrc(imgUrl);
    } else {
      setImgSrc(null);
    }
  }, [imgUrl]);
  //绘制hook
  useEffect(() => {
    if (imgUrl && saveArea?.eventObjects?.length) {
      drawSavaArea(saveArea?.eventObjects);
    }
  }, [imgUrl, JSON.stringify(saveArea), modelList, visibleTitle, visibleBorder]);
  return {};
}

export default useROI;
