.roi-image-box {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 6px;
    .ant-spin-nested-loading,
    .ant-spin-container {
      overflow: hidden;
      height: 100%;
    }
    .image_top {
      width: 100%;
      height: 100%;
      // position: absolute;
      // left: 0;
      // top: 0;
      // z-index: 6;
      border: none;
      display: block;
    }
    .roi-image {
      width: 100%;
      height: 100%;
      position: relative;
      // visibility: hidden;
      canvas {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
      }
      img {
        width: 100%;
        height: 100%;
        border: none;
        display: block;
      }
      .roi_item_title {
        position: absolute;
        display: flex;
        padding: 0px 4px;
        justify-content: flex-end;
        align-items: center;
        border-radius: 3px;
        border: 1px solid #d54941;
        background: #fff0ed;
        margin-top: -23px;
        color: #d54941;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        .confidence {
          color: rgba(0, 0, 0, 0.6);
          font-family: "PingFang SC";
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          margin-left: 4px;
        }
      }
    }
  }
  