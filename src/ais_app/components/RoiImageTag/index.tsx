import "./index.less";
import { BasicTarget, getTargetElement } from "ahooks/lib/utils/domTarget";
import { useRef, useEffect, useState } from "react";
import useROI from "./useRoi";
import { Spin, Image } from "antd";
// import { modelListQuery } from "@src/service/ais_app";
import { useSize } from "ahooks";
import { LoadingOutlined } from "@ant-design/icons";
export default function RoiImage({
  imgUrl,
  rois,
  visibleTitle,
  ListName,
  modleBody,
  visibleBorder,
  imageView,
  setImageView,
}: {
  imgUrl: any;
  rois: any;
  visibleTitle?: any;
  ListName?: any;
  modleBody?: any;
  visibleBorder?: any;
  imageView?: any;
  setImageView?: any;
}) {
  const ref = useRef<HTMLCanvasElement>(null);
  const size: any = useSize(ref);
  const [modelList, setModelList] = useState<any[]>([]);
  const [imgListSpin, setImgListSpin] = useState(false);
  const [imgSrc, setImgSrc] = useState<any>(imgUrl);
  useROI(ref, { saveArea: rois, modleBody, imgUrl, setImgSrc, size, setImgListSpin, visibleTitle, ListName, visibleBorder });
  // useEffect(() => {
  //   modelListQuery({ limit: 1000, offset: 0, permission: false }).then((res) => {
  //     setModelList(res);
  //   });
  // }, []);
  return (
    <div className="roi-image-box">
      <Spin className="roi-image-box_spin" spinning={imgListSpin} indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}>
        {/* {imgSrc && <img src={imgSrc} className="image_top" />} */}
        <div className="roi-image" key={size?.width + size?.height + imgUrl}>
          <img src={imgSrc} alt="" className="image_ind" />
          <Image
            style={{ display: "none" }}
            preview={{
              src: imgSrc,
              destroyOnClose: true,
              visible: imageView,
              onVisibleChange: (visible) => {
                setImageView(visible);
              },
              forceRender: false,
            }}
            width={"100%"}
            src={imgSrc}
          />
        </div>
      </Spin>
    </div>
  );
}
