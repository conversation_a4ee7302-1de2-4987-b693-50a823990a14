import { useRequest, useUpdateEffect } from 'ahooks';
import { Checkbox, Tree, Input } from 'antd';
import {  useMemo, useState ,useEffect} from 'react';
import { getDevices, getGroups } from './utils';
import { IconFont, List, useSimpleState,DynamicList  } from '@cloud-app-dev/vidc';
import { getTreeIdWithKeyword, getTreeMap } from '@src/ais_app/pages/video/components/DeviceDir/utils';
import { ITreeItem, ListItem } from '@src/ais_app/pages/video/Context';
import { treeHelper } from '@cloud-app-dev/vidc';
import DeviceIcon from '../DeviceIcon';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { cloneDeep, size, xor,uniqBy } from 'lodash-es';
import './index.less';

interface IDeviceSelectProps {
  selectItems?: ListItem[];
  onSelectChange?: any;
}

function DeviceSelect({ selectItems=[], onSelectChange }: IDeviceSelectProps) {
  const [list1, setList1] = useState([])
  const [state, updateState, setState] = useSimpleState({
    expandedKeys: [] as string[],
    groupId: undefined as string | undefined,
    keyword: '' as string,
    selectItems: selectItems || ([] as any[]),
  });
  const { data: treeRes = [] } = useRequest<ITreeItem[], any>(() => getGroups());
  const treeDataTemp = useMemo(() => treeHelper.computTreeList(treeRes), [treeRes]);
  // 树数据处理，高亮关键字
  const treeData = useMemo(() => {
    const loop = (data: ITreeItem[]): ITreeItem[] =>
      data.map((item: any) => {
        const strName = item.groupName as string;
        const index = strName.indexOf(state.keyword);
        const beforeStr = strName.substring(0, index);
        const afterStr = strName.slice(index + state.keyword.length);
        const name =
          index > -1 ? (
            <span>
              {beforeStr}
              <span className="site-tree-search-value">{state.keyword}</span>
              {afterStr}
            </span>
          ) : (
            <span>{strName}</span>
          );
        const title = <div className="title">{name}</div>;

        if (item.children) {
          return { ...item, name: title, code: item.code, children: loop(item.children) };
        }

        return { ...item, name: title, code: item.code };
      });

    return loop(treeDataTemp);
  }, [state.keyword, treeDataTemp]);
useEffect(() => {
  if(selectItems.length===0){
    updateState({ selectItems: []})
  }
  if(selectItems[0]?.cid){
    updateState({ selectItems: selectItems.map((v: any) => v.cid)})
  }else{
    updateState({ selectItems: selectItems})
  }
},[])
  const onSelectItem = (items: any, type?: boolean) => {
    if (type) {
      updateState({ selectItems: items });
    } else {
      const item = items[0];
      setState((old) => {
        let arr = cloneDeep(old.selectItems);
        const inSelectIndex = arr.findIndex((v) => v === item.cid);
        if (inSelectIndex > -1) {
          arr.splice(inSelectIndex, 1);
        } else {
          arr.push(item.cid);
        }
        return { ...old, selectItems: [...arr] };
      });
    }
  };

const onDeleteAll = () => {
  updateState({ selectItems: [] });
}
  useUpdateEffect(() => onSelectChange?.(state.selectItems,list1.filter((item:any)=>state.selectItems.includes(item.cid))), [state.selectItems]);
  const treeMap = useMemo(() => getTreeMap(treeRes), [treeRes]);
  // 树搜索处理,匹配结果展开
  const onFilterChange = (name: string) => {
    const codes = getTreeIdWithKeyword(treeMap, name);
    updateState({ keyword: name, expandedKeys: codes });
  };
  return (
    <div className="device-select">
      <div className="org-tree-select select-content-part">
        <div className="tree-title">
          <span>
            {/* <IconFont type="icon-renwupeizhi_shebeirenwu_shebeimulu" style={{ fontSize: 20, position: 'relative', top: 2 }} /> */}
            <span>系统分组</span>
          </span>
          {/* <Checkbox>全选</Checkbox> */}
        </div>
        <div className="tree-content">
          <div className="search-box">
            <Input prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: '12px' }} />} placeholder="请输入分组名称" onChange={(v) => onFilterChange(v.target.value)} />
          </div>
          <Tree
            treeData={treeData}
            switcherIcon={<IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" style={{ transform: 'rotate(90deg)' }} />}
            fieldNames={{ title: 'name', key: 'id' }}
            onExpand={(expandedKeys) => updateState({ expandedKeys } as any)}
            onSelect={(treeKeys) => updateState({ groupId: treeKeys[0] as string })}
            expandedKeys={state.expandedKeys}
          />
        </div>
      </div>
      <DeviceList1 setList1={setList1} groupId={state.groupId} onCheck={onSelectItem} selectItems={state.selectItems} />
      <DeviceList2 onDeleteAll={onDeleteAll} list1={list1} list={state.selectItems} className="devive-assign-list" onDelete={onSelectItem} />
    </div>
  );
}

interface Device1ListProps {
  groupId?: string;
  className?: string;
  onCheck: (items: ListItem[],type?:boolean) => void;
  selectItems?: ListItem[];
  setList1?:any
}

function DeviceList1({ groupId, className = '', onCheck, selectItems = [],setList1 }: Device1ListProps) {
  const [state, updateState] = useSimpleState({ keywords: '', total: '',list:[] as any });
  /**
   * 获取设备和总数
   * @param page
   * @param pageSize
   * @param oldlist
   * @returns
   */
  const getLoadMoreList = (page: number, pageSize: number, oldlist: any): Promise<{ list: ListItem[]; total: number }> => {
    const options = {
      buzGroupId: groupId,
      offset: (page + 1) * pageSize,
      limit: pageSize,
      keywords: state.keywords,
      isPagination: true,
      sortField: 'name',
      sortOrder: 'desc',
    };

    return getDevices(options).then((result) => {
      updateState({list: [...oldlist, ...result.list]?.map(v=>v.cid),total: result.totalCount });
      setList1((oL:any)=>uniqBy([...oL, ...result.list], 'cid'))
      return { list: [...oldlist, ...result.list], total: result.totalCount, totalPage: result.totalPage, count: result.totalCount };
    });
  };

  const selectKeys = useMemo(() => {
    return selectItems
  }, [selectItems]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    onCheck(e.target.checked ? state?.list : [], true);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  useEffect(() => {
    if (size(selectKeys) === 0) {
      setIndeterminate(false);
      setCheckAll(false);
    } else {
      const flag =
        size(
          xor(
            selectKeys,
            state?.list?.map((v: any) => v),
          ),
        ) !== 0;
      setIndeterminate(flag);
      if (flag) {
        setCheckAll(false);
      } else {
        setCheckAll(true);
      }
    }
  }, [selectKeys, state?.list]);
  return (
    <div style={{margin:'0px 24px'}} className={`devive-list select-content-part ${className}`}>
      <div className="tree-title">
        <div>设备列表({state.total || 0})</div>
        <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
        全选
      </Checkbox>
      </div>
      <div className="list-from">
        <div className="search-box">
          <Input
            prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: '12px' }} />}
            value={state.keywords}
            placeholder="请输入设备名称"
            onChange={(v) => updateState({ keywords: v.target.value })}
          />
        </div>
      {/* <div className='all-check'>
      <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
        全选
      </Checkbox>
      </div> */}
      </div>
      <DynamicList
        loadPage={(d: any) => {
          // offset 0开始
          const page = d?.list.length ? Math.ceil(d.list.length / 20) - 1 : -1;
          return getLoadMoreList(page, 20, d ? d?.list : []);
        }}
        reloadDeps={[groupId, state.keywords]}
        renderItem={(item: { index: number; data: any }) => (
          <div className="device-list-item" key={item.data.cid}>
            <Checkbox checked={selectKeys.includes(item.data.cid)} onClick={() => onCheck([item.data])}>
              <DeviceIcon state={item.data.state} type={item.data.type} style={{ paddingRight: 4, fontSize: 18 }} />
              <span>{item.data.name}</span>
            </Checkbox>
          </div>
        )}
      />
    </div>
  );
}

interface DeviceList2Props {
  list?: ListItem[];
  className?: string;
  onDelete: (items: ListItem[]) => void;
  list1: any;
  onDeleteAll: () => void;
}

function DeviceList2({ list = [], className = '', onDelete,list1 ,onDeleteAll}: DeviceList2Props) {
  const [state, updateState] = useSimpleState({ keywords: '' });
  const filterList = useMemo(() => {
    const currentList = list1.filter((v:any) => list.includes(v.cid))
    return currentList?.filter((v:any) => v.name?.includes(state.keywords));
  }, [list, state.keywords,list1]);
  return (
    <div className={`devive-list select-content-part ${className}`}>
      <div className="tree-title">已选择设备({list?.length || 0}) <span onClick={onDeleteAll} className='clear'>清空</span></div>
      <div className="list-from">
        <div>
          <Input
            placeholder="请输入设备名称"
            prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: '12px' }} />}
            value={state.keywords}
            onChange={(v) => updateState({ keywords: v.target.value })}
          />
        </div>
      </div>
      <List
        list={filterList||[]}
        renderItem={(item:any) => (
          <div className="device-select-list-item" key={item.cid}>
            <DeviceIcon state={item.state} type={item.type} style={{ paddingRight: 4, fontSize: 18 }} />
            <span>{item.name}</span>
            <IconFont type="icon-guanbi" className="delete-icon" onClick={() => onDelete([item])} />
          </div>
        )}
      />
    </div>
  );
}

export default DeviceSelect;
