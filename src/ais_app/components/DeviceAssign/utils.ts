import { Service ,cache} from '@cloud-app-dev/vidc';

export type GroupItemType = {
  code: string;
  name: string;
};

export interface TreeItemType {
  code: string;
  name: string;
  offLine: number;
  onLine: number;
}

export function getHeader() {
  return { Authorization: cache.getCache('token', 'session') };
}

export async function fetchGroup(): Promise<GroupItemType[]> {
  const res = await Service.http({ url: '/api/device/group/v2', headers: getHeader() });
  return res.data;
}

export function getGroups() {
  return Service.http({
    headers: getHeader(),
    url: '/api/dvia-app-scene-server/device/deviceBuzGroup/deviceBuzGroupList/query',
  }).then((res) => res.data ?? []);
}

export async function fetchList(times: number[] = [], queryMatch: string, userId?: string, rightLikeCode?: string) {
  const res = await Service.http({
    url: '/api/device/v2/user',
    headers: getHeader(),
    params: { userId, rightLikeCode, fromCreateTime: times[0], toCreateTime: times[1], queryMatch },
  });
  return res.data;
}

export function getDevices(data: { groupId?: string; limit: number; offset: number; keywords?: string; type?: any; state?: any }) {
  return Service.http({
    headers: getHeader(),
    url: '/api/dvia-app-scene-server/device/systemDevice/deviceList/query',
    data,
    method: 'post',
  }).then((res) => res.data ?? []);
}
