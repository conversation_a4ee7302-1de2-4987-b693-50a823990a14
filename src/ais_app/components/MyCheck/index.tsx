import React, { useEffect, useState, forwardRef, useImperativeHandle } from "react";
import { Checkbox, Tooltip } from "antd";

import "./index.less";
const CheckboxGroup = Checkbox.Group;

interface MyCheckType {
  value?: any;
  onChange?: (list: any[]) => void;
  itemcheckgroup: any;
}

const MyCheck = forwardRef(({ value, onChange, itemcheckgroup = [] }: MyCheckType, ref) => {
  const [checkedList, setCheckedList] = useState<any[]>(value);
  const CheckonChange = (list: any[]) => setCheckedList(list);
  const onCheckClear = () => setCheckedList([]);
  useEffect(() => {
    onChange && onChange(checkedList);
  }, [checkedList]);
  useImperativeHandle(ref, () => ({ setCheckedList,onCheckClear }));
  return (
    <div className="my-check">
      {/* <div className="check-all" onClick={onCheckClear}>
        <span className="choseBtn">
        重置
        </span>
      </div> */}
      <CheckboxGroup onChange={CheckonChange} value={checkedList}>
        {itemcheckgroup.map((item: any) => (
          <TitleTip title={item.value} key={item.value}>
            <div>
              <Checkbox value={item.id}>{item.value}</Checkbox>
            </div>
          </TitleTip>
        ))}
      </CheckboxGroup>
    </div>
  );
});

function TitleTip({ title, children }: { title?: string; children?: JSX.Element }) {
  if (title && title.length > 8) {
    return <Tooltip title={title}>{children}</Tooltip>;
  } else {
    return <>{children}</>;
  }
}

export default MyCheck;
