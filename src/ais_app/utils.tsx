import { cloneDeep, isNil } from 'lodash-es';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';

export const mergeFill = <T, S>(len: number, mergeArr: T[], fillItem: S) => {
  const arr = [];
  for (let i = 0; i < len; i++) {
    arr.push(typeof fillItem === 'object' ? cloneDeep(fillItem) : fillItem);
  }
  const newarr = arr.map((v, i) => (mergeArr[i] ? mergeArr[i] : v)) as (T | S)[];

  return newarr;
};

export function arrayFill(length: number, fill: any) {
  const arr = [];
  for (let i = 0; i < length; i++) {
    arr.push(typeof fill === 'object' ? cloneDeep(fill) : fill);
  }
  return arr;
}
export const base64ToBlob = (base64Url: any) => {
  const bytes = window.atob(base64Url.split(',')[1]); // 去掉url的头，并转换为byte
  //处理异常,将ascii码小于0的转换为大于0
  const ab = new ArrayBuffer(bytes.length);
  const ia = new Uint8Array(ab);
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  return new Blob([ab], { type: 'image/png' });
};
export const tagAToDownload = ({ url, title = '', target = '_blank' }: any) => {
  const tagA = document.createElement('a');
  tagA.setAttribute('href', url);
  tagA.setAttribute('download', title);
  tagA.setAttribute('target', target);
  document.body.appendChild(tagA);
  tagA.click();
  document.body.removeChild(tagA);
};
export const downloadLocalImage = (base64Url: any, title: any) => {
  const path = URL.createObjectURL(base64ToBlob(base64Url));
  tagAToDownload({
    url: path,
    title,
  });
  setTimeout(() => {
    URL.revokeObjectURL(path);
  }, 10000);
};

export const TransDate = (params: any, key: string, value: any, havehms?: boolean) => {
  const Rparams = cloneDeep(params);
  delete Rparams[key];
  if (!isNil(value)) {
    if (havehms) {
      Rparams['startTime'] = value[0].valueOf();
      Rparams['endTime'] = value[1].valueOf();
    } else {
      Rparams['startTime'] = value[0].set({ hour: 0, minute: 0, second: 0 }).valueOf();
      Rparams['endTime'] = value[1].set({ hour: 23, minute: 59, second: 59 }).valueOf();
    }
  }
  return Rparams;
};

export const TransNumber = (v: number | string | undefined): any => {
  if (!isNil(v)) {
    const num = String(v);
    const tnum = `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`);
    if (num.length >= 8) {
      const fnum = (+v / 10000000).toFixed(2);
      return (
        <Tooltip title={tnum}>
          <object>{fnum}千万</object>
        </Tooltip>
      );
    }
    return tnum;
  } else {
    return '-';
  }
};
//时间戳转格式
export const DateToFormat = (date: number | string | undefined, format: string) => {
  if (!date) {
    return '-';
  }
  return dayjs(+date).format(format);
};
export const getAlgorithmNameById = (sceneArr: any[], id: number) => {
  return sceneArr?.find((v) => v.id === id)?.algorithmNoteName || '-';
};

export const statusArr = [
  {
    value: '分析中',
    id: 'RUNNING',
  },
  {
    value: '排队中',
    id: 'QUEUEING',
  },
  {
    value: '暂停',
    id: 'STOPPED',
  },
  {
    value: '失败',
    id: 'FAILED',
  },
  {
    value: '其他',
    id: 'OTHER',
  },
];
export const TransStatus = (text: string) => {
  return statusArr?.find((v) => v?.id === text)?.value || '-';
};
export const TransMins = (value: number, mode = true) => {
  let secondTime = value; // 秒
  let minuteTime = 0; // 分
  let hourTime = 0; // 小时
  if (secondTime > 60) {
    minuteTime = (secondTime / 60) >>> 0;
    secondTime = secondTime % 60;
    if (minuteTime > 60) {
      hourTime = (minuteTime / 60) >>> 0;
      minuteTime = minuteTime % 60;
    }
  }
  return mode
    ? `${hourTime ? hourTime + '小时' : ''}${minuteTime ? minuteTime + '分钟' : ''}${secondTime + '秒'}`
    : `${hourTime ? hourTime + 'h' : ''}${minuteTime ? minuteTime + 'm' : ''}${secondTime + 's'}`;
};

export const DeviceType = [
  { code: 70041, name: '普通摄像机', icon: 'icon-renwupeizhi_lunxunrenwu_shexiangji2' },
  { code: 70042, name: '球机', icon: 'icon-renwupeizhi_lunxunrenwu_shexiangji3' },
  { code: 70043, name: '抓拍机', icon: 'icon-renwupeizhi_lunxunrenwu_shexiangji1' },
];

export const getDeviceNameByCode = (code: number | string) => {
  return DeviceType.find((v) => v.code == code)?.name;
};
