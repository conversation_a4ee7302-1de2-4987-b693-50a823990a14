import { useRef, useEffect, useState } from "react";
import { Drawer, Input, Checkbox, Button, Switch, message } from "antd";
import Service from "@src/service/system";

import "./index.less";

export default function DeviceDrawer({ open, setOpen, onSub, dataInfo, setDataInfo, getTableData }: any) {
    const inputRef: any = useRef(null)
    const [data, setData]: any = useState([])
    const [keywords, setKeywords] = useState('')
    const [selectId, setSelectId] = useState(null)

    useEffect(()=> {
        if (dataInfo.orgId) {
            setSelectId(dataInfo.orgId)
        }
    }, [dataInfo])

    useEffect(() => {
        getOrgTree()
    }, [keywords])

    const getOrgTree = () => {
        Service.device.orgListTree({ keywords }).then((res) => {
            setData(res.data.list)
        })
    }

    const onCancel = () => {
        setDataInfo({})
        setOpen(false)
    }

    const onChange = (index: any) => {
        setSelectId(data[index].id)
    }

    const handleSubmit = () => {
        if (selectId) {
            Service.device.updateDeviceOrgId({ cid: dataInfo.cid, orgId: selectId }).then((res) => {
                setOpen(false)
                getTableData()
                message.success('局点标定成功', 1.5)
            }).catch((err) => {
                message.warning(err.data.message)
            })
        } else {
            message.info('请选择局点')
        }
    }

    return (
        <>
            <Drawer
                width={350}
                title={"局点标定"}
                placement={'right'}
                closable={false}
                onClose={onCancel}
                open={open}
                keyboard={false}
                footer={<div className="editGantryCraneDrawer-footer">
                    <div onClick={() => onCancel()} className="editGantryCraneDrawer-clear">取消</div>
                    <div className="editGantryCraneDrawer-ok" onClick={() => handleSubmit()}>确定</div>
                </div>}
            >
                <div style={{ marginBottom: 24 }}>
                    <Input
                        allowClear
                        ref={inputRef}
                        value={keywords}
                        onChange={(e) => setKeywords(e.target.value)}
                        style={{ width: 300 }}
                        placeholder={`请输入局点名称搜索`}
                    />
                </div>
                {
                    data.map((item: any, index: any) => (
                        <div key={index} style={{ marginBottom: 8 }}>
                            <Checkbox checked={selectId === item.id} onChange={() => onChange(index)}>{item.orgName}</Checkbox>
                        </div>
                    ))

                }
            </Drawer>
        </>
    );
}
