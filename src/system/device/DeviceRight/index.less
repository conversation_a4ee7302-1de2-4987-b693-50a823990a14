.tree-select-devicepage {
  .ant-select-tree {
    background: transparent;
    color: inherit;
  }

  .ant-select-tree .ant-select-tree-node-content-wrapper:hover {
    background: #E8F3FF;
  }
}

.DeviceTestRight {
  width: 100%;
  height: 100%;
  padding: 24px 28px;

  .table-status-online {
    color: #2ba471;
    background: #e3f9e9;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    display: inline-flex;
    padding: 2px 8px;
    align-items: center;
    border-radius: 3px;
    border: 1px solid #2ba471;
  }

  .table-status-Offline {
    color: rgba(0, 0, 0, 0.9);
    background: #f3f3f3;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    display: inline-flex;
    padding: 2px 8px;
    align-items: center;
    border-radius: 3px;
    border: 1px solid #dcdcdc;
  }

  .DeviceTestRight-title {
    color: #000;
    font-family: "PingFang SC";
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 32px;
    margin-bottom: 24px;

    .DeviceTestRight-title-button {
      cursor: pointer;
      border-radius: 3px;
      border: 1px solid #165DFF;
      background: #FFF;
      height: 32px;
      padding: 0px 16px;

      color: #165DFF;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      display: flex;
      align-items: center;
    }
  }

  .user-search-container {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // flex-flow: wrap;

    .search {
      .ant-form-inline .ant-form-item {
        margin: 0;
      }
    }

    .chose-group {
      display: flex;
      align-items: center;
      flex: none;

      .select {
        margin-left: 10px;
        margin-right: 10px;
      }

      .okbtn {
        cursor: pointer;
        margin-left: 10px;
        width: 60px;
        height: 32px;
        border: 1px solid var(--primary-light);
        color: var(--primary-light);
        text-align: center;
        line-height: 32px;
        border-radius: var(--radius1);
      }
    }
  }

  .DeviceTestRight-sort-backgroundColor {
    background-color: var(--content-bg);
  }
}