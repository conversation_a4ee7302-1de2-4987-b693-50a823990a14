import { useCallback, useMemo, useRef, useEffect, useState } from "react";
import { Table, Form, TreeSelect, Button, message, Input, Select, Space, Tag } from "antd";
import { useAntdTable, useSafeState, useSize, useToggle } from "ahooks";
import { IconFont, treeHelper, useHistory } from "@cloud-app-dev/vidc";
import type { ColumnsType } from "antd/es/table";
import warnImg from "@src/assets/image/warn.png";
import { useForm } from "antd/es/form/Form";
import OperatingBounced from "@src/components/OperatingBounced";
import Service from "@src/service/system";
import DeviceDrawer from '../DeviceDrawer'
import Authority from "@src/components/Authority";
import dayjs from "dayjs";
import useDict from "@src/ais_app/hooks/useDict";
import "./index.less";

interface tableType {
  list: any[];
  total: any;
}

export default function DeviceRight({
  groupId,
  treeData,
  setUpdataTime,
  catalog,
  checkedList,
  setCheckedList,
}: {
  treeData: any;
  groupId: string;
  setUpdataTime: any;
  catalog: string;
  checkedList: any;
  setCheckedList: any;
}) {
  const Dict7004 = useDict(["7004"]); // 字典
  const history = useHistory();
  const [refsh, { toggle: rToggle }] = useToggle(false);
  const ref = useRef<HTMLDivElement>(null);
  const size = useSize(ref);
  const [form] = useForm();
  const [moveGroupId, setMoveGroupId] = useSafeState<string | undefined>(undefined);
  const [isShow, { toggle }] = useToggle(false);
  const [isShowSelect, { toggle: setIsShowSelect, setLeft }] = useToggle(false);
  const [deviceDrawerOpen, setDeviceDrawerOpen] = useState(false)
  const [detailsInfo, setDetailsInfo] = useState({})
  const selectArr = useMemo<any[]>(() => treeHelper.computTreeList(treeData?.filter((v: any) => v.id !== "-1") || []), [treeData]);
  const [selectedRowKeys, setSelectedRowKeys] = useSafeState<React.Key[]>([]);
  const [tagList, setTagList] = useSafeState<any[]>([]);
  const getGroup: any = useCallback(
    (v: string, name: string) => {
      const group = treeData?.find((m: any) => m?.id === v);
      let res = "";
      if (group && name) {
        res = group?.groupName + ">" + name;
      } else {
        res = group?.groupName || name;
      }
      return group?.parentId ? getGroup(group?.parentId, res) : res;
    },
    [treeData],
  );
  useEffect(() => {
    setLeft();
    onSelectChange([]);
    setMoveGroupId(undefined);
  }, [groupId]);
  const getTableData = (
    { current, pageSize, sorter, filters }: { current: number; pageSize: number; sorter: any; filters: any },
    formData: Object,
  ): Promise<tableType> => {
    let params: any = {
      keywords: "",
      type: filters?.typeText ? filters?.typeText : [],
      limit: pageSize,
      sortField: "first_online_time",
      offset: (current - 1) * pageSize,
      sortOrder: sorter?.order === "ascend" ? "asc" : "desc",
      buzGroupId: groupId,
      isPagination: true,
      ...formData,
      deviceTagIds: checkedList,
    };
    params.searchGroupType = catalog
    if (catalog === "2") {
      params.originGroupId = params.buzGroupId;
      delete params.buzGroupId;
    }
    if (catalog === "3") {
      delete params.searchGroupType;
    }

    return Service.device.queryDevice(params).then((res) => {
      setUpdataTime(new Date().getTime());
      return {
        list: res?.data?.list,
        total: res?.data?.totalCount,
      };
    });
  };
  const getDeviceName = (getId: any) => {
    const allData = treeData?.filter((v: any) => v.id !== "-1") || [];
    return allData.find((v: any) => v.id === getId)?.groupName;
  };
  const { tableProps, search, params } = useAntdTable(getTableData, { defaultPageSize: 10, form, refreshDeps: [groupId, checkedList, refsh] });
  const { sorter = {}, filters = {} } = params[0] || ({} as any);
  //显示分页器跳转
  tableProps.pagination.showQuickJumper = true;
  tableProps.pagination.showSizeChanger = true;
  tableProps.pagination.showTotal = function (total: number, range: number[]) {
    return `共${total}项数据`;
  };
  const { submit, reset } = search;
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  const getFetchTag = () => {
    Service.device.deviceTagList({}).then((res) => {
      setTagList(res?.data?.list || []);
    });
  }
  useEffect(() => {
    setLeft();
    setSelectedRowKeys([]);
    submit();
    getFetchTag()
  }, [catalog]);
  const moveOk = () => {
    Service.device
      .dispenseDevice({
        buzGroupId: moveGroupId,
        cids: selectedRowKeys,
      })
      .then((res) => {
        toggle();
        if (res.code === 0) {
          message.success("操作成功");
          setMoveGroupId("");
          setSelectedRowKeys([]);
          submit();
        } else {
          message.warning(res.message);
        }
      });
  };
  const columns: ColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "key",
      key: "key",
      width: 88,
      fixed: "left",
      render: (_, __, index) => index + 1,
    },
    {
      title: "服务状态",
      ellipsis: true,
      dataIndex: "state",
      width: 134,
      render: (res) => {
        return (
          <div
            className={res === 1 ? `table-status-online` : `table-status-Offline `}
            style={{ color: res === 1 ? "#2BA471" : "rgba(0, 0, 0, 0.90)", background: res === 1 ? "#E3F9E9" : "#F3F3F3" }}
          >
            {res === 1 ? "在线" : "离线"}
          </div>
        );
      },
    },
    {
      title: "场所标签",
      ellipsis: true,
      dataIndex: "tagInfoList",
      width: 234,
      render: (res) => {
        return (
          <>
            {/* <Space> */}
            {res?.map((v: any) => {
              return (
                <Tag
                  style={{
                    color: "#" + v?.tagFontColor,
                    background: "#" + v?.tagBgColor,
                    borderColor: "#" + v?.tagFontColor,
                  }}
                >
                  {v?.tagName}
                </Tag>
              );
            })}
            {/* </Space> */}
          </>
        );
      },
    },
    {
      title: "设备名称",
      ellipsis: true,
      dataIndex: "name",
      width: 144,
      render: (res) => res || "-",
    },
    {
      title: "设备ID",
      ellipsis: true,
      dataIndex: "cid",
      width: 180,
      render: (res) => res || "-",
    },
    {
      title: "设备类型",
      ellipsis: true,
      dataIndex: "typeText",
      key: "typeText",
      width: 135,
      render: (res) => res || "-",
      // filters: DeviceType?.map((v) => ({
      //   text: v.name,
      //   value: v.code,
      // })),
      // filteredValue: filters.gender,
    },
    {
      title: "局点",
      ellipsis: true,
      width: 135,
      dataIndex: "orgName",
      render: (res) => res || "-",
    },
    {
      title: "接入平台IP",
      ellipsis: true,
      width: 135,
      dataIndex: "ip",
      render: (res) => res || "-",
    },
    {
      title: "首次上线时间",
      ellipsis: true,
      width: 188,
      dataIndex: "firstOnlineTime",
      // className: "DeviceTestRight-sort-backgroundColor",
      render: (res) => (res ? dayjs(res)?.format("YYYY-MM-DD HH:mm:ss") || "-" : "-"),
      // sorter: true,
      // sortOrder: sorter.field === "firstOnlineTime" && sorter.order,
    },
    {
      title: "所属分组",
      ellipsis: true,
      width: 210,
      dataIndex: "treeDesc",
      fixed: "right",
      render: (res) => (res ? res : "未分组"),
    },
    {
      title: "设备能力",
      dataIndex: "id",
      width: 160,
      fixed: "right",
      render: (text, item: any) =>
        `${item?.ptz ? "PTZ" : ""} ${item?.ptz && item?.localRecord ? "、" : ""} ${item?.localRecord ? "录像" : ""}${!item?.ptz && !item?.localRecord ? "-" : ""}`,
    },
    {
      title: '操作',
      key: 'operation',
      fixed: 'right',
      width: 98,
      render: (res: any, i: any, t: any) => {
        return <div style={{ display: 'flex', alignItems: 'center' }}>
          {
            window._PLATFORM_TYPE === 2 &&
            <Authority code="30000433">
              <a
                style={{ fontSize: 14, color: '#165DFF' }}
                onClick={() => {
                  setDetailsInfo(i)
                  setDeviceDrawerOpen(true)
                }}
              >
                标定
              </a>
            </Authority>
          }
        </div >
      },
    },
  ];
  const SearchForm = (
    <Form form={form} layout="inline" className="search-form">
      <Space>
        <Form.Item label="在线状态" name="state" style={{ marginRight: 8 }}>
          <Select
            placeholder="请选择"
            style={{ width: "145px" }}
            className="user-filter-btn"
            onChange={() => {
              onSelectChange([]);
              submit();
            }}
            allowClear={false}
            options={[
              {
                label: "全部",
                value: null,
              },
              {
                label: "在线",
                value: 1,
              },

              {
                label: "离线",
                value: 0,
              },
            ]}
          />
        </Form.Item>
        {catalog === "3" ? (
          <></>
        ) : (
          <Form.Item label="场所标签" name="deviceTagId" style={{ marginRight: 8 }}>
            <Select
              placeholder="请选择"
              style={{ width: "145px" }}
              className="user-filter-btn"
              allowClear
              onChange={() => {
                onSelectChange([]);
                submit();
              }}
              options={tagList?.map(i => ({ label: i.tagName, value: i.id }))}
            />
          </Form.Item>
        )}
        <Form.Item label="设备类型" name="type" style={{ marginRight: 8 }}>
          <Select
            placeholder="请选择"
            mode="tags"
            style={{ width: "145px" }}
            className="user-filter-btn"
            onChange={() => {
              onSelectChange([]);
              submit();
            }}
            allowClear={false}
            options={Dict7004.map((item:any)=>{
              return {
                label: item.value,
                value: item.id,
              }
            })}
          />
        </Form.Item>
        <Form.Item name="keywords">
          <Input
            placeholder="请输入设备名称/ID"
            className="user-filter-btn vidc-Input"
            allowClear={false}
            suffix={
              <IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ cursor: "pointer", fontSize: "12px", color: "rgba(255,255,255,0.5)" }} />
            }
            onChange={() => {
              onSelectChange([]);
              submit();
            }}
            style={{ width: "160px", borderRadius: "4px" }}
          />
        </Form.Item>
      </Space>
    </Form>
  );
  return (
    <div className="DeviceTestRight" ref={ref}>
      <div className="DeviceTestRight-title" style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ flex: 1 }}>设备管理</div>
        <Authority code="30000431">
          <div className="DeviceTestRight-title-button" onClick={() => history.push('/systemManagement/device/labelEdit')}>标签管理</div>
        </Authority>
      </div>
      <div className="user-search-container">
        <div className="search">{SearchForm}</div>
        {isShowSelect ? (
          <div className="chose-group">
            <div className="text">选中设备移动至</div>
            <div className="select">
              <TreeSelect
                showSearch
                popupClassName="tree-select-devicepage"
                filterTreeNode={(v, node) => node.groupName.includes(v)}
                treeData={selectArr}
                style={{ width: "220px" }}
                placeholder="请选择分组"
                fieldNames={{ label: "groupName", value: "id" }}
                value={moveGroupId}
                onChange={(v) => {
                  setMoveGroupId(v);
                }}
              />
            </div>
            <Button type="primary" onClick={toggle} disabled={moveGroupId && selectedRowKeys?.length !== 0 ? false : true}>
              确定
            </Button>
            <Button onClick={setIsShowSelect} style={{ color: "var(--primary)", borderColor: "var(--primary)", marginLeft: 8 }}>
              取消
            </Button>
          </div>
        ) : (
          <div>
            {catalog !== "1" ? null : (
              <Authority code="30000432">
                <Button type="primary" onClick={setIsShowSelect}>
                  移动设备
                </Button>
              </Authority>
            )}
          </div>
        )}
      </div>
      <Table
        rowKey="cid"
        columns={columns}
        rowSelection={isShowSelect ? rowSelection : undefined}
        scroll={{ y: size?.height && size.height - 250 }}
        {...tableProps}
      />
      <OperatingBounced
        isShow={isShow}
        onCancel={toggle}
        onOk={moveOk}
        title="移动确定"
        icon={<img src={warnImg} />}
        content={`是否要将选中的${selectedRowKeys?.length ?? 0}台设备移动至“${getDeviceName(moveGroupId)}”中？`}
      ></OperatingBounced>

      {
        deviceDrawerOpen &&
        <DeviceDrawer
          dataInfo={detailsInfo}
          setDataInfo={setDetailsInfo}
          open={deviceDrawerOpen}
          setOpen={setDeviceDrawerOpen}
          getTableData={rToggle}
        />
      }
    </div>
  );
}
