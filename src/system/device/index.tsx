import VideoLayout from "@src/components/VideoLayout";
import { useSafeState } from "ahooks";
import DeviceLeft from "./DeviceLeft";
import DeviceRight from "./DeviceRight";
export default function DevicePage() {
  const [groupId, setGroupId] = useSafeState<string>("");
  const [getUpdataTime, setUpdataTime] = useSafeState<string>("");
  const [treeData, setTreeData] = useSafeState<any>();
  const [catalog, setCatalog] = useSafeState<any>("2");
  const [checkedList, setCheckedList] = useSafeState<string[]>([]);
  return (
    <VideoLayout
      leftControl={
        <DeviceLeft
          checkedList={checkedList}
          setCheckedList={setCheckedList}
          setUpdataTime={setUpdataTime}
          catalog={catalog}
          setCatalog={setCatalog}
          getUpdataTime={getUpdataTime}
          setGroupId={setGroupId}
          setTreeData={setTreeData}
        />
      }
      RightContent={
        <DeviceRight
          checkedList={checkedList}
          setCheckedList={setCheckedList}
          catalog={catalog}
          setUpdataTime={setUpdataTime}
          treeData={treeData}
          groupId={groupId}
        />
      }
    />
  );
}
