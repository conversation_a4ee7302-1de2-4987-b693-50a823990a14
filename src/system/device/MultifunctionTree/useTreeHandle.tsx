import { IconFont, useSimpleState } from "@cloud-app-dev/vidc";
import { useMemoizedFn, useToggle } from "ahooks";
import { Popover, Input, Modal } from "antd";
import type { TreeProps } from "antd/es/tree";
import warnImg from "@src/assets/image/warn.png";
import { cache, treeHelper } from "@cloud-app-dev/vidc";
import ModalContent from "./components/ModalContent";
import { TreeItem, TreeHandleProps, OrderParamsType, OrderItem } from "./index.d";
import { cloneDeep, size, sortBy } from "lodash-es";
import { getTreeIdWithKeyword, getTreeMap } from "./utils";
import { useEffect, useMemo } from "react";

function useTreeHandle(
  treeList: TreeItem[],
  { reloadDeps, keyword, onSelect, add, update, move, modal, del, expandedAll, catalog }: TreeHandleProps,
) {
  const [showSearchOn, { toggle: searchtoggleOn }] = useToggle(false);
  const [state, updateState] = useSimpleState<any>({
    currentItem: {},
    preAddItem: { groupName: "", parentId: "" },
    expandedKeys: [],
    selectedKey: "",
    keyword: "",
  });
  const treeMap = useMemo(() => getTreeMap(treeList), [treeList]);
  useEffect(() => {
    const codes = getTreeIdWithKeyword(treeMap, keyword);
    updateState({ keyword: keyword, expandedKeys: codes });
  }, [keyword]);
  useEffect(() => {
    onSelect(state.selectedKey);
  }, [state.selectedKey]);
  useEffect(() => {
    updateState({ expandedKeys: [], selectedKey: "" });
  }, [catalog]);
  useEffect(() => {
    let expandedKeys: any = [];
    if (!expandedAll) {
      updateState({ expandedKeys: [] });
    } else {
      expandedKeys = treeList?.map((item: any) => item.id);
      updateState({ expandedKeys });
    }
  }, [expandedAll]);
  const getAllSameByParentId = useMemoizedFn((parentId: string) => treeList.filter((v) => v.parentId === parentId));
  const handlesOrderList = ({ dropParentKey, dropPosition, dragKey, dropKey }: OrderParamsType): OrderItem[] => {
    //dragNode是否与node为同父级
    const allData = sortBy(getAllSameByParentId(dropParentKey), "orderNo");
    const dragKeyIndex = allData.findIndex((v) => v.id === dragKey);
    const DropKeyIndex = allData.findIndex((v) => v.id === dropKey);

    if (dropPosition === 0) {
      if (dragKeyIndex !== -1) {
        //插入到指定位置
        allData.splice(dragKeyIndex, 1);
        allData.unshift({ id: dragKey });
      } else {
        allData.unshift({ id: dragKey });
      }
    } else {
      if (dragKeyIndex !== -1) {
        allData.splice(DropKeyIndex + dropPosition, 0, { id: dragKey });
        allData.splice(dragKeyIndex + dropPosition, 1);
      } else {
        allData.splice(DropKeyIndex + dropPosition, 0, { id: dragKey });
      }
    }
    //找所有同级的
    return allData.map((v, index) => ({ id: v.id, orderNo: index }));
  };
  const addGroupEnter = (name: string) => {
    Modal.confirm({
      title: "新增确认",
      width: 530,
      icon: null,
      className: "modal-comfire",
      content: <ModalContent icon={<img src={warnImg} />} content={`确认新增${name}分组吗？`} />,
      onOk() {
        modal.close();
        const orderList = handlesOrderList({
          dropParentKey: state.preAddItem.parentId,
          dropPosition: 0,
          dragKey: "",
          dropKey: state.preAddItem.parentId,
        });
        add({ userId: cache.getCache("userId", "session"), parentId: state.preAddItem.parentId, groupName: name }, orderList);
        updateState({ preAddItem: {} });
      },
      onCancel() {
        updateState({ preAddItem: {} });
      },
    });
  };
  const updateNameEnter = (name: string) => {
    if (name === state.currentItem.groupName) {
      updateState({ currentItem: { ...state.currentItem, edit: false } });
    } else {
      Modal.confirm({
        title: "重命名确认",
        width: 530,
        icon: null,
        className: "modal-comfire",
        content: <ModalContent icon={<img src={warnImg} />} content={`确认重命名为${name}吗？`} />,
        onOk() {
          modal.close();
          updateState({ currentItem: { ...state.currentItem, edit: false } });
          update({ id: state.currentItem.id, groupName: name });
        },
        onCancel() {
          updateState({ currentItem: { ...state.currentItem, edit: false } });
        },
      });
    }
  };
  const getGroupIds = (data: any) => {
    let listIds: any = [];
    if (data?.id) {
      listIds.push(data.id);
    }
    if (data?.children) {
      data.children.forEach((v: any) => {
        listIds = [...listIds, ...getGroupIds(v)];
      });
    }
    return listIds;
  };
  const deleteGroupEnter = (id: string, name: string, node: any) => {
    const ids = getGroupIds(node) || [];
    Modal.confirm({
      title: "删除确认",
      width: 530,
      icon: null,
      className: "modal-comfire",
      content: <ModalContent icon={<img src={warnImg} />} content={`确认删除${name}分组${size(ids) > 0 ? "及以下所有子分组" : ""}吗？`} />,
      onOk() {
        modal.close();
        del([...ids]);
      },
    });
  };
  const onDrop: TreeProps["onDrop"] = (info: any) => {
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropParentKey = info.node?.parentId;
    const dragParentKey = info.dragNode?.parentId;
    const dropPos = info.node.pos.split("-");
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);
    //dragNode是否与node为同级
    if (info.dropToGap) {
      const orderList = handlesOrderList({ dropParentKey, dropPosition, dragKey, dropKey });
      if (dragParentKey === dropParentKey) {
        //重新排序
        move(orderList);
      } else {
        // 改dragNode父级节点改为node同父级,//找所有同级的，再排序
        move(orderList, dragKey, dropParentKey);
      }
    } else {
      //改dragNode父级节点为node,找所有同级的，再排序
      const orderList = handlesOrderList({ dropParentKey: dropKey, dropPosition, dragKey, dropKey });
      move(orderList, dragKey, dropKey);
    }
  };
  const treeDataTemp: any = useMemo(() => treeHelper.computTreeList(treeList), [treeList]);
  const MoreMenu = (node: any) => {
    return (
      <div className="handles">
        <p
          className="hbtn"
          onClick={() => {
            updateState({ currentItem: { id: node.id, edit: true, groupName: node.groupName } });
          }}
        >
          <IconFont type="icon-xitongguanli_zhongmingming" /> 重命名
        </p>
        {/* {node.id !== '1000' && ( */}
        <p
          className="hbtn"
          onClick={() => {
            deleteGroupEnter(node.id, node.groupName, node);
          }}
        >
          <IconFont type="icon-renwupeizhi_shebeirenwu_qingkong" /> 删除
        </p>
        {/* )} */}
      </div>
    );
  };
  // 树数据处理，高亮关键字
  const treeData = useMemo(() => {
    const loop = (data: any[]): any[] =>
      data.map((item: any) => {
        const strName = item.groupName || ("" as string);
        const index = strName.indexOf(state.keyword);
        const beforeStr = strName.substring(0, index);
        const afterStr = strName.slice(index + state.keyword.length);
        const isEdit = item.id === state.currentItem.id ? state.currentItem.edit : false;
        //当前节点是否为新增父节点新增
        const addFlag = item.id === state.preAddItem.parentId;
        //当前节点是否为新增父节点新增
        const currentAdd = item.id === "preId";
        let EditGroupName = state.currentItem.groupName;
        const name =
          index > -1 ? (
            <span>
              {beforeStr}
              <span className="site-tree-search-value">{state.keyword}</span>
              {afterStr}
            </span>
          ) : (
            <span>{strName}</span>
          );
        let title = isEdit ? (
          <div className="searchinput">
            <Input
              placeholder="请输入重命名名称"
              allowClear={false}
              maxLength={20}
              autoFocus
              defaultValue={EditGroupName}
              style={{ fontSize: "var(--fs-small)" }}
              onPressEnter={(e: any) => {
                // if (e.target.value?.trim()) {
                //   searchtoggleOn();
                //   updateNameEnter(e.target.value);
                // } else {
                //   updateState({ currentItem: { ...state.currentItem, edit: false } });
                // }
              }}
              onBlur={(e) => {
                if (showSearchOn) return searchtoggleOn();
                if (e.target.value?.trim()) {
                  updateNameEnter(e.target.value);
                } else {
                  updateState({ currentItem: { ...state.currentItem, edit: false } });
                }
              }}
              prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: "12px" }} />}
              suffix={
                <IconFont
                  type="icon-renwupeizhi_shebeirenwu_sousuoguanbi"
                  onClick={() => {
                    updateState({ currentItem: { id: item.id, edit: false } });
                  }}
                />
              }
            />
          </div>
        ) : (
          <div className="tree-item">
            <div
              className="item-info"
              title={strName}
              onClick={() => {
                updateState({ selectedKey: item.id === state.selectedKey ? "" : item.id });
              }}
            >
              {name}
            </div>
            {strName !== "未分组" && catalog !== "2" && (
              <div className="item-tool">
                <Popover content={MoreMenu(item)} placement="bottomLeft">
                  <IconFont type="icon-xitongguanli_gengduo" />
                </Popover>
                <IconFont
                  type="icon-xitongguanli_zengjiafenzu"
                  onClick={() => {
                    updateState({ preAddItem: { id: "preId", groupName: "", parentId: item?.id } });
                  }}
                />
              </div>
            )}
          </div>
        );
        if (currentAdd) {
          title = (
            <div className="searchinput">
              <Input
                placeholder="请输入新增分组名称"
                allowClear={false}
                maxLength={20}
                autoFocus
                style={{ fontSize: "var(--fs-small)" }}
                // onPressEnter={(e: any) => {
                //   if (e.target.value?.trim()) {
                //     searchtoggleOn();
                //     addGroupEnter(e.target.value);
                //   } else {
                //     updateState({ preAddItem: {} });
                //   }
                // }}
                onBlur={(e) => {
                  if (showSearchOn) return searchtoggleOn();
                  if (e.target.value?.trim()) {
                    addGroupEnter(e.target.value);
                  } else {
                    updateState({ preAddItem: {} });
                  }
                }}
                prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: "12px" }} />}
                suffix={
                  <IconFont
                    type="icon-renwupeizhi_shebeirenwu_sousuoguanbi"
                    onClick={() => {
                      updateState({ preAddItem: {} });
                    }}
                  />
                }
              />
            </div>
          );
        }
        const newChildren: any[] = cloneDeep(item.children) || [];
        if (addFlag) {
          updateState({ expandedKeys: [...state.expandedKeys, item.id] });
          newChildren.unshift(state.preAddItem);
        }
        if (newChildren.length !== 0) {
          return { ...item, name: title, code: item.code, children: loop(newChildren) };
        }
        return { ...item, name: title, code: item.code };
      });
    const data = loop(treeDataTemp);
    return data;
  }, [state.keyword, treeDataTemp, state.currentItem, state.preAddItem, state.selectedKey]);
  const onExpand = (expandedKeys: any) => {
    updateState({ expandedKeys });
  };
  return { treeData, onExpand, expandedKeys: state.expandedKeys, selectedKey: state.selectedKey, onDrop };
}

export default useTreeHandle;
