import { Tree } from 'antd';
import { MultifunctionTreeType} from './index.d';
import { IconFont, RefModal } from '@cloud-app-dev/vidc';
import { IRefModalMethodsProps } from '@cloud-app-dev/vidc/es/RefModal';
import useTreeHandle from './useTreeHandle';
import { useRef , useEffect} from 'react';

const MultifunctionTree = ({ treeList, expandedAll,keyword, updateNode, updateName, addGroup, deleteGroup,onSelect,setOrgListCurr=()=>{},catalog }: any) => {
  const ref = useRef<IRefModalMethodsProps>(null);
  const {treeData,onDrop,onExpand,expandedKeys,selectedKey} = useTreeHandle(treeList,{
    keyword:keyword || '',
    expandedAll:expandedAll,
    add:addGroup,
    update:updateName,
    move:updateNode,
    del:deleteGroup,
    onSelect:onSelect,
    modal:ref.current as IRefModalMethodsProps,
    catalog
  })
  useEffect(()=>{
    setOrgListCurr(treeData)
  },[treeData])
  return (
    <>
      <Tree
        switcherIcon={<IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" style={{ transform: 'rotate(90deg)' }} />}
        treeData={treeData}
        fieldNames={{ title: 'name', key: 'id' }}
        onExpand={onExpand}
        selectedKeys={[selectedKey]}
        draggable
        onDrop={onDrop}
        expandedKeys={expandedKeys}
      />
      <RefModal ref={ref} />
    </>
  );
};

export default MultifunctionTree;
