import './index.less'
interface ModalContentType{
    icon:any;
    content:string;
    colortext?:string;
    info?:string;
    symbol?:string;
}
const ModalContent = ({symbol,icon,content,colortext,info}:ModalContentType) => {
    return (
    <div className="modal-content-box">
        <div className='iconbox'>{icon}</div>
        <div className='Bouncedcontent' title={content}>{content} <span className="color-text" title={colortext}>{colortext}</span> <span>{symbol}</span></div>
        <div className='small-info' title={info}>{info}</div>
    </div>
    )
}
export default ModalContent