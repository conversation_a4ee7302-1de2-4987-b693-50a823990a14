.modal-content-box {
    padding: 12px 16px 16px;
    height: max-content;
    margin: 8px 12px 12px;
    position: relative;
    .iconbox {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      margin: 32px auto 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
    }
    .Bouncedcontent,
    .small-info {
      width: 100%;
      // width: max-content;
      font-weight: 400;
      font-size: 16px;
      line-height: 26px;
      text-align: center;
      color: rgba(0, 0, 0, 0.6);
      margin: 0 auto 0px;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
    }
    .color-text {
      color: rgba(255, 173, 74, 1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .info {
      font-size: 14px;
    }
  }