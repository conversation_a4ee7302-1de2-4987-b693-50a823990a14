import { IRefModalMethodsProps } from '@cloud-app-dev/vidc/es/RefModal';
export interface MultifunctionTreeType {
    treeList: any[];
    expandedAll?: boolean;
    keyword?:string;
    updateNode: (orderList: OrderItem[], id?: string, parentId?: string) => void;
    updateName: (item: UpdateParams) => void;
    addGroup: (item: UpdateParams, orderList: OrderItem[]) => void;
    deleteGroup: (ids: string[]) => void;
    onSelect:(v:string)=>void;
  }
export interface UpdateParams{
    id?:string;
    groupName?:string;
    userId?:string;
    parentId?:string;
}

export interface TreeItem {
    id: string;
    groupName?: string;
    parentId?: string;
    [key:string]:any;
}
export interface TreeHandleProps {
    reloadDeps?:any[];
    keyword:string;
    expandedAll?:boolean;
    move:(orderList: OrderItem[], id?: string, parentId?: string) => void;
    add:(item: UpdateParams, orderList: OrderItem[]) => void;
    update:(item: UpdateParams) => void;
    del:(ids: string[]) => void;
    onSelect:(v:string)=>void;
    modal:IRefModalMethodsProps;
    catalog?:string;
}
export interface OrderParamsType{
    dropParentKey:string;
    dropPosition:number;
    dragKey:string;
    dropKey:string;
}
export interface OrderItem{ 
    id: string|undefined; 
    orderNo: number; 
}
export interface ITreeItem {
    [key:string]:any
}