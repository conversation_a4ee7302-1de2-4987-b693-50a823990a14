.user-org {
  width: 320px;
  height: 100%;
  margin-right: 24px;
  display: flex;
  flex-direction: column;
  background: var(--content-bg);
  // box-shadow: 0px 5px 14px rgba(0, 0, 0, 0.05);
  border-radius: 14px;
  .my-check {
    width: 100%;
    position: relative;
    padding-left: 16px;
    .check-all {
      span:last-child {
        // color: #2bbfc7;
      }
    }
    .ant-checkbox-group {
      width: 100%;
      display: grid;
      // grid-template-columns: 134px 134px;
      grid-gap: 12px 20px;
      // overflow: hidden;
      .ant-checkbox-inner {
        border-radius: 2px;
      }
      .ant-checkbox-wrapper-in-form-item {
        margin: 0px;
        width: 100%;
        span:last-child {
          font-size: 12px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  
  .ant-tree-node-selected{
    background-color: #E8F3FF !important;
    color: var(--primary);
  }
  .ant-tree-treenode {
    width: 100%;
    padding: 0;
  }
  .ant-tree .ant-tree-node-content-wrapper {
    flex: 1;
    &:hover {
      background-color: #E8F3FF;
    }
    &.ant-tree-node-selected {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }

  .site-tree-search-value {
    color: var(--danger);
  }
  .tree-heard {
    // border-bottom: 1px solid var(--gray8);
    padding: 16px 16px 10px;
    // height: 50px;
    // line-height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // .ant-space-item{
    //   width: 100%;
    // }
    .ant-input {
      // background-color: transparent;
    }
    .title {
      color: rgba(0, 0, 0, 0.9);
      /* Title/Medium */
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      display: flex;
      align-items: center;
      & > span {
        margin-left: 8px;
        color: rgba(0, 0, 0, 0.9);
        /* Body/Medium */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    }
    .rightIcon {
      span {
        cursor: pointer;
      }
      span ~ span {
        margin-left: 10px;
      }
    }
  }
  .tree-creatTime {
    padding: 0px 16px;
    color: rgba(0, 0, 0, 0.6);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .org-tree {
    padding: 12px 16px;
    overflow: auto;
    flex: 1;
    position: relative;
    .org-tree-tabs{
        height: auto !important;
    }
    .ant-tree-treenode {
      position: static !important;
    }
    .ant-tabs-tab{
      padding-left: 8px;
      padding-right: 8x;
    }
  }
  .ant-tree-draggable-icon {
    display: none;
  }

  .tree-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .item-info {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
    .item-tool {
      width: 40px;
      height: 100%;
      .anticon {
        height: 20px;
        width: 20px;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        display: none;
      }
      .anticon:hover {
        // background-color: rgba(232, 177, 39, 0.2);
        // color: var(--secondary2);
        color: var(--primary);
      }
    }
    &:hover {
      .item-tool {
        display: flex;
        align-items: center;
        justify-content: center;
        .anticon {
          display: flex;
        }
      }
    }
  }
}
.handles {
  .hbtn:hover {
    background-color: #E8F3FF;
    color: var(--primary);
  }
  .hbtn {
    cursor: pointer;
    margin: 0;
    line-height: 32px;
    width: 112px;
    height: 32px;
    padding: 0 14px;
    .anticon {
      margin-right: 10px;
    }
  }
}
