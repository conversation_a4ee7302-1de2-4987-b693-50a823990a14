import { useEffect, useState, useMemo, useRef } from "react";
import { message, Input, Tabs, Space, Modal, Checkbox, Tooltip } from "antd";
import { IconFont, treeHelper } from "@cloud-app-dev/vidc";
import Service from "@src/service/system";
import warnImg from "@src/assets/image/warn.png";
import { useMount, useSafeState, useToggle } from "ahooks";
import MultifunctionTree from "../MultifunctionTree";
import { OrderItem } from "../MultifunctionTree/index.d";
import RefModal, { IRefModalMethodsProps } from "@cloud-app-dev/vidc/es/RefModal";
import ModalContent from "../MultifunctionTree/components/ModalContent";
import dayjs from "dayjs";
import "./index.less";
const CheckboxGroup = Checkbox.Group;
const catalogs: any = {
  1: "queryTree", // 原始目录接口
  2: "queryTreeNew", // 自定义目录接口
  3: "deviceTagList", //场所标签接口
};

const DeviceLeft = ({
  setGroupId,
  setTreeData,
  getUpdataTime,
  catalog,
  setCatalog = () => {},
  setUpdataTime,
  checkedList,
  setCheckedList
}: {
  setTreeData: (v: any) => void;
  setGroupId: (v: string) => void;
  getUpdataTime: string;
  catalog: string;
  setCatalog: (v: string) => void;
  setUpdataTime: (v: string) => void;
  checkedList:any,
  setCheckedList: any;
}) => {
  function TitleTip({ title, children }: { title?: string; children?: JSX.Element }) {
    if (title && title.length > 8) {
      return <Tooltip title={title}>{children}</Tooltip>;
    } else {
      return <>{children}</>;
    }
  }
  const ref: any = useRef<IRefModalMethodsProps>(null);
  const [orgList, setOrgList] = useState<any>([]);
  const [isInputShow, setIsInputShow] = useState<any>(false);
  const [countData, setCountData] = useState<any>({});
  const [orgListCurr, setOrgListCurr] = useState<any>([]);
  const [defaultExpandedAll, { toggle: ExpandedAllToggle }] = useToggle(false);
  const [showSearch, { toggle: searchtoggle }] = useToggle(false);
  const [showSearchOn, { toggle: searchtoggleOn }] = useToggle(false);
  const [keyword, steKeyword] = useSafeState<string>("");
  const getData = () => {
    // Service.device.queryTree().then((res) => {
    const device: any = Service.device;
    catalogs[catalog] &&
      device[catalogs[catalog]]({}).then((res: any) => {
        if (res.code === 0) {
          setUpdataTime(dayjs()?.format("YYYY-MM-DD HH:mm:ss"));
          if (catalog === "3") {
            setOrgList(res?.data?.list || []);
            setCheckedList((res?.data?.list || []).map((v: any) => v.id));
          } else {
            setOrgList(res?.data || []);
          }
        }
      });
  };
  useEffect(() => steKeyword(""), [showSearch]);
  useEffect(() => setTreeData(orgList), [orgList]);
  useEffect(() => getData(), [catalog]);
  useEffect(() => setCheckedList([]), [catalog]);
  const onSelect = (v: string) => {
    setGroupId(v);
  };
  const updateNode = (orderList: OrderItem[], id?: string, parentId?: string) => {
    const isSameParent = orgList?.find((v: any) => v.id === id)?.parentId === parentId;
    if (id && parentId && id !== "-1" && parentId !== "-1" && isSameParent) {
      Service.device.updateDeviceGroup({ id, parentId }).then((res) => {
        if (res.code === 0) {
          Service.device.moveDeviceBuzGroup(orderList).then((result) => {
            if (result.code === 0) {
              getData();
            } else {
              message.warning(result.message);
            }
          });
        } else {
          message.warning(res.message);
        }
      });
    } else if (!parentId && !id) {
      Service.device.moveDeviceBuzGroup(orderList).then((res) => {
        if (res.code === 0) {
          getData();
        } else {
          message.warning(res.message || res.codeRemark);
        }
      });
    } else {
      // message.warning("无法移动分组");
      if (catalog === "2") return message.warning("无法移动原始目录节点");
      const targetItem = orgList?.find((v: any) => v.id === id) || {};
      if (!parentId) {
        return message.warning("无法移动到根结点");
      }
      if (parentId === "-1") {
        return message.warning("无法移动到未分组");
      }
      const params = {
        sourceGroupId: id,
        sourceParentId: targetItem.parentId,
        targetGroupId: parentId || orgListCurr[0]?.parentId,
      };
      Service.device
        .moveToGroup(params)
        .then((res) => {
          if (res.code === 0) {
            getData();
          } else {
            message.warning(res.message);
          }
        })
        .catch((err) => {
          message.warning(err);
        });
    }
  };
  const updateName = (item: any) => {
    Service.device.updateDeviceGroup(item).then((res) => {
      if (res.code === 0) {
        message.success("重命名成功");
        getData();
      } else {
        message.warning(res.message);
      }
    });
  };
  const addGroup = (item: any, orderList: any[]) => {
    Service.device.addDeviceGroup(item).then((res) => {
      if (res.code === 0) {
        message.success("新增成功");
        orderList[0].id = res.data?.id;
        updateNode(orderList);
      } else {
        message.warning(res.message);
      }
    });
  };
  const deleteGroup = (ids: string[]) => {
    Service.device.deleteDeviceGroup(ids).then((res) => {
      if (res.code === 0) {
        message.success("删除成功");
        getData();
      } else {
        message.warning(res.message);
      }
    });
  };
  const treeDataTemp = useMemo(() => {
    return treeHelper.computTreeList(orgList);
  }, [orgList]);
  const addGroupEnter = (name: string) => {
    // if (ref.current) {
    Modal.confirm({
      title: "新增确认",
      width: 530,
      icon: null,
      className: "modal-comfire",
      content: <ModalContent icon={<img src={warnImg} />} content={`确认新增${name}分组吗？`} />,
      onOk() {
        ref.current?.close();
        Service.device
          .addDeviceGroup({
            groupName: name,
          })
          .then((res) => {
            if (res.code === 0) {
              getData();
              message.success("新增成功");
            } else {
              message.warning(res.message);
            }
          });
        setIsInputShow(false);
      },
      onCancel() {
        setIsInputShow(false);
      },
    });
    // }
  };
  useEffect(() => {
    const { totalCount, onlineCount } = treeDataTemp.reduce(
      (p, n) => {
        p.onlineCount += n.onlineCount || 0;
        p.totalCount += n.totalCount || 0;
        return p;
      },
      { totalCount: 0, onlineCount: 0 },
    );
    setCountData({ totalCount, onlineCount });
  }, [treeDataTemp]);
  const checkAll = orgList?.length === checkedList.length;
  const indeterminate = checkedList.length > 0 && checkedList.length < orgList?.length;
  const onChange = (list: string[]) => {
    setCheckedList(list);
  };

  const onCheckAllChange = (e:any) => {
    setCheckedList(e.target.checked ? orgList.map((item:any) => item.id) : []);
  };
  return (
    <div className="user-org">
      <div className="tree-heard">
        {/* {showSearch ? (
          <Input
            placeholder="输入目录名称"
            bordered={false}
            autoFocus
            allowClear={false}
            style={{ paddingLeft: "18px" }}
            onChange={(v) => steKeyword(v.target.value)}
            prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ position: "absolute", left: "4px", zIndex: "2" }} />}
            suffix={
              <IconFont type="icon-renwupeizhi_shebeirenwu_sousuoguanbi" onClick={searchtoggle} style={{ cursor: "pointer", fontSize: "12px" }} />
            }
          />
        ) : (
          <> */}
        <div className="title">
          设备分组
          <span>
            <span style={{ color: "var(--primary)" }}>{countData?.onlineCount || 0}</span>/{countData?.totalCount || 0}
          </span>
        </div>
        <IconFont
          onClick={() => {
            getData();
            setGroupId("");
            message.success("刷新成功");
          }}
          type="icon-shipinchakan_shuaxin"
        />
        {/* <div className="rightIcon">
              <span onClick={searchtoggle}>
                <IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" />
              </span>
              <span onClick={ExpandedAllToggle}>
                {defaultExpandedAll ? (
                  <IconFont style={{ fontSize: "16px" }} type="icon-renwupeizhi_shebeirenwu_zhankaicaidan" />
                ) : (
                  <IconFont style={{ fontSize: "16px" }} type="icon-renwupeizhi_shebeirenwu_shouqicaidan" />
                )}
              </span>
            </div>
          </>
        )} */}
      </div>
      <div className="tree-creatTime">最后更新：{getUpdataTime ? dayjs(getUpdataTime).format("YYYY-MM-DD HH:mm:ss") : "-"}</div>
      <div className="org-tree">
        <Tabs
          className="org-tree-tabs"
          defaultActiveKey={catalog}
          items={[
            {
              key: "1",
              label: "自定义目录",
              children: <></>,
            },
            {
              key: "2",
              label: "原始目录",
              children: <></>,
            },
            {
              key: "3",
              label: "场所标签",
              children: <></>,
            },
          ]}
          onChange={(e) => {
            setCatalog(e);
            setGroupId(e === "1" ? "" : "");
          }}
        />
        {catalog === "3" ? (
          <></>
        ) : (
          <>
            <Space className="tree-heard" style={{ padding: 0, paddingBottom: 16 }}>
              <Input
                placeholder="输入目录名称"
                allowClear={false}
                value={keyword}
                style={{ marginRight: "16px", paddingLeft: 18, width: "100%" }}
                onChange={(v) => steKeyword(v.target.value)}
                prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ position: "absolute", left: "4px", zIndex: "2" }} />}
                suffix={
                  <IconFont type="icon-renwupeizhi_shebeirenwu_sousuoguanbi" onClick={searchtoggle} style={{ cursor: "pointer", fontSize: "12px" }} />
                }
              />
              <div className="rightIcon">
                {catalog === "2" ? null : (
                  <span
                    onClick={() => {
                      setIsInputShow(true);
                    }}
                  >
                    <IconFont type="icon-xitongguanli_zengjiafenzu" />
                  </span>
                )}
                <span onClick={ExpandedAllToggle}>
                  {defaultExpandedAll ? (
                    <IconFont style={{ fontSize: "16px" }} type="icon-renwupeizhi_shebeirenwu_zhankaicaidan" />
                  ) : (
                    <IconFont style={{ fontSize: "16px" }} type="icon-renwupeizhi_shebeirenwu_shouqicaidan" />
                  )}
                </span>
              </div>
            </Space>
            {isInputShow ? (
              <Input
                placeholder="请输入新增分组名称"
                allowClear={false}
                autoFocus
                maxLength={20}
                style={{ fontSize: "var(--fs-small)" }}
                // prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: '12px' }} />}
                suffix={
                  <IconFont
                    type="icon-renwupeizhi_shebeirenwu_sousuoguanbi"
                    onClick={() => {
                      setIsInputShow(false);
                    }}
                  />
                }
                onBlur={(e) => {
                  if (showSearchOn) return searchtoggleOn();
                  if (e.target.value?.trim()) {
                    addGroupEnter(e.target.value);
                  } else {
                    setIsInputShow(false);
                  }
                }}
                onPressEnter={(e: any) => {
                  if (e.target.value?.trim()) {
                    searchtoggleOn();
                    addGroupEnter(e.target.value);
                  } else {
                    setIsInputShow(false);
                  }
                }}
              />
            ) : (
              <></>
            )}
          </>
        )}
        {catalog === "3" ? (
          <>
            <div style={{ marginBottom: 16 }}>
              <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
                全选
              </Checkbox>
            </div>
            <div className="my-check">
            <CheckboxGroup value={checkedList} onChange={onChange}>
              {orgList.map((item: any) => (
                <TitleTip title={item.tagName} key={item.tagName}>
                  <div>
                    <Checkbox value={item.id}>{item.tagName}</Checkbox>
                  </div>
                </TitleTip>
              ))}
            </CheckboxGroup>
            </div>
          </>
        ) : (
          <MultifunctionTree
            catalog={catalog}
            treeList={orgList}
            setOrgListCurr={setOrgListCurr}
            keyword={keyword}
            onSelect={onSelect}
            expandedAll={defaultExpandedAll}
            deleteGroup={deleteGroup}
            updateNode={updateNode}
            updateName={updateName}
            addGroup={addGroup}
          />
        )}
      </div>
      {/* <RefModal ref={ref} /> */}
    </div>
  );
};

export default DeviceLeft;
