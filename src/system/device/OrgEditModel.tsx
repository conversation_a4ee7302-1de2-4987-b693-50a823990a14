import { Input, Form, message, Modal } from "antd";
import Service from "@src/service/system";
import { cache } from "@cloud-app-dev/vidc";
import { useEffect } from "react";
import { useRequest } from "ahooks";
import { useForm } from "antd/es/form/Form";

interface OrgEditProps {
  visible: boolean;
  setOrgModelVisible: any;
  orgModelInfo?: any;
  getDir: () => void;
  isAdd: boolean;
}

const OrgEditModel = ({ isAdd, visible, setOrgModelVisible, orgModelInfo, getDir }: OrgEditProps) => {
  const [form] = useForm();
  useEffect(() => {
    form.resetFields();
    if (!isAdd) {
      form.setFieldValue("groupName", orgModelInfo?.groupName);
    }
  }, [orgModelInfo, visible]);
  const { run: addrun } = useRequest(Service.device.addDeviceGroup, {
    manual: true,
    onSuccess: (result) => {
      if (result.code === 0) {
        getDir();
      } else {
        message.warning(result.message);
      }
    },
    onError: (err: any) => {
      message.warning(err.data.message);
    },
  });
  const { run: updaterun } = useRequest(Service.device.updateDeviceGroup, {
    manual: true,
    onSuccess: (result) => {
      if (result.code === 0) {
        getDir();
      } else {
        message.warning(result.message);
      }
    },
    onError: (err: any) => {
      message.warning(err.data.message);
    },
  });
  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        const code: string = orgModelInfo?.id;
        if (isAdd) {
          addrun({ userId: cache.getCache("userId", "session"), ...values, parentId: code });
        } else {
          updaterun({ id: code, ...values });
        }
        setOrgModelVisible();
      })
      .catch((err) => console.error("err", err));
  };

  return (
    <Modal
      title={isAdd ? "新增分组" : "重命名"}
      centered
      open={visible}
      onOk={handleSubmit}
      onCancel={() => {
        setOrgModelVisible(false);
      }}
      width={400}
    >
      <Form form={form} className="user-form">
        <Form.Item label="分组名称" name="groupName" style={{ margin: "0" }} rules={[{ required: true, max: 10 }]}>
          <Input placeholder="请输入" showCount maxLength={10} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default OrgEditModel;
