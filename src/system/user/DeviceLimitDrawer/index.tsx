import { useEffect } from "react";
import { But<PERSON>, message, Modal, Tabs } from "antd";
import DeviceAssign from "../DeviceAssignNew";
import { IconFont } from "@cloud-app-dev/vidc";
import service from "@src/service/user";
import { useSafeState } from "ahooks";
import { uuid } from "@cloud-app-dev/vidc";
import Drawer from "@src/components/Drawer";
import DeviceAssignS from "../DeviceAssign";
import { userInfoAll, updateUser, distributeDevicesCopy } from "@src/service/ais_app";
import "./index.less";

interface DeviceLimitModalType {
  isShow: boolean;
  toggle: () => void;
  submit: () => void;
  item: any;
}
const DeviceLimitModal = ({ isShow, toggle, submit, item }: DeviceLimitModalType) => {
  const [resData , setResData]:any = useSafeState({});
  const [stup , setStup]:any = useSafeState('1');
  const onFinish = () => {
    let params = {
      ...resData,
      userDataRight: {
        ...resData.userDataRight,
        userId: item?.userId,
      },
    }
    if(stup === '1'){
      params.userDataRight.deviceBuzGroupIds = selectItems;
      updateUser({
        ...params
      })
        .then((res: any) => {
          if (res.code === 0) {
            message.success("分配成功");
            toggle();
            submit();
          } else {
            message.warning(res.message||res.codeRemark);
          }
        });
    }
    if(stup === '2'){
      distributeDevicesCopy({
        targetUserIds:selectItemsUser,
        userId: item?.userId,
      })
        .then((res: any) => {
          if (res.code === 0) {
            message.success("分配成功");
            toggle();
            submit();
          } else {
            message.warning(res.message||res.codeRemark);
          }
        }).catch((err)=> {
          message.warning(err?.data?.message)
        })
    }
  };
  const [key, setKey] = useSafeState(uuid());
  const [selectItems, setSelectItems] = useSafeState<any[]>([]);
  const [selectItemsUser, setSelectItemsUser] = useSafeState<any[]>([]);
  useEffect(() => {
    setSelectItems([]);
    if (item?.userId && isShow) {
      userInfoAll(item?.userId).then((res) => {
        // service.getUserDevicesNew({ userId: item?.userId }).then((res) => {
        if (res.code === 0) {
          const data = res?.data?.userDataRight || {};
          const list = data?.deviceBuzGroupIds || [];
          setResData(res.data);
          setSelectItems(list);
          setKey(uuid());
        }
      });
    }
  }, [item?.userId, isShow]);
  return (
    <Drawer
      closable={false}
      open={isShow}
      width={907}
      onClose={toggle}
      forceRender
      className="deviceLimitModal-modal"
      onOk={onFinish}
      title={
        <div className="userform-wrapper-title">
          <div>设备权限管理</div>
          <div>
            <IconFont type="icon-guanbi" onClick={toggle} />
          </div>
        </div>
      }
      getContainer={() => document.body}
    >
      <Tabs
        defaultActiveKey="1"
        onChange={(key)=>setStup(key)}
        items={[
          {
            key: "1",
            label: "设备选择",
            children: (
              <DeviceAssignS
                key={key}
                selectItems={selectItems}
                onChange={(v: any) => {
                  setSelectItems(v);
                }}

              />
            ),
          },
          {
            key: "2",
            label: "用户权限复制",
            children: (
              <DeviceAssign
                key={key}
                item={item}
                selectItems={selectItemsUser}
                onSelectChange={(v: any) => {
                  setSelectItemsUser(v);
                }}
              />
            ),
          },
        ]}
      />
    </Drawer>
  );
};

export default DeviceLimitModal;
