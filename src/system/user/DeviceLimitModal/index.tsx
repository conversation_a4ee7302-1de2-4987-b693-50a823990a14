import { useEffect } from 'react';
import { Button, message, Modal } from 'antd';
import  GradientTitle  from '@src/components/GradientTitle';
import DeviceAssign from '@src/components/DeviceAssignNew';
import { IconFont } from '@cloud-app-dev/vidc';
import service from '@src/service/user';
import { useSafeState } from 'ahooks';
import { uuid } from '@cloud-app-dev/vidc';
import './index.less';

interface DeviceLimitModalType {
  isShow: boolean;
  toggle: () => void;
  submit: () => void;
  item: any;
}
const DeviceLimitModal = ({ isShow, toggle, submit, item }: DeviceLimitModalType) => {
  const onFinish = () => {
    toggle();
    service
      .distributeDeviceGroups({
        userId: item?.userId,
        deviceBuzGroupIds: selectItems,
      })
      .then((res: any) => {
        if (res.code === 0) {
          message.success('分配成功');
          submit();
        } else {
          message.warning('分配失败');
        }
      });
  };
  const [key, setKey] = useSafeState(uuid());
  const [selectItems, setSelectItems] = useSafeState<any[]>([]);
  useEffect(() => {
    setSelectItems([]);
    if (item?.userId && isShow) {
      service.getUserDevicesNew({ userId: item?.userId }).then((res) => {
        if (res.code === 0) {
          const data = res?.data || {};
          const list = data?.deviceBuzGroupIds || [];
          setSelectItems([...list]);
          setKey(uuid());
        }
      });
      // service.getUserDevices(item?.userId).then((res) => {
      //   if (res.code === 0 && res?.data?.cids && res?.data?.cids?.length !== 0) {
      //     deviceservice.queryDevice({ cidList: res.data.cids }).then((result) => {
      //       if (result.code === 0) {
      //         setSelectItems(result.data?.list?.map((v: any) => ({ ...v, cid: +v.cid })));
      //         setKey(uuid());
      //       }
      //     });
      //   }
      // });
    }
  }, [item?.userId, isShow]);
  return (
    <Modal width={1002} open={isShow} footer={[]} closable={false} className="deviceLimitModal-modal" destroyOnClose={true}>
      {/* <div className="topicon"></div> */}
      <div className="mycontent">
        <div className="modal-close">
          <IconFont type="icon-guanbi" onClick={toggle} />
        </div>
        <GradientTitle title="设备权限"></GradientTitle>
        <div className="Content">
          <DeviceAssign
            key={key}
            selectItems={selectItems}
            onSelectChange={(v: any) => {
              setSelectItems(v);
            }}
          />
        </div>
        <div className="footer">
          <div className="btns">
            <Button key="back" onClick={toggle}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={onFinish} style={{ width: 'max-content' }}>
              确定
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DeviceLimitModal;
