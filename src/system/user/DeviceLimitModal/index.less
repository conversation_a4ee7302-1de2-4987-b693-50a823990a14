.deviceLimitModal-modal {
    .ant-tree {
      background-color: transparent;
    }
    .ant-tree-treenode {
      width: 100%;
      padding: 0;
    }
    .ant-tree .ant-tree-node-content-wrapper {
      flex: 1;
      &:hover {
        background-color: #E8F3FF;
      }
      &.ant-tree-node-selected {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
    .ant-modal-content {
      .ant-modal-footer {
        border: none;
        padding: 1px;
      }
      .ant-modal-body {
        padding: 0px;
        .topicon {
          width: 135px;
          height: 4px;
          margin: 0 auto;
          background-color: var(--primary);
        }
        .mycontent {
        //   padding: 12px 16px 16px;
          height: max-content;
        //   margin: 8px 12px 12px;
          position: relative;
          .modal-close {
            position: absolute;
            right: 16px;
            top: 18px;
            color: var(--gray6);
            cursor: pointer;
          }
          .Content {
            margin-top: 20px;
            color: var(--gray2);
            .ant-select:not(.ant-select-customize-input) .ant-select-selector {
              border-radius: var(--radius1);
            }
          }
          .footer {
            width: 100%;
            margin-top: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            .btns {
              width: max-content;
              display: flex;
              align-items: center;
              .ant-btn-primary {
                margin-left: 16px;
              }
            }
          }
        }
      }
    }
  }
  