import React, { useEffect, useState } from "react";
import { Button, Checkbox, Modal, Tooltip, message } from "antd";
import GradientTitle from "@src/components/GradientTitle";
import service from "@src/service/user";
import Drawer from "@src/components/Drawer";
import { IconFont, cache } from "@cloud-app-dev/vidc";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import { useMemoizedFn, useSafeState } from "ahooks";
import { userInfoAll, updateUser } from "@src/service/ais_app";
import "./index.less";
interface AloLimitModalType {
  isShow: boolean;
  toggle: () => void;
  submit: () => void;
  item: any;
}
const CheckboxGroup = Checkbox.Group;
const AloLimitDrawer = ({ isShow, toggle, submit, item }: AloLimitModalType) => {
  const [resData , setResData]:any = useSafeState({});
  const onFinish = () => {
    // service
    //   .distributeAlgorithm
    // let params = {
    //   ...item,
    //   systemId,
    //   accountId: item?.accountId,
    //   isLocked: !item?.isLocked,
    // };
    // updateUser({
    //     userId: item?.userId,
    //     algorithmIds: checkedList,
    //   })
    updateUser({
      ...resData,
      userDataRight: {
        ...resData.userDataRight,
        algorithmIds: checkedList,
        userId: item?.userId,
      },
    }).then((res) => {
      if (res.code === 0) {
        message.success("分配成功");
        toggle();
        submit();
      } else {
        message.warning("分配失败");
      }
    });
  };
  const [data, setData] = useSafeState<any>([]);
  //查询算法列表
  useEffect(() => {
    service.noCachequeryAlgorithmList({ limit: 1000, offset: 1, algorithmType: null }).then((res) => {
      setData(res);
    });
  }, []);
  const [checkedList, setCheckedList] = useState<any[]>([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const onChange = useMemoizedFn((list: any[]) => {
    setCheckedList(list);
    setIndeterminate(!!list.length && list.length < data.length);
    setCheckAll(list.length ? list.length === data.length : false);
  });
  const onCheckAllChange = useMemoizedFn((e: CheckboxChangeEvent) => {
    setCheckedList(e.target.checked ? data?.map((v: any) => v.id) : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  });
  useEffect(() => {
    if (item?.userId && isShow) {
      userInfoAll(item?.userId).then((res) => {
        // service.getUserAlgorithm(item?.userId).then((res) => {
        if (res.code === 0) {
          const list = res?.data?.userDataRight?.algorithmIds || [];
          setResData(res.data)
          onChange(list);
        }
      });
    }
  }, [item?.userId, isShow]);
  return (
    // <Modal width={684} open={isShow} footer={[]} closable={false} className="aloLimitModal-modal" destroyOnClose={true}>
    <Drawer
      closable={false}
      open={isShow}
      width={907}
      onClose={toggle}
      forceRender
      className="aloLimitModal-modal"
      onOk={onFinish}
      title={
        <div className="userform-wrapper-title">
          <div>算法权限管理</div>
          <div>
            <IconFont type="icon-guanbi" onClick={toggle} />
          </div>
        </div>
      }
      getContainer={() => document.body}
    >
      <div className="mycontent">
        <div className="Content">
          <Checkbox style={{ margin: "0 0 8px 16px" }} indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
            全选
          </Checkbox>
          <CheckboxGroup style={{ width: "100%" }} value={checkedList} onChange={onChange}>
            {data?.map((v: any, i: number) => (
              <>
                {v?.algorithmNoteName?.length > 6 ? (
                  <Tooltip title={v?.algorithmNoteName} placement={"topLeft"}>
                    <Checkbox value={v?.id} key={i}>
                      {v?.algorithmNoteName}
                    </Checkbox>
                  </Tooltip>
                ) : (
                  <Checkbox value={v?.id} key={i}>
                    {v?.algorithmNoteName}
                  </Checkbox>
                )}
              </>
            ))}
          </CheckboxGroup>
        </div>
      </div>
      {/* </Modal> */}
    </Drawer>
  );
};

export default AloLimitDrawer;
