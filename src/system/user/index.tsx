import { Space, Table, Switch, Button, Col, Row, Form, message, Input, Tree } from "antd";
import OperatingBounced from "@src/components/OperatingBounced";
import EditModal from "./EditModal";
import { useAntdTable, useSize, useRequest, useToggle } from "ahooks";
import { IconFont, treeHelper } from "@cloud-app-dev/vidc";
import type { ColumnsType } from "antd/es/table";
import Service from "@src/service/system";
import { UserDataType } from "@src/service/interface";
import { useState, useRef, useEffect } from "react";
import warnImg from "@src/assets/image/warn.png";
import DeviceLimitModal from "./DeviceLimitModal";
import { uuid } from "@cloud-app-dev/vidc";
import AloLimitModal from "./AloLimitModal";
import EditDrawer from "./EditDrawer";
import AloLimitDrawer from "./AloLimitDrawer";
import DeviceLimitDrawer from "./DeviceLimitDrawer";
import Authority from "@src/components/Authority";
import "./index.less";

interface tableType {
  list: UserDataType[];
  total: any;
}

const FormItem = Form.Item;

export default function UserPage() {
  const [selectedKeys, setSelectedKeys]: any = useState([])
  const [treeData, setTreeData]: any = useState([])

  const [dataList, setDataList]: any = useState([])
  const [isAdd, { setLeft, setRight }] = useToggle(false);
  const [showreset, { toggle: resetToggle }] = useToggle(false);
  const [showdelete, { toggle: deleteToggle }] = useToggle(false);
  const [showupdate, { toggle: updateToggle }] = useToggle(false);
  const [showedit, { toggle: editToggle }] = useToggle(false);
  const [showalo, { toggle: aloToggle }] = useToggle(false);
  const [showdevice, { toggle: deviceToggle }] = useToggle(false);
  const [form] = Form.useForm();
  const ref = useRef<HTMLDivElement>(null);
  const size = useSize(ref);
  const [currentItem, setCurrentItem] = useState<any>();
  const [islock, { setLeft: left, setRight: right }] = useToggle(false);
  const [key, setKey] = useState(uuid());
  const statusChange = (value: boolean) => {
    updateToggle();
    if (value) {
      left();
    } else {
      right();
    }
  };
  const columns: ColumnsType<UserDataType> = [
    {
      title: "序号",
      dataIndex: "key",
      key: "key",
      width: 90,
      render: (text, record, index) => index + 1,
    },
    {
      title: "用户名",
      ellipsis: true,
      dataIndex: "loginName",
      key: "loginName",
      width: 240,
      render: (text) => text ?? "-",
    },
    {
      title: "角色",
      ellipsis: true,
      width: 240,
      dataIndex: "roleId",
      key: "roleId",
      render: (roleId = []) => {
        const isList = Array.isArray(roleId);
        if (!isList) return "-";
        const roleListFin: any = roleId.map((v: string) => roleList.find((v2: any) => v === v2.id)?.roleName);
        return (
          <span className="ellipsis" title={roleListFin.join(",")}>
            {roleListFin.length
              ? roleListFin?.map((v: string, index: number) => (
                <span className="user-table-roleId" key={index}>
                  {v}
                </span>
              ))
              : "-"}
          </span>
        );
      },
    },
    {
      title: "姓名",
      ellipsis: true,
      dataIndex: "realName",
      key: "realName",
      width: 140,
      render: (text) => text ?? "-",
    },
    {
      title: "所属局点",
      ellipsis: true,
      dataIndex: "orgId",
      key: "orgId",
      width: 170,
      render: (text) => {
        const roleListFin: any = dataList.filter(((v: any) => text === v.id));;
        return roleListFin.length > 0 ? roleListFin[0].orgName : '-'
      }
    },
    {
      title: "联系电话",
      ellipsis: true,
      dataIndex: "mobilePhone",
      key: "mobilePhone",
      width: 180,
      render: (text) => text ?? "-",
    },
    {
      title: "操作",
      dataIndex: "userId",
      width: 422,
      fixed: "right",
      render: (text, item: any) => {
        return (
          <div className="table-tools">
            <Space size="middle">
              <Authority code="30000412" other={<span></span>}>
                <Switch
                  checked={!item.isLocked}
                  disabled={item?.userType == 1}
                  onClick={() => {
                    setCurrentItem(item);
                    statusChange(item.isLocked);
                    setKey(uuid());
                  }}
                />
              </Authority>
              {item?.userType !== 1 && (
                <>
                  <Authority code="30000412" other={<span></span>}>
                    <Space size="middle">
                      <a
                        style={{ color: "var(--primary)" }}
                        onClick={() => {
                          setCurrentItem(item);
                          deviceToggle();
                          setKey(uuid());
                        }}
                      >
                        设备权限
                      </a>
                      <a
                        style={{ color: "var(--primary)" }}
                        onClick={() => {
                          setCurrentItem(item);
                          aloToggle();
                          setKey(uuid());
                        }}
                      >
                        算法权限
                      </a>
                      <a
                        style={{ color: "var(--primary)" }}
                        onClick={() => {
                          setCurrentItem(item);
                          setLeft();
                          editToggle();
                          setKey(uuid());
                        }}
                      >
                        编辑
                      </a>
                      <a
                        style={{ color: "var(--primary)" }}
                        onClick={() => {
                          setCurrentItem(item);
                          resetToggle();
                        }}
                      >
                        重置密码
                      </a>
                    </Space>
                  </Authority>
                  <Authority code="30000413" other={<span></span>}>
                    {item.userId !== "100100000017" && (
                      <a
                        style={{ color: "var(--primary)" }}
                        onClick={() => {
                          setCurrentItem(item);
                          deleteToggle();
                        }}
                      >
                        删除
                      </a>
                    )}
                  </Authority>
                </>
              )}
            </Space>
          </div>
        );
      },
    },
  ];

  const getOrgTree = (type: any) => {
    Service.device.orgListTree({}).then((res) => {
      const treeList = res.data.list.map((item: any) => ({ ...item, name: item.organizationName, title: item.organizationName, value: item.id }));
      const treeData = treeHelper.computTreeList(treeList);
      setDataList(treeList)
      setTreeData(treeData)
      if (!type && treeData.length > 0) {
        setSelectedKeys([treeData[0].id])
        search.submit();
        // setParams({ ...params, current: 1, offset: 0, orgId: treeData[0].id })
      }
    })
  }

  const onSelect = (selectedKeys: any, info: any) => {
    if (selectedKeys.length > 0) {
      setSelectedKeys(selectedKeys)
      search.submit();
      // setParams({ ...params, current: 1, offset: 0, orgId: selectedKeys[0] })
    }
  };

  useEffect(() => {
    getOrgTree(0)
  }, [])

  /**
   * 获取角色列表
   */
  const { data: roleList = [] } = useRequest(Service.role.queryRoles, {
    defaultParams: [
      {
        limit: 1000,
        offset: 0,
      } as any,
    ],
    refreshDeps: [showedit],
  });
  // @ts-ignore
  const getTableData = ({ current, pageSize }: { current: number; pageSize: number }, formData: any): Promise<tableType> => {
    const params: any = {
      limit: pageSize,
      offset: (current - 1) * pageSize,
      isPagination: true,
      ...formData,
    };

    if (selectedKeys.length > 0) {
      return Service.user
        .queryUsers({ ...params, containSubOrg: true, orgId: selectedKeys[0], userType :4 })
        .then((res) => {
          return {
            list: res?.data?.list,
            total: res?.data?.totalCount,
          };
        })
        .catch((err) => {
          message.warning(err.data.message);
          return {
            list: [],
            total: 0,
          };
        });
    }
  };
  const { tableProps, search } = useAntdTable(getTableData, { defaultPageSize: 10, form });

  //显示分页器跳转
  tableProps.pagination.showQuickJumper = true;
  tableProps.pagination.showSizeChanger = true;
  tableProps.pagination.showTotal = function (total: number) {
    return `共${total}项数据`;
  };
  tableProps.style = { margin: " 0 28px" };
  const deleteOk = () => {
    deleteToggle();
    Service.user
      .deleteUser({ id: currentItem?.userId })
      .then((res) => {
        if (res.code === 0) {
          message.success("删除成功");
          search.submit();
        } else {
          message.warning(res.message);
        }
      })
      .catch((err) => {
        message.warning(err.data.message);
      });
  };
  const updateOk = () => {
    updateToggle();
    Service.user
      .updateUserStatus({ accountId: currentItem?.accountId, isLocked: islock })
      .then((res) => {
        if (res.code === 0) {
          message.success("修改成功");
          search.submit();
        } else {
          message.warning(res.message);
        }
      })
      .catch((err) => {
        message.warning(err.data.message);
      });
  };
  /**重置密码 */
  const resetOk = () => {
    resetToggle();
    Service.user
      .resetPassword(currentItem?.userId)
      .then((res) => {
        if (res.code === 0) {
          message.success("重置密码成功");
        } else {
          message.warning(res.message);
        }
      })
      .catch((err) => {
        message.warning(err.data.message);
      });
  };
  const searchForm = (
    <Form form={form} layout="inline" className="search-form">
      <FormItem name="keywords">
        <Input
          placeholder="请输入用户名、真实姓名、联系电话"
          className="user-filter-btn vidc-Input"
          onChange={search.submit}
          allowClear={false}
          style={{ width: "346px", borderRadius: "4px" }}
          suffix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ cursor: "pointer", fontSize: "12px" }} />}
        />
      </FormItem>
    </Form>
  );

  return (
    <div className="user-wrapper" style={{ display: "flex" }}>
      <div className='user-wrapper-left'>
        <div className='user-wrapper-left-title'>
          用户管理
        </div>
        <div className='user-wrapper-left-center'>
          <Tree
            selectedKeys={selectedKeys}
            onSelect={onSelect}
            treeData={treeData}
            fieldNames={{ title: 'orgName', key: 'id' }}
          />

        </div>
      </div>
      <Row className="user-main">
        <Col className="user-content" ref={ref}>
          <div className="user-content-title"></div>
          <div className="user-search-container">
            {searchForm}
            <Authority code="30000411" other={<span></span>}>
              <Button
                className="role-filter-btn btn-add"
                type="primary"
                style={{ background: "var(--primary)" }}
                onClick={() => {
                  setCurrentItem({});
                  editToggle();
                  setRight();
                }}
              >
                <IconFont type="icon-xitongguanli_zengjiafenzu" />
                新建用户
              </Button>
            </Authority>
          </div>
          <Table rowKey="userId" columns={columns} scroll={{ y: size?.height && size.height - 248 }} {...tableProps} />
        </Col>
      </Row>
      <OperatingBounced
        isShow={showreset}
        title="重置密码确认"
        icon={<img src={warnImg} />}
        onCancel={resetToggle}
        onOk={resetOk}
        content={`确认要重置“${currentItem?.loginName}”的密码吗？`}
      />
      <OperatingBounced
        isShow={showdelete}
        title="删除确认"
        icon={<img src={warnImg} />}
        onCancel={deleteToggle}
        onOk={deleteOk}
        content={`确认要删除“${currentItem?.loginName}”吗？`}
      />
      <OperatingBounced
        isShow={showupdate}
        title="状态修改确认"
        icon={<img src={warnImg} />}
        onCancel={updateToggle}
        onOk={updateOk}
        content={`是否${!currentItem?.isLocked ? "禁用" : "启用"}“${currentItem?.loginName}”该用户？`}
      />
      {/* <DeviceLimitModal isShow={showdevice} toggle={deviceToggle} submit={search.submit} item={currentItem} /> */}
      {/* <AloLimitModal isShow={showalo} toggle={aloToggle} submit={search.submit} item={currentItem} /> */}
      {/* <EditModal item={currentItem} isAdd={isAdd} roleList={roleList || []} isShow={showedit} toggle={editToggle} submit={search.submit} /> */}
      <EditDrawer key={key} selectedKeys={selectedKeys} item={currentItem} isAdd={isAdd} roleList={roleList || []} isShow={showedit} toggle={editToggle} submit={search.submit} />
      <AloLimitDrawer key={key} isShow={showalo} toggle={aloToggle} submit={search.submit} item={currentItem} />
      <DeviceLimitDrawer key={key} isShow={showdevice} toggle={deviceToggle} submit={search.submit} item={currentItem} />
    </div>
  );
}
