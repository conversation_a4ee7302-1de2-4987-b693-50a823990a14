.device-select {
  display: flex;
  min-height: 500px;
  .ant-tree-node-selected{
    background-color: #E8F3FF  !important;
  }
  .css-kk6zbp {
    contain-intrinsic-size: none !important;
  }
  .assign-tools {
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .ant-btn {
      width: 48px;
      height: 52px;
      padding: 0;
      margin-bottom: 20px;
    }
  }
  .select-content-part {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
    border: 1px solid #dcdcdc !important;
    &.org-tree-select {
      .tree-content {
        overflow: auto;
        height: 100%;
        padding: 16px 10px;
        .ant-tree-title1111 {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
        .site-tree-search-value {
          color: var(--danger);
        }
        .search-box {
          margin-bottom: 16px;
        }
      }
    }
    &.devive-list {
      border-left: none;
      display: flex;
      flex-direction: column;
      border: 1px solid #dcdcdc;
      .lm-c-list-wrapper {
        border: none;
        flex: 1;
      }
      & > div:nth-child(3) {
        border: none;
      }
    }
  }
  .l-c-dynamic-list-layout {
    border: none;
  }
  .tree-title {
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    background-color: #f3f3f3;
    border-bottom: 1px solid #dcdcdc;
    color: rgba(0, 0, 0, 0.9);
    /* Title/Small */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
    & > span {
      cursor: pointer;
    }
    .clear {
      color: #165dff;
      /* Body/Medium */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-right: 16px;
      cursor: pointer;
    }
  }
  .device-list-item {
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 隐藏溢出部分 */
    text-overflow: ellipsis;
    color: #000;
    padding: 0 10px;
    line-height: 2.2em;
    .ant-checkbox-wrapper {
      display: flex;
      align-items: center;
      overflow: hidden;
    }
    .ant-checkbox {
      position: relative;
      top: -1px;
    }
    .ant-checkbox + span {
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;
      & > span {
        white-space: nowrap;
        text-overflow: ellipsis;
        flex: 1;
        overflow: hidden;
      }
    }
  }
  .device-select-list-item {
    display: flex;
    overflow: hidden;
    align-items: center;
    padding: 0 10px;
    height: 30px;
    position: relative;
    color: rgba(0, 0, 0, 0.9);
    /* Body/Medium */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    span {
      white-space: nowrap;
      text-overflow: ellipsis;
      flex: 1;
      overflow: hidden;
    }
    .delete-icon {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
      display: none;
    }
    &:hover {
      .delete-icon {
        display: inline-block;
      }
    }
  }
  .list-from {
    padding: 16px 10px;
    padding-bottom: 0;
    .all-check {
      margin-bottom: 8px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--gray8);
    }
    .search-box {
      margin-bottom: 16px;
    }
  }
}
