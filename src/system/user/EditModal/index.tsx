import { Button, Form, Input, Select, Switch, message, Modal } from "antd";
import Service from "@src/service/system";
import GradientTitle from "@src/components/GradientTitle";
import { useUpdateEffect } from "ahooks";
import { IconFont, cache } from "@cloud-app-dev/vidc";
import { useEffect, useMemo } from "react";
import "./index.less";
interface Bounced {
  isShow: boolean;
  cancel?: boolean;
  loading?: boolean;
  toggle: () => void;
  roleList: any[];
  isAdd: boolean;
  submit: () => void;
  item: any;
}
const EditModal = ({ loading = false, isShow, cancel = true, roleList, toggle, isAdd, submit, item }: Bounced) => {
  const [form] = Form.useForm();
  useEffect(() => {
    if (!isAdd) {
      form.setFieldsValue(item);
    } else {
      form.resetFields();
    }
  }, [isShow]);
  const onFinish = () => {
    form.validateFields().then((res) => {
      toggle();
      const { systemId } = cache.getCache("userInfo", "session");
      if (!isAdd) {
        Service.user
          .changeUser({ ...res, systemId, accountId: item?.accountId, userId: item?.userId, isLocked: !item?.isLocked })
          .then((res) => {
            if (res.code === 0) {
              message.success("修改成功");
              submit();
            } else {
              message.warning(res.message);
            }
          })
          .catch((err) => {
            message.warning(err.data.message);
          });
      } else {
        Service.user
          .addUser({ ...res, systemId })
          .then((res) => {
            if (res.code === 0) {
              message.success("新增成功");
              submit();
            } else {
              message.warning(res.message);
            }
          })
          .catch((err) => {
            message.warning(err.data.message);
          });
      }
    });
  };
  useUpdateEffect(() => {
    if (!loading) {
      toggle();
    }
  }, [loading]);
  const FormSwitch = ({ value, onChange }: { value?: boolean; onChange?: (v?: boolean) => void }) => {
    const checked = useMemo(() => !value, [value]);
    return (
      <Switch
        defaultChecked={true}
        checked={checked}
        onChange={(v) => {
          onChange && onChange(!v);
        }}
      />
    );
  };
  return (
    <Modal width={500} open={isShow} footer={[]} closable={false} forceRender className="user-edit-modal" destroyOnClose={true}>
      {/* <div className="topicon"></div> */}
      <div className="mycontent">
        <div className="modal-close">
          <IconFont type="icon-guanbi" onClick={toggle} />
        </div>
        <GradientTitle title={isAdd ? "新增用户" : "编辑用户"}></GradientTitle>
        <div className="Content">
          <Form form={form} className="user-form" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
            <Form.Item label="用户名" name="loginName" rules={[{ required: true, message: "登录名称必须填写" }]}>
              <Input placeholder="请输入用户名" />
            </Form.Item>
            <Form.Item
              label="真实姓名"
              name="realName"
              rules={[{ pattern: /^([\u4e00-\u9fa5]{2,10}|[a-zA-Z.\s]{2,10})$/, message: "请输入2-10位的中文或英文" }]}
            >
              <Input placeholder="请填写真实姓名" />
            </Form.Item>
            <Form.Item label="联系电话" name="mobilePhone" rules={[{ pattern: /^(1)\d{10}$/, message: "请输入正确手机号码" }]}>
              <Input placeholder="请填写手机号码" />
            </Form.Item>
            <Form.Item label="角色" name="roleId" rules={[{ required: true, message: "用户角色必须填写" }]}>
              <Select placeholder="请选择用户角色" style={{ width: "100%" }} mode="multiple" optionFilterProp="children">
                {roleList?.map((item: any, index: any) => (
                  <Select.Option key={index} value={item.id}>
                    {item.roleName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="状态" name="isLocked">
              <FormSwitch />
            </Form.Item>
          </Form>
        </div>
        <div className="footer">
          <div className="btns">
            {cancel && (
              <Button key="back" onClick={toggle}>
                取消
              </Button>
            )}
            <Button key="submit" type="primary" onClick={onFinish} loading={loading} style={{ width: "max-content" }}>
              确定
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};
export default EditModal;
