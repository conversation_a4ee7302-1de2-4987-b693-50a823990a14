import { Button, Form, Input, Select, Switch, message, Row, Col, Divider, Checkbox, Tooltip } from "antd";
import Service from "@src/service/system";
import Drawer from "@src/components/Drawer";
import { useUpdateEffect, useMemoizedFn } from "ahooks";
import { IconFont, cache } from "@cloud-app-dev/vidc";
import { useEffect, useMemo, useState } from "react";
import DeviceAssign from "../DeviceAssign";
import service from "@src/service/user";
import { userInfoAll, updateUser, userCreate } from "@src/service/ais_app";
import "./index.less";
interface Bounced {
  isShow: boolean;
  cancel?: boolean;
  loading?: boolean;
  toggle: () => void;
  roleList: any[];
  isAdd: boolean;
  submit: () => void;
  item: any;
  selectedKeys?: any[];
}
const CheckboxGroup = Checkbox.Group;
const EditModal = ({ loading = false, isShow, cancel = true, roleList, toggle, isAdd, submit, item, selectedKeys }: Bounced) => {
  const [form] = Form.useForm();
  const [device, setDevice] = useState(false);
  const [alo, setalo] = useState(false);
  const [checkedList, setCheckedList] = useState([]);
  const [data, setData] = useState<any>([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const [selectItems, setSelectItems] = useState<any[]>([]);
  useEffect(() => {
    if (!alo) {
      setCheckAll(false);
      setCheckedList([]);
      setIndeterminate(false);
    }
  }, [alo]);
  useEffect(() => {
    if (!device) {
      setSelectItems([]);
    }
  }, [device]);
  useEffect(() => {
    if (!isAdd) {
      form.setFieldsValue(item);
    } else {
      form.resetFields();
      setCheckAll(false);
      setCheckedList([]);
      setIndeterminate(false);
      setSelectItems([]);
      setDevice(false);
      setalo(false);
    }
  }, [isShow, isAdd]);
  const onFinish = () => {
    form.validateFields().then((res) => {
      const { systemId } = cache.getCache("userInfo", "session");
      if (!isAdd) {
        // Service.user
        //   .changeUser({ ...res, systemId, accountId: item?.accountId, userId: item?.userId, isLocked: !item?.isLocked })
        let params = {
          ...res,
          systemId,
          accountId: item?.accountId,
          isLocked: item?.isLocked,
          userId: item?.userId,
        };
        if(item.isLocked ===res.isLocked){

        }else{
          params.isLocked = !params.isLocked
        }
        updateUser({
          userDataRight: {
            algorithmIds: alo ? checkedList : [],
            userId: item?.userId,
            deviceBuzGroupIds: device ? selectItems : [],
          },
          user: {
            ...params,
          },
        })
          .then((res) => {
            if (res.code === 0) {
              message.success("修改成功");
              toggle();
              submit();
            } else {
              message.warning(res.message);
            }
          })
          .catch((err) => {
            message.warning(err.data.message);
          });
      } else {
        // Service.user
        //   .addUser({ ...res, systemId })
        let params = {
          ...res,
          systemId,
          accountId: item?.accountId,
          isLocked: item?.isLocked,
          orgId: selectedKeys && selectedKeys.length > 0 ? selectedKeys[0]: '',
          // userId: item?.userId,
        };
        if(item.isLocked ===res.isLocked){

        }else{
          params.isLocked = !params.isLocked
        }
        userCreate({
          userDataRight: {
            algorithmIds: alo ? checkedList : [],
            userId: item?.userId,
            deviceBuzGroupIds: device ? selectItems : [],
          },
          user: {
            ...params,
          },
        })
          .then((res) => {
            if (res.code === 0) {
              message.success("新增成功");
              toggle();
              submit();
            } else {
              message.warning(res.message);
            }
          })
          .catch((err) => {
            message.warning(err.data.message);
          });
      }
    });
  };
  useUpdateEffect(() => {
    if (!loading) {
      toggle();
    }
  }, [loading]);
  const FormSwitch = ({ value, onChange }: { value?: boolean; onChange?: (v?: boolean) => void }) => {
    const checked = useMemo(() => !value, [value]);
    return (
      <Switch
        defaultChecked={true}
        checked={checked}
        onChange={(v) => {
          onChange && onChange(!v);
        }}
      />
    );
  };
  const onChange = useMemoizedFn((list) => {
    setCheckedList(list);
    setIndeterminate(!!list.length && list.length < data.length);
    setCheckAll(list.length ? list.length === data.length : false);
  });
  const onCheckAllChange = useMemoizedFn((e) => {
    setCheckedList(e.target.checked ? data?.map((v: any) => v.id) : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  });
  //查询算法列表
  useEffect(() => {
    service.noCachequeryAlgorithmList({ limit: 1000, offset: 1, algorithmType: null }).then((res) => {
      setData(res);
    });
  }, []);
  useEffect(() => {
    if (item?.userId && isShow) {
      // service.getUserAlgorithm(item?.userId).then((res) => {
      userInfoAll(item?.userId).then((res) => {
        if (res.code === 0) {
          const list = res?.data?.userDataRight?.algorithmIds || [];
          const selectList = res?.data?.userDataRight?.deviceBuzGroupIds || [];
          onChange(list);
          setSelectItems(selectList.map((item: any) => item));
          if (selectList?.length) setDevice(true);
          if (list?.length) setalo(true);
        }
      });
    }
  }, [item?.userId, isShow]);
  return (
    <Drawer
      closable={false}
      open={isShow}
      width={907}
      onClose={toggle}
      forceRender
      className="user-edit-drawer"
      onOk={onFinish}
      {...(cancel
        ? {}
        : {
            footer: (
              <div style={{ width: "100%", display: "flex", justifyContent: "flex-end" }}>
                <Button key="back" onClick={toggle}>
                  取消
                </Button>
              </div>
            ),
          })}
      title={
        <div className="userform-wrapper-title">
          <div>{isAdd ? "新增" : "编辑"}用户</div>
          <div>
            <IconFont type="icon-guanbi" onClick={toggle} />
          </div>
        </div>
      }
      getContainer={() => document.body}
    >
      <div className="mycontent">
        <div className="Content">
          <div className="content-title">基本信息</div>
          <Form layout="vertical" form={form} className="user-form" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
            <Row>
              <Col flex={9}>
                <Form.Item
                  label="用户名"
                  name="loginName"
                  rules={[
                    {
                      required: true,
                      // message: "登录名称必须填写",
                      pattern: /^[a-zA-Z0-9]+$/,
                      validator: (e, val) => {
                        const value = val.replace(/\s+/g, "");
                        if (!e.pattern) {
                          return Promise.resolve();
                        }
                        if(!value?.length){
                          return Promise.reject('登录名称必须填写')
                        }
                        if (e.pattern.test(value)) {
                          return Promise.resolve();
                        }
                        return Promise.reject('仅支持输入字母和数字');
                      },
                    },
                  ]}
                >
                  <Input placeholder="请输入用户名" style={{ width: 393 }} maxLength={20} />
                </Form.Item>
              </Col>
              <Col flex={9}>
                <Form.Item label="角色" name="roleId" rules={[{ required: true, message: "用户角色必须填写" }]}>
                  <Select placeholder="请选择用户角色" style={{ width: 393 }} mode="multiple" optionFilterProp="children">
                    {roleList?.map((item: any, index: any) => (
                      <Select.Option key={index} value={item.id}>
                        {item.roleName}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col flex={9}>
                <Form.Item
                  label="真实姓名"
                  name="realName"
                  rules={[{ pattern: /^([\u4e00-\u9fa5]{2,10}|[a-zA-Z.\s]{2,10})$/, message: "请输入2-10位的中文或英文" }]}
                >
                  <Input placeholder="请填写真实姓名" style={{ width: 393 }} />
                </Form.Item>
              </Col>
              <Col flex={9}>
                <Form.Item label="联系电话" name="mobilePhone" rules={[{ pattern: /^(1)\d{10}$/, message: "请输入正确手机号码" }]}>
                  <Input placeholder="请填写手机号码" style={{ width: 393 }} />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item label="角色状态" name="isLocked">
              <FormSwitch />
            </Form.Item>
          </Form>
          <Divider style={{ color: "#E7E7E7" }} />
          <div className="content-title">
            设备权限
            <Switch
              checked={device}
              style={{ marginLeft: 24 }}
              onChange={(v) => {
                setDevice(v);
              }}
            />
          </div>
          {device ? (
            <DeviceAssign
              selectItems={selectItems}
              onChange={(v: any) => {
                setSelectItems(v);
              }}
            />
          ) : null}
          <Divider style={{ color: "#E7E7E7" }} />
          <div className="content-title">
            算法权限
            <Switch
              checked={alo}
              style={{ margin: "0px 24px" }}
              onChange={(v) => {
                setalo(v);
              }}
            />
            {alo ? (
              <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
                全选
              </Checkbox>
            ) : null}
          </div>
          {alo ? (
            <div className="content-alo">
              <CheckboxGroup style={{ width: "100%" }} value={checkedList} onChange={onChange}>
                {data?.map((v: any, i: number) => (
                  <>
                    {v?.algorithmNoteName?.length > 6 ? (
                      <Tooltip title={v?.algorithmNoteName} placement={"topLeft"}>
                        <Checkbox value={v?.id} key={i}>
                          {v?.algorithmNoteName}
                        </Checkbox>
                      </Tooltip>
                    ) : (
                      <Checkbox value={v?.id} key={i}>
                        {v?.algorithmNoteName}
                      </Checkbox>
                    )}
                  </>
                ))}
              </CheckboxGroup>
            </div>
          ) : null}
        </div>
      </div>
    </Drawer>
  );
};
export default EditModal;
