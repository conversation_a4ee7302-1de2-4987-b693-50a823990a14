.user-edit-drawer {
  .content-title {
    color: #000;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
  }
  .content-alo {
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: var(--radius1);
    }
    .ant-checkbox-group {
      flex-wrap: wrap;
      .ant-checkbox-wrapper {
        width: 120px;
        margin: 8px 16px;
        font-size: 12px;
        & > span:last-child {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .userform-wrapper-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .topicon {
    width: 135px;
    height: 4px;
    margin: 0 auto;
  }
  .mycontent {
    // padding: 12px 16px 16px;
    height: max-content;
    // margin: 8px 12px 12px;
    position: relative;

    .Content {
      // margin-top: 20px;

      .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        border-radius: var(--radius1);
      }
    }
  }
}
