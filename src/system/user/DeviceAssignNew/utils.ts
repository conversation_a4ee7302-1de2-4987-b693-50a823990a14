import { cache } from '@cloud-app-dev/vidc';
import { Service } from '@cloud-app-dev/vidc';
import { DataNode } from 'antd/lib/tree';
import { uniq } from 'lodash-es';
export type GroupItemType = {
  code: string;
  name: string;
};

export interface TreeItemType {
  code: string;
  name: string;
  offLine: number;
  onLine: number;
}

export function getHeader() {
  return { Authorization: cache.getCache('token', 'session') };
}

export async function fetchGroup(): Promise<GroupItemType[]> {
  const res = await Service.http({ url: '/api/device/group/v2', headers: getHeader() });
  return res.data;
}

export function getGroups() {
  return Service.http({
    headers: getHeader(),
    // url: '/api/dvia-app-scene-server/device/deviceOriginGroup/list/query',
    url:'/api/dvia-app-scene-server/device/deviceBuzGroup/deviceBuzGroupList/query'
  }).then((res) => res.data ?? []);
}

export async function fetchList(times: number[] = [], queryMatch: string, userId?: string, rightLikeCode?: string) {
  const res = await Service.http({
    url: '/api/device/v2/user',
    headers: getHeader(),
    params: { userId, rightLikeCode, fromCreateTime: times[0], toCreateTime: times[1], queryMatch },
  });
  return res.data;
}

export function getDevices(data: { groupId?: string; limit: number; offset: number; keywords?: string; type?: any; state?: any }) {
  return Service.http({
    headers: getHeader(),
    url: '/api/dvia-app-scene-server/device/systemDevice/deviceList/query',
    data,
    method: 'post',
  }).then((res) => res.data ?? []);
}

export function getTreeIdWithKeyword(treeMap: { [key: string]: ITreeItem }, keyword: string) {
  const arr = [] as string[];
  if (!keyword) {
    return arr;
  }
  const getParentCode = (code: string, arr2 = [] as string[]) => {
    if (treeMap[code]) {
      const item2 = treeMap[code];
      arr2.push(code);
      if (item2.parentId && treeMap[item2.parentId]) {
        getParentCode(item2.parentId);
      }
    }
    return arr2;
  };
  const data = Object.values(treeMap);
  data.forEach((item: ITreeItem) => {
    if (item.groupName.indexOf(keyword)) {
      arr.push(...getParentCode(item.id));
    }
  });

  return uniq(arr);
}

export function getTreeMap(treeData: ITreeItem[]) {
  const map = {} as any;
  treeData.forEach((item: ITreeItem) => {
    map[item.id] = item;
  });
  return map;
}
export type ListItem = {
  cid: string;
  accessType: string;
  lat: string;
  lng: string;
  localRecord: number;
  name: string;
  ptz: number;
  state: number;
  storageVideo: number;
  type: number;
};
export interface ITreeItem extends DataNode {
  id: string;
  groupName: string;
  onlineCount: number;
  totalCount: number;
  parentId: string;
  children?: ITreeItem[];
  name?: JSX.Element;
}
