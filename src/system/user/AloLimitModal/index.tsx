import React, { useEffect, useState } from 'react';
import { Button, Checkbox, Modal, Tooltip, message } from 'antd';
import  GradientTitle  from '@src/components/GradientTitle';
import service from '@src/service/user';
import { IconFont } from '@cloud-app-dev/vidc';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import { useMemoizedFn, useSafeState } from 'ahooks';
import './index.less';
interface AloLimitModalType {
  isShow: boolean;
  toggle: () => void;
  submit: () => void;
  item: any;
}
const CheckboxGroup = Checkbox.Group;
const AloLimitModal = ({ isShow, toggle, submit, item }: AloLimitModalType) => {
  const onFinish = () => {
    toggle();
    service
      .distributeAlgorithm({
        userId: item?.userId,
        algorithmIds: checkedList,
      })
      .then((res) => {
        if (res.code === 0) {
          message.success('分配成功');
          submit();
        } else {
          message.warning('分配失败');
        }
      });
  };
  const [data, setData] = useSafeState<any>([]);
  //查询算法列表
  useEffect(() => {
    service.noCachequeryAlgorithmList({ limit: 1000, offset: 1, algorithmType: null }).then((res) => {
      setData(res);
    });
  }, []);
  const [checkedList, setCheckedList] = useState<any[]>([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const onChange = useMemoizedFn((list: any[]) => {
    setCheckedList(list);
    setIndeterminate(!!list.length && list.length < data.length);
    setCheckAll(list.length === data.length);
  });
  const onCheckAllChange = useMemoizedFn((e: CheckboxChangeEvent) => {
    setCheckedList(e.target.checked ? data?.map((v: any) => v.id) : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  });
  useEffect(() => {
    if (item?.userId && isShow) {
      service.getUserAlgorithm(item?.userId).then((res) => {
        if (res.code === 0) {
          onChange(res?.data?.algorithmIds || []);
        }
      });
    }
  }, [item?.userId, isShow]);
  return (
    <Modal width={684} open={isShow} footer={[]} closable={false} className="aloLimitModal-modal" destroyOnClose={true}>
      {/* <div className="topicon"></div> */}
      <div className="mycontent">
        <div className="modal-close">
          <IconFont type="icon-guanbi" onClick={toggle} />
        </div>
        <GradientTitle title="算法权限"></GradientTitle>
        <div className="Content">
          <Checkbox style={{ margin: '0 0 8px 16px' }} indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
            全选
          </Checkbox>
          <CheckboxGroup style={{ width: '100%' }} value={checkedList} onChange={onChange}>
            {data?.map((v: any, i: number) => (
              <>
                {v?.algorithmNoteName?.length > 6 ? (
                  <Tooltip title={v?.algorithmNoteName} placement={'topLeft'}>
                    <Checkbox value={v?.id} key={i}>
                      {v?.algorithmNoteName}
                    </Checkbox>
                  </Tooltip>
                ) : (
                  <Checkbox value={v?.id} key={i}>
                    {v?.algorithmNoteName}
                  </Checkbox>
                )}
              </>
            ))}
          </CheckboxGroup>
        </div>
        <div className="footer">
          <div className="btns">
            <Button key="back" onClick={toggle}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={onFinish} style={{ width: 'max-content' }}>
              确定
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default AloLimitModal;
