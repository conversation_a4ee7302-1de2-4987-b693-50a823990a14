.aloLimitModal-modal {
    .ant-modal-content {
      .ant-modal-footer {
        border: none;
        padding: 1px;
      }
      .ant-modal-body {
        padding: 0px;
        .topicon {
          width: 135px;
          height: 4px;
          margin: 0 auto;
          background-color: var(--primary);
        }
        .mycontent {
        //   padding: 12px 16px 16px;
          height: max-content;
        //   margin: 8px 12px 12px;
          position: relative;
          .modal-close {
            position: absolute;
            right: 16px;
            top: 18px;
            color: var(--gray6);
            cursor: pointer;
          }
          .Content {
            margin-top: 20px;
            .ant-select:not(.ant-select-customize-input) .ant-select-selector {
              border-radius: var(--radius1);
            }
            .ant-checkbox-group {
              flex-wrap: wrap;
              .ant-checkbox-wrapper {
                width: 120px;
                margin: 8px 16px;
                font-size: 12px;
                & > span:last-child {
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
              }
            }
          }
          .footer {
            width: 100%;
            margin-top: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            .btns {
              width: max-content;
              display: flex;
              align-items: center;
              .ant-btn-primary {
                margin-left: 16px;
              }
            }
          }
        }
      }
    }
  }
  