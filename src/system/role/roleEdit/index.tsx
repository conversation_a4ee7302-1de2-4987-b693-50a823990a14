import { Form, Input, message, Tree, Checkbox } from "antd";
import { uniq } from "lodash-es";
import { useEffect, useMemo, useState } from "react";
import GradientTitle from "@src/components/GradientTitle";
import { useToggle, useRequest, useSafeState, useThrottleEffect, useSize, useAntdTable } from "ahooks";
import { cache, treeHelper, Config, IconFont } from "@cloud-app-dev/vidc";
import Service from "@src/service/system";
import { RoleDataType } from "@src/service/interface";
import Drawer from "@src/components/Drawer";
import RoleTree from "./roleTree";

import "./index.less";
const getCleckedKeys = (list: any) => {
  let arr: any = [];
  list.forEach((item: any) => {
    if (item.checked) {
      arr.push(item.id);
      if(item.parentId)arr.push(item.id,item.parentId);
    }
    if (item.children && item.children.length) {
      arr = arr.concat(getCleckedKeys(item.children));
    }
  });
  return arr;
};

export default function RoleEdit({
  isShow = false,
  toggle,
  isAdd,
  item,
  submit,
}: {
  submit: () => void;
  item?: RoleDataType;
  isShow: boolean;
  toggle: () => void;
  isAdd: boolean;
}) {
  const [form] = Form.useForm();
  const [checkedKeys, setCheckedKeys] = useState<(string | number)[]>([]);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [roleData, setRoleData]: any = useState({
    tree: [],
    value: [],
    allLen: 0,
    isShow: false,
  });
  // const indeterminate = true;
  const indeterminate = roleData?.value.length ? (checkAll ? false : Config.features.length <= roleData?.value.length ? false : true) : false;
  const onCheckAllChange = (e: any) => {
    const currCheck = e.target.checked;
    setCheckAll(currCheck);
    if (currCheck) {
      const AllRole: any = Config.features.map((v: any) => {
        return {
          ...v,
          text: v.name,
          checked: true,
        };
      });
      const AllRoleIds: any = Config.features.map((v: any) => v.id);
      let defaultTree: any = treeHelper.computTreeList(AllRole, undefined, undefined, true);
      // setCheckedKeys(AllRoleIds);
      setRoleData({
        ...roleData,
        tree: defaultTree,
        value: AllRoleIds ?? ([] as any),
      });
    } else {
      const AllRole: any = Config.features.map((v: any) => {
        return {
          ...v,
          text: v.name,
          checked: false,
        };
      });
      let defaultTree: any = treeHelper.computTreeList(AllRole, undefined, undefined, true);
      // setCheckedKeys([]);
      setRoleData({
        ...roleData,
        tree: defaultTree,
        value: [],
      });
    }
  };
  const onFinish:any = () => {
    const { value: checkedKeys } = roleData;
    form.validateFields().then((res) => {
      if (checkedKeys.length > 0) {
        toggle();
        const roleId = item?.id;
        const { systemId } = cache.getCache("userInfo", "session") || {};

        if (roleId) {
          Service.role
            .changeRole({ ...res, id: roleId, functionIds: checkedKeys, systemId })
            .then((res) => {
              if (res.code === 0) {
                message.success("编辑成功");
                submit();
              } else {
                message.warning(res.message);
              }
            })
            .catch((err) => {
              message.warning(err.data.message);
            });
        } else {
          Service.role
            .addRole({ ...res, functionIds: checkedKeys, systemId })
            .then((res) => {
              if (res.code === 0) {
                message.success("新增成功");
                submit();
              } else {
                message.warning(res.message);
              }
            })
            .catch((err) => {
              message.warning(err.data.message);
            });
        }
      } else {
        message.warning("权限信息不能为空");
      }
    });
  };
  const { data, run } = useRequest(onFinish, {
    debounceWait: 300,
    manual: true
  });
  useEffect(() => {
    // 当前所有权限
    let AllRole = Config.features;
    // 当前有的权限
    let currentRole = item?.functionIds ?? ([] as any);
    // if(currentRole?.length){
    AllRole = AllRole.map((v: any) => {
      return {
        ...v,
        text: v.name,
        checked: currentRole.includes(v.id),
      };
    });
    let defaultTree: any = treeHelper.computTreeList(AllRole, undefined, undefined, true);
    setRoleData({
      ...roleData,
      tree: defaultTree,
      allLen: AllRole.length,
      value: item?.functionIds ?? ([] as any),
    });
    setCheckedKeys(item?.functionIds ?? ([] as any));
    if (item?.functionIds && Config.features.length <= item?.functionIds?.length) {
      setCheckAll(true);
    } else {
      setCheckAll(false);
    }
    form.setFieldsValue(item);
    // }
    // if (isAdd) {
    //   form.resetFields();
    //   setCheckedKeys([]);
    //   setCheckAll(false);
    //   setRoleData({
    //     ...roleData,
    //     tree: defaultTree,
    //     value: [],
    //     // allLen: 0,
    //     isShow: false,
    //   });
    // }
  }, [isShow, item]);
  useEffect(() => {
    if(roleData.tree.length){
      const AllClecked = uniq(getCleckedKeys(roleData.tree));
      setRoleData({
        ...roleData,
        value: AllClecked,
      });
    }
  }, [checkedKeys]);

  return (
    <Drawer
      closable={false}
      open={isShow}
      width={900}
      onClose={toggle}
      forceRender
      onOk={run}
      title={
        <div className="userform-wrapper-title">
          <div>{isAdd ? "新增角色" : "编辑角色"}</div>
          <div>
            <IconFont type="icon-guanbi" onClick={toggle} />
          </div>
        </div>
      }
      getContainer={() => document.body}
    >
      <div className="userform-wrapper">
        <Form layout="vertical" form={form} className="user-form" labelCol={{ span: 5 }} wrapperCol={{ span: 19 }}>
          <div className="info basic">
            <div className="title">基本信息</div>

            <Form.Item
              label="角色名称"
              name="roleName"
              rules={[
                { required: true, message: "角色名称必须填写" },
                { max: 20, message: "角色名称最大长度20" },
                {
                  pattern: /^[^\s]*$/,
                  message: '禁止输入空格',
                }
        
              ]}
              getValueFromEvent={(event) => {
                return event.target.value.replace(/\s+/g,"")
              }}
            >
              <Input style={{ width: "835px" }} placeholder="请输入角色名称" />
            </Form.Item>

            <Form.Item
              label="角色描述"
              name="roleDesc"
              rules={[
                {
                  required: false,
                },
                {
                  max: 150,
                  message: "最大长度是150",
                },
                {
                  pattern: /^[^\s]*$/,
                  message: '禁止输入空格',
                }
              ]}
              getValueFromEvent={(event) => {
                return event.target.value.replace(/\s+/g,"")
              }}
            >
              {/* <Input.TextArea rows={4} placeholder="请填写角色描述" /> */}
              <Input style={{ width: "835px" }} placeholder="请填写角色描述" />
            </Form.Item>
          </div>
          <div className="info identity">
            <div className="title">
              权限信息
              {/* <span
                onClick={() => setCheckedKeys(Config.features.map((v) => v.id))}
                style={{ fontWeight: "normal", fontSize: 12, color: "var(--primary-light)", cursor: "pointer", marginLeft: "16px" }}
              >
                全选
              </span> */}
            </div>
            <div className="auth">
              {/* <div style={{ padding: "20px 20px 20px 58px", display: "flex" }}>
                <Tree
                  checkable
                  selectable={false}
                  fieldNames={{ title: "text", key: "id" }}
                  checkStrictly
                  defaultExpandAll
                  onCheck={onCheck}
                  checkedKeys={checkedKeys}
                  treeData={treeData as any}
                />
              </div> */}
              <div style={{ margin: "24px 0px 15px 0px" }}>
                <Checkbox
                  indeterminate={indeterminate}
                  onChange={onCheckAllChange}
                  checked={roleData.allLen && roleData.allLen === roleData?.value.length}
                >
                  全选
                </Checkbox>
              </div>
              <div className="userform-wrapper-auth-tree">
                <RoleTree
                  setCheckedKeys={setCheckedKeys}
                  roleData={roleData}
                  setRoleData={setRoleData}
                  checkAll={checkAll}
                  setCheckAll={setCheckAll}
                  getCleckedKeys={getCleckedKeys}
                />
              </div>
            </div>
          </div>
        </Form>
      </div>
    </Drawer>
  );
}
