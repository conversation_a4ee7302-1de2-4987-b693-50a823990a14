import React, { useEffect } from "react";
import { Checkbox } from "antd";
import { uniq } from "lodash-es";
import "./index.less";
interface treeItemType {
  id: string;
  text: string;
  parentId: string;
  checked: boolean;
  indeterminate?: boolean;
  children?: Array<treeItemType>;
}
interface roleDataType {
  tree: Array<treeItemType>;
  value: Array<string>;
  allLen: number;
}

interface roleTreeType {
  roleData: roleDataType;
  setRoleData: (val: any) => void;
  checkAll: boolean;
  setCheckAll: (val: boolean) => void;
}
function RoleTree({ roleData, setRoleData, checkAll, setCheckAll, setChecked<PERSON>eys, getCleckedKeys }: any) {
  useEffect(() => {
    if (roleData.value.length > 0 && !checkAll) {
      for (const item of roleData.value) {
        defaultChecked(roleData.tree, item, true);
      }
      roleData.value = uniq(roleData.value);
      setCheckedKeys(roleData.value);
      setRoleData(JSON.parse(JSON.stringify(roleData)));
    }
    // eslint-disable-next-line
  }, [checkAll]);

  //递归所有菜单状态
  const defaultChecked = (list: Array<any>, code: string, checked: boolean) => {
    for (const item of list) {
      if (item.id === code) {
        item.checked = checked;
        if (item.children && item.children.length > 0) {
          const arr: Array<boolean> = item.children.map((v: any) => v.checked);
          if (arr.includes(true) && arr.includes(false)) {
            item.indeterminate = true;
            item.checked = checked;
          } else {
            item.indeterminate = arr.includes(true);
            item.children.map((v: any) => {
              v.indeterminate = false;
              return v;
            });
          }
        }
        return true;
      }
      if (Array.isArray(item.children) && item.children.length > 0) {
        const bool = defaultChecked(item.children, code, checked);
        if (bool) {
          setChecked(item);
          return true;
        }
      }
    }
  };
  const checkChange = (item: treeItemType, checked: boolean, refresh = false) => {
    if (!item.children || item.children.length === 0) {
      const isCode = roleData.value.includes(item.id);
      if (checked) {
        if (isCode) return;
        roleData.value.push(item.id);
        roleData.value = [...new Set(roleData.value)];
        // .filter((v) => v !== 30000300);
        if (roleData.value.length >= roleData.allLen) {
          setCheckAll(true);
          refresh = true;
        }
      } else {
        let arr = roleData.value.filter((v: any) => v !== item.id);
        roleData.value = [...new Set(arr)];
        if (roleData.value.length === 0 || checkAll) {
          setCheckAll(false);
          refresh = false;
        }
      }
    }
    item.checked = checked;
    if (item.children && item.children.length > 0) {
      if (checked) {
        const isCode = roleData.value.includes(item.id);
        if (isCode) return;
        roleData.value.push(item.id);
        roleData.value = [...new Set(roleData.value)];
      } else {
        let arr = roleData.value.filter((v: any) => v !== item.id);
        roleData.value = [...new Set(arr)];
      }
      for (const iterator of item.children) {
        checkChange(iterator, checked);
      }
    }
    if (refresh) {
      roleData.value = uniq(roleData.value);
      item.parentId && defaultChecked(roleData.tree, item.id, checked);
      // defaultChecked(roleData.tree, item.id, checked);
      setCheckedKeys(roleData.value);
      setRoleData(JSON.parse(JSON.stringify(roleData)));
    }
  };

  const setChecked = (item: treeItemType) => {
    if (Array.isArray(item.children) && item.children.length > 0) {
      const arr: Array<boolean> = item.children.map((v) => v.checked);
      if (arr.includes(true) && arr.includes(false)) {
        item.checked = false;
        item.indeterminate = true;
      } else {
        item.checked = arr.includes(true);
        item.indeterminate = arr.includes(true);
      }
    }
  };
  /**
   * 渲染checkbox
   */
  const renderCheckedModule = (treeData: Array<treeItemType>) => {
    return treeData.map((item: any) => {
      return (
        <React.Fragment key={item.id}>
          <span
            key={item.id}
            className={item.parentId ? (item.parentId && item.children ? "firSon" : item?.type === 1 ? "firSon" : "secSon") : "father"}
          >
            <Checkbox
              indeterminate={item.checked ? (item.children?.map((item: any) => item.checked).includes(false) ? true : false) : item.indeterminate}
              onChange={() => {
                checkChange(item, !item.checked, true);
              }}
              checked={item.checked}
            >
              {item.text}
            </Checkbox>
            {Array.isArray(item.children) && item.children.length > 0 && renderCheckedModule(item.children)}
          </span>
        </React.Fragment>
      );
    });
  };
  return <>{renderCheckedModule(roleData.tree)}</>;
}

export default RoleTree;
