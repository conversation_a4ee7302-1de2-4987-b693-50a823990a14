.ant-drawer-body {
  padding: 24px 32px !important;
  // overflow: hidden !important;
  display: flex;
  flex-direction: column;
  padding-bottom: 0px !important;
}
.ant-drawer-header {
  border: none;
}
.lm-drawer-container.ant-drawer .ant-drawer-header .ant-drawer-close {
  top: 8px;
  right: 8px;
}
.ant-drawer-content-wrapper {
  // border-radius: 16px 0 0 16px;
  overflow: hidden;
  background-color: var(--primary-dark);
  color: var(--gray2);
}
.userform-wrapper-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.userform-wrapper {
  width: 100%;
  flex: 1;
  padding-bottom: 16px;
  overflow: hidden;
  .user-anchor {
    position: absolute;
    right: 10px;
    top: 10px;
  }
  .user-form {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .ant-col {
      width: 100%;
    }
    .basic {
      // padding: 16px;
      box-sizing: border-box;
      height: max-content;
    }
    .identity {
      contain: strict;
      margin-top: 16px;
      // padding-left: 16px;
      // margin-bottom: 32px;
      flex: 1;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
    }
    input {
      width: 100%;
      border-radius: 4px;
    }
  }

  .info {
    &:first-child {
      border-bottom: 1px solid #dcdcdc;
      .title {
        margin-bottom: 24px;
      }
    }

    .title {
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
    }
  }

  .auth {
    flex: 1;
    overflow: auto;
    .ant-tree-checkbox-inner {
      background-color: var(--form-bg);
      border: none;
    }
    .ant-tree-checkbox-checked .ant-tree-checkbox-inner {
      border-color: var(--form-active-bg);
      background-color: var(--form-active-bg);
    }
    .ant-tree .ant-tree-node-content-wrapper:hover {
      border: none;
    }
  }

  .bottom-submit {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 65px;
    line-height: 65px;
    text-align: center;
    border-top: 1px solid #dcdcdc;
    border-radius: 4px;
  }
}
.userform-wrapper-auth-tree {
  span.father {
    display: block;
    margin-bottom: 8px;
    > label {
      display: block;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.90);
    }
  }
  span.firSon {
    display: block;
    color: #fff;
    padding: 0 !important;
    > label {
      display: block;
      line-height: 30px;
      padding-left: 25px;
      font-size: var(--fs-small);
      span {
        color: #333;
      }
    }
  }
  span.secSon {
    padding: 0 !important;
    > label {
      display: inline-block;
      line-height: 30px;
      height: 30px;
      padding-left: 50px;
      font-size: var(--fs-small);
      span {
        color: #333;
      }
    }
  }

  .ant-checkbox-inner {
    display: inline-block;
    &::after{
      top: 35%;
    }
  }

  .ant-checkbox-wrapper {
    .ant-checkbox {
      display: inline-block;
    }
  }
}
