.role-wrapper {
  height: 100%;
  padding: 16px;
  .functionIds-item {
    display: inline-flex;
    padding: 2px 8px;
    align-items: center;
    border-radius: 3px;
    border: 1px solid #dcdcdc;
    background: #f3f3f3;
    margin-right: 8px;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
  .role-container {
    width: 100%;
    height: 100%;
    background: var(--content-bg);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    .role-container-title {
      padding-top: 24px;
      padding-left: 24px;
      color: #000;
      font-family: "PingFang SC";
      font-size: 24px;
      font-style: normal;
      font-weight: 600;
      line-height: 32px;
    }
    .role-search-container {
      padding-top: 20px;
      margin: 0 28px 20px 28px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .role-filter-btn {
        width: 140px;
        height: 32px;
        border-radius: 4px;
        //   border: 1px solid #dcdcdc;

        .ant-select-selector {
        }
      }
      .btn-add {
        width: 112px;
        margin-right: 16px;
      }

      .search-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .ant-form-item {
          margin: 0;
        }

        .search-more {
          display: inline-flex;
        }
      }
    }
  }
}
