import { Table, Button, Form, message, Input } from "antd";
import { useAntdTable, useSize } from "ahooks";
import { IconFont, useSimpleState } from "@cloud-app-dev/vidc";
import RoleEdit from "./roleEdit";
import warnImg from "@src/assets/image/warn.png";
import OperatingBounced from "@src/components/OperatingBounced";
import Service from "@src/service/system";
import { DataType } from "@src/service/interface";
import { useMemo, useRef } from "react";
import { getColumns } from "./utils";
import Authority from "@src/components/Authority";
import "./index.less";

interface tableType {
  list: DataType[];
  total: any;
}
const FormItem = Form.Item;

export default function RolePage() {
  const [form] = Form.useForm();
  const ref = useRef<HTMLDivElement>(null);
  const size = useSize(ref);
  const [state, updateState] = useSimpleState({
    currentItem: undefined as DataType | undefined,
    showedit: false,
    isAdd: false,
    showdelete: false,
    key: Date.now(),
  });

  const columns = useMemo(() => getColumns(updateState), [updateState]);

  const getTableData = ({ current, pageSize }: { current: number; pageSize: number }, formData: Object): Promise<tableType> => {
    let params: any = {
      limit: pageSize,
      offset: (current - 1) * pageSize,
      isPagination: true,
      ...formData,
    };

    return Service.role.queryRolesTable(params).then((res: any) => ({ list: res?.data?.list, total: res?.data?.totalCount }));
  };
  const deleteOk = () => {
    const ids = [state.currentItem?.id];
    Service.role
      .deleteRole(ids)
      .then((res: any) => {
        if (res.code === 0) {
          updateState({ showdelete: false });
          message.success("角色删除成功");
          search.submit();
        } else {
          message.warning(res.message);
        }
      })
      .catch((err: any) => {
        message.warning(err.data.message);
      });
  };

  const { tableProps, search } = useAntdTable(getTableData, { defaultPageSize: 10, form });
  //显示分页器跳转
  tableProps.pagination.showQuickJumper = true;
  tableProps.pagination.showSizeChanger = true;
  tableProps.pagination.showTotal = function (total: number, range: number[]) {
    return `共${total}项数据`;
  };
  tableProps.style = { margin: " 0 28px" };

  const searchForm = (
    <Form form={form} layout="inline" className="search-form">
      <div className="search-more"></div>
      <FormItem name="roleName">
        <Input
          placeholder="请输入角色名称查询"
          className="user-filter-btn vidc-Input"
          allowClear={false}
          onChange={search.submit}
          style={{ width: "260px", borderRadius: "4px" }}
          suffix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ cursor: "pointer", fontSize: "12px" }} />}
        />
      </FormItem>
    </Form>
  );

  return (
    <div className="role-wrapper">
      <div className="role-container" ref={ref}>
        <div className="role-container-title">角色管理</div>
        <div className="role-search-container">
          <Authority code='30000422' other={<span></span>}>
          <Button
            className="user-filter-btn btn-add"
            type="primary"
            style={{ background: "var(--primary)" }}
            onClick={() => updateState({ isAdd: true, showedit: true, currentItem: undefined })}
          >
            <IconFont type="icon-xitongguanli_zengjiafenzu" />
            新建角色
          </Button>
          </Authority>
          
          {searchForm}
        </div>
        <Table rowKey="id" columns={columns} scroll={{ y: size?.height && size.height - 248 }} {...tableProps} />
      </div>
      <OperatingBounced
        isShow={state.showdelete}
        title="删除确认"
        icon={<img src={warnImg} />}
        onCancel={() => updateState({ showdelete: false })}
        onOk={deleteOk}
        content={`是否删除${state.currentItem?.roleName}角色？`}
      />
      {state.showedit ? (
        <RoleEdit
          key={state.key}
          isShow={state.showedit}
          toggle={() => updateState({ showedit: false })}
          isAdd={state.isAdd}
          item={state.currentItem}
          submit={search.submit}
        />
      ) : null}
    </div>
  );
}
