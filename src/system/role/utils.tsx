/* eslint-disable jsx-a11y/anchor-is-valid */
import { DataType } from "@src/service/interface";
import { Config } from "@cloud-app-dev/vidc";
import { Space } from "antd";
import { ColumnsType } from "antd/lib/table";
import Authority from "@src/components/Authority";

export function getColumns(stateChange: (options: any) => void): ColumnsType<DataType> {
  return [
    {
      title: "序号",
      dataIndex: "key",
      key: "key",
      width: "3%",
      render: (_, __, index) => index + 1,
    },
    {
      title: "用户名",
      width: "5%",
      ellipsis: true,
      dataIndex: "roleName",
      key: "roleName",
    },
    {
      title: "权限信息",
      width: "20%",
      ellipsis: true,
      dataIndex: "functionIds",
      key: "functionIds",
      render: (text, item, index) => {
        const systemFeatures: any = Config.features;
        let privilegeIds = text || [];
        let itemss: any = [];
        privilegeIds.length > 0 &&
          privilegeIds.map((x: any) => {
            const obj = systemFeatures.find((v: any) => v.id === x);
            if (obj) {
              itemss.push(obj.functionName);
            }
          });
        return (
          itemss && (
            <span className="ellipsis" title={itemss.join(",")}>
              {/* {itemss.join(', ')} */}
              {itemss.map((item: string, index: number) => (
                <span className="functionIds-item" key={index}>
                  {item}
                </span>
              ))}
            </span>
          )
        );
      },
    },

    {
      title: "描述",
      width: "10%",
      ellipsis: true,
      dataIndex: "roleDesc",
      key: "roleDesc",
      render: (text) => text || "-",
    },
    {
      title: "操作",
      dataIndex: "id",
      width: "4%",
      render: (_, item: DataType) => {
        return (
          <div className="table-tools">
            {item.id != "351305569764101" && item.id != "351305240027909" && (
              <Space size="middle">
                <Authority code="30000421">
                  <a style={{ color: "var(--primary)" }} onClick={() => stateChange({ currentItem: item, isAdd: false, showedit: true })}>
                    编辑
                  </a>
                </Authority>
                <Authority code="30000423">
                  <a style={{ color: "var(--primary)" }} onClick={() => stateChange({ currentItem: item, showdelete: true })}>
                    删除
                  </a>
                </Authority>
              </Space>
            )}
          </div>
        );
      },
    },
  ];
}
