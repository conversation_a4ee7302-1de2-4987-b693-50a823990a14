import React from "react";
import { Routes, Route, unstable_HistoryRouter as HistoryRouter } from "react-router-dom";
import { AppContext, InstanceHistory } from "@cloud-app-dev/vidc";
import Redirect from "./components/AppHome/Redirect";
import InitialConfig from "./components/InitialConfig";
import ThemeAntd from "./components/ThemeAntd";
import { ConfigProvider } from "antd";
import zh_CN from "antd/locale/zh_CN";
import { legacyLogicalPropertiesTransformer, StyleProvider } from '@ant-design/cssinjs';

const AppHome = React.lazy(() => import("./components/AppHome"));
const LoginPage = React.lazy(() => import("./login"));

function App() {
  return (
    <StyleProvider hashPriority="high" transformers={[legacyLogicalPropertiesTransformer]}>
      <ConfigProvider locale={zh_CN}>
        <InitialConfig>
          <ThemeAntd>
            <AppContext.Provider>
              <HistoryRouter history={InstanceHistory as any}>
                <Routes>
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/:subApp/*" element={<AppHome />} />
                  <Route path="/" element={<Redirect />} />
                </Routes>
              </HistoryRouter>
            </AppContext.Provider>
          </ThemeAntd>
        </InitialConfig>
      </ConfigProvider>
    </StyleProvider>
  );
}

export default App;
