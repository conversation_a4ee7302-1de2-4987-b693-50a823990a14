import { ConfigProvider } from "antd";

interface IThemeAntdProps {
  children: any;
}

export default function ThemeAntd({ children }: IThemeAntdProps) {
  return (
    <ConfigProvider
      // theme={{
      //   token: {
      //     colorBgBase: "#ffffff",
      //     colorTextBase: "#333",
      //     colorBgContainer: "#ffffff",
      //     colorBgElevated: "#2a64bc",
      //     colorPrimary: "#319bff", // 主色
      //     controlOutline: "transparent", // 表单阴影
      //   },
      // }}
    >
      {children}
    </ConfigProvider>
  );
}
