import { IconFont } from '@cloud-app-dev/vidc';

interface FontAndIconType {
  text: string;
  type: string;
  tColor?: string;
  iColor?: string;
}
const FontAndIcon = ({ text, type, tColor = '#FFFFFF', iColor = 'var(--primary)' }: FontAndIconType) => {
  return (
    <span>
      <IconFont style={{ color: iColor }} type={type} /> <span style={{ color: tColor }}>{text}</span>
    </span>
  );
};

export default FontAndIcon;
