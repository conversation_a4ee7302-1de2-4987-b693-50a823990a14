export interface AppInstance {
  bootstrap: (props: any) => Promise<any>;
  mount: (props: any) => Promise<any>;
  unmount: (props: any) => Promise<any>;
  update: (props: any) => Promise<any>;
  unmountDev?: (props: any) => Promise<any>;
}

export type AppItemType = {
  id: string;
  routerPrefix: string;
  version: string;
  status: 0 | 1 | 2; //App状态1：正常，2：下架，0：删除，微应用下架或者删除时对应的功能项全部停用
  resource: string[]; //App对应脚本静态资源文件路径[js,css]
  icon: string;
  name: string;
  ext?: any;
  html: string;
  devHtml: string;
};
