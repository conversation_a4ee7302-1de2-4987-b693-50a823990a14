---
title: 微应用加载 LoaderApp
nav:
  path: /components
  title: 组件库
  order: 2
group:
  path: /load
  title: 加载
  order: 1
---

## LoaderApp 微应用加载器

微应用加载器是基于`systemjs`实现的一台`js`脚本加载器，是实现微应用框架的核心组件,微应用必须符合约定规则导出的生命周期。

### 何时使用

框架级别组件，当微应用自己需要独立部署时可以基于路由实现，加载登录或者其他授权的微应用达到单独运行自己的目的

### 代码演示

```tsx | pure
import React, { useState } from 'react';
import { LoaderApp } from '@cloud-app-dev/basic-components';
const config = {
  name: 'login',
  script: 'http://***************:2234/micro-apps/micro-unified-login/static/js/micro-unified-login.js',
  style: 'http://***************:2234/micro-apps/micro-unified-login/static/css/micro-unified-login.css',
  version: '0.0.2',
};
const App = () => {
  return (
    <div>
      <LoaderApp appConfig={config} />
    </div>
  );
};
export default App;
```

<API src="./index.tsx" exports='["default"]' identifier="LoaderApp"></API>
