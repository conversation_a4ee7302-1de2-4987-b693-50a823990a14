import React, { useRef, useMemo } from "react";
import { uuid } from "@cloud-app-dev/vidc";
import { AppItemType } from "./interface";
import { useMount } from "ahooks";
import LoaderAppConfig from "./config";
import "./index.less";

type LoaderKey = keyof typeof LoaderAppConfig;

interface ILoaderAppProps {
  /**
   * @description 微应用配置信息
   * @default -
   */
  appConfig: AppItemType;

  /**
   * @description 需要传递微应用的参数
   * @default {}
   */
  appProps: { [key: string]: any; currentId: string; tabId: string; pathname?: string };

  /**
   * @description 微应用加载容器样式
   * @default
   */
  style?: React.CSSProperties;
}

function LoaderApp({ appConfig, appProps, style }: ILoaderAppProps) {
  const domRef = useRef<HTMLDivElement>(null);
  const id = useMemo(() => uuid(), []);

  useMount(() => {
    if (!appConfig) {
      console.error("微应用不存在", "appConfig ->", appConfig, "appProps ->", appProps);
      return undefined;
    }
    LoaderAppConfig;
  });
  const LoaderComponent = useMemo(() => (appProps.pathname ? LoaderAppConfig[appProps.pathname as LoaderKey] : () => <></>), [appProps.pathname]);
  return (
    <main ref={domRef} className={`loaded-app-layout ${appConfig.routerPrefix}-${id}`} style={style}>
      <LoaderComponent />
    </main>
  );
}

export default LoaderApp;
