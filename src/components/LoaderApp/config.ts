import React from "react";

const LoaderAppConfig = {
  // "/aisApp/alarmCenter": React.lazy(() => import("@src/ais_app/pages/AlarmCenter")),
  "/aisApp/eventAlarm": React.lazy(() => import("@src/ais_app/pages/EventAlarm")),
  "/aisApp/identityLibrary": React.lazy(() => import("@src/ais_app/pages/IdentityLibrary")),
  "/aisApp/frequentDoctorAppearances": React.lazy(() => import("@src/ais_app/pages/FrequentDoctorAppearances")),
  "/aisApp/restTimePolicePrison": React.lazy(() => import("@src/ais_app/pages/RestTimePolicePrison")),
  "/aisApp/solitaryDetention": React.lazy(() => import("@src/ais_app/pages/SolitaryDetention")),
  "/aisApp/surveillanceRoomPoliceOffCuty": React.lazy(() => import("@src/ais_app/pages/SurveillanceRoomPoliceOffCuty")),
  // "/aisApp/alarmDetails": React.lazy(() => import("@src/ais_app/pages/AlarmCenter/AlarmDetails")),
  "/aisApp/eventDetails": React.lazy(() => import("@src/ais_app/pages/EventAlarm/AlarmDetails")),
  "/aisApp/frequentDoctorAppearancesDetails": React.lazy(() => import("@src/ais_app/pages/EventAlarm/AlarmDetails")),
  "/aisApp/restTimePolicePrisonDetails": React.lazy(() => import("@src/ais_app/pages/EventAlarm/AlarmDetails")),
  "/aisApp/solitaryDetentionDetails": React.lazy(() => import("@src/ais_app/pages/EventAlarm/AlarmDetails")),
  "/aisApp/surveillanceRoomPoliceOffCutyDetails": React.lazy(() => import("@src/ais_app/pages/EventAlarm/AlarmDetails")),
  "/aisApp/identityLibraryDetails": React.lazy(() => import("@src/ais_app/pages/IdentityLibrary/AlarmDetails")),
  "/aisApp/testView": React.lazy(() => import("@src/ais_app/pages/TestView")),
  "/aisApp/testConfig": React.lazy(() => import("@src/ais_app/pages/TestConfig")),
  "/aisApp/server": React.lazy(() => import("@src/ais_app/pages/ServerView")),
  "/aisApp/platform": React.lazy(() => import("@src/ais_app/pages/Platform")),
  "/aisApp/testDetails": React.lazy(() => import("@src/ais_app/pages/TestView/TestDetails")),
  "/aisApp/rtVideo": React.lazy(() => import("@src/ais_app/pages/video/live")),
  "/aisApp/videoReplay": React.lazy(() => import("@src/ais_app/pages/video/record")),
  "/systemManagement/user": React.lazy(() => import("@src/system/user")),
  "/systemManagement/device": React.lazy(() => import("@src/system/device")),
  "/systemManagement/role": React.lazy(() => import("@src/system/role")),
  "/systemManagement/gamePointEdit": React.lazy(() => import("@src/ais_app/pages/GamePointEdit")),
  "/systemManagement/timeRule": React.lazy(() => import("@src/ais_app/pages/TimeRule")),
  "/systemManagement/device/labelEdit": React.lazy(() => import("@src/ais_app/pages/LabelEdit")),
  "/systemManagement/algorithmicRule": React.lazy(() => import("@src/ais_app/pages/AslgorithmicRule")),
} as const;

export default LoaderAppConfig;
