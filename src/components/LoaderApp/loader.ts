import { AppInstance } from './interface';
import { arrayify, rewrite, importHTML } from './utils';

interface createScopedCssTextProps {
  styleNode: HTMLStyleElement;
  prefix: string;
}

export function createScopedCssText({ styleNode, prefix }: createScopedCssTextProps) {
  const sheet = styleNode.sheet;
  const rules = arrayify(sheet?.cssRules ?? []) as CSSRule[];
  return rewrite(rules, prefix);
}

interface ILoaderStyleProps {
  cssText: string;
  prefix: string;
  styleNode: HTMLStyleElement;
}

export async function LoaderStyle({ cssText, prefix, styleNode }: ILoaderStyleProps) {
  styleNode.innerHTML = cssText;
  const scopeCssText = createScopedCssText({ prefix, styleNode });
  styleNode.textContent = scopeCssText;
}

interface ILoaderModuleProps {
  prefix: string;
  styleNode: HTMLStyleElement;
  html: string;
}

export default async function LoaderModule({ prefix, styleNode, html }: ILoaderModuleProps): Promise<AppInstance> {
  let cssText: string;
  let exports: AppInstance;
  const result = await importHTML(html);
  cssText = result.cssTexts.join(' ');
  exports = result.appInstance;
  await LoaderStyle({ cssText, prefix, styleNode });
  return exports;
}
