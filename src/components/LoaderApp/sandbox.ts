interface OPTIONS {
  // 沙箱中自动继承全局变量
  inheritGlobal?: boolean;
  // 黑名单列表
  blacklist?: string[];
  docMap?: { [key: string]: any };
}
interface BLACKMAP {
  [key: string]: boolean;
}

// 懒加载的脚本暂时没法拦截
class SandBox {
  private updatedValueSet = new Map();
  constructor(context = window, { blacklist = [], docMap = {} }: OPTIONS = {}) {
    const blackmap: BLACKMAP = {};
    for (let i = 0; i < blacklist.length; i++) {
      const name = blacklist[i];
      blackmap[name] = true;
    }
    const updatedValueSet = this.updatedValueSet;
    // const proxyDocoment = createSandBoxDocument(docMap);
    const proxy = new Proxy(context, {
      set(target: any, name: string, value: any): boolean {
        if (!target[name]) {
          updatedValueSet.set(name, value);
        } else {
          target[name] = value;
        }
        return true;
      },
      get(target: any, name: string): any {
        if (blackmap.hasOwnProperty(name)) {
          console.error(`Can't assess property: ${name} in blacklist`);
          return undefined;
        }
        if (updatedValueSet.has(name)) {
          return updatedValueSet.get(name);
        }
        // if (
        //   name === 'Object' ||
        //   name === 'Symbol' ||
        //   typeof name === 'symbol' ||
        //   name === 'parseFloat' ||
        //   name === 'parseInt' ||
        //   name === 'Array' ||
        //   name === 'Number' ||
        //   name === 'Math' ||
        //   name === 'String'||
        //   name === 'Reflect'||
        //   name === 'Boolean'||
        //   name === 'isNaN'
        // ) {
        // } else {
        //   console.log(name);
        // }

        switch (name) {
          case 'window':
          case 'global':
          case 'self':
          case 'globalThis':
            return proxy;
          case 'document':
            return document;
          // return proxyDocoment;
        }
        if (typeof target[name] === 'function' && /^[a-z]/.test(name)) {
          return target[name].bind && target[name].bind(target);
        } else {
          return target[name];
        }
      },
    });
    return proxy;
  }
}

// function createSandBoxDocument(proxyMap: { [key: string]: any } = {}) {
//   return new Proxy(document, {
//     get(target: any, p: string): any {
//       if (proxyMap.hasOwnProperty(p)) {
//         return proxyMap[p];
//       }
//       if (typeof target[p] === 'function' && /^[a-z]/.test(p)) {
//         return target[p].bind && target[p].bind(target);
//       } else {
//         return target[p];
//       }
//     },
//   });
// }

// function text() {
//   (function (global: any) {
//     const s = global.document.createElement('span');
//     s.innerHTML = '1111';
//     console.log(global.document.head.appendChild(s));
//   })(new SandBox(window, { docMap: { head: document.body } }));
// }
// text();
export default SandBox;
