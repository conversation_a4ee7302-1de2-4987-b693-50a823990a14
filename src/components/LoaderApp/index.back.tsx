import React, { useRef, useMemo } from 'react';
import { uuid, nextTick } from '@cloud-app-dev/vidc';
import LoaderModule from './loader';
import { AppInstance, AppItemType } from './interface';
import { useUnmount, useMount } from 'ahooks';
import './index.less';

interface ILoaderAppProps {
  /**
   * @description 微应用配置信息
   * @default -
   */
  appConfig: AppItemType;

  /**
   * @description 需要传递微应用的参数
   * @default {}
   */
  appProps: { [key: string]: any; currentId: string; tabId: string };

  /**
   * @description 微应用加载容器样式
   * @default
   */
  style?: React.CSSProperties;
}

function LoaderApp({ appConfig, appProps, style }: ILoaderAppProps) {
  const domRef = useRef<HTMLDivElement>(null);
  const styleRef = useRef<HTMLStyleElement>(null);
  const loadedAppRef = useRef<AppInstance>();
  const id = useMemo(() => uuid(), []);
  const statusRef = useRef<string>();

  useMount(() => {
    if (!appConfig) {
      console.error('微应用不存在', 'appConfig ->', appConfig, 'appProps ->', appProps);
      return undefined;
    }

    if (!domRef.current || !styleRef.current) {
      console.error('LoaderApp组件未正常初始化！', 'config ->', appConfig, 'appProps ->', appProps);
      return undefined;
    }

    const props = { ...appProps, container: domRef.current };
    const { routerPrefix, html } = appConfig;

    const options = { html, name: routerPrefix, prefix: `.${routerPrefix}-${id}`, styleNode: styleRef.current };
    LoaderModule(options).then(async (mod) => {
      if (!mod.bootstrap) {
        return;
      }
      loadedAppRef.current = mod;

      // 沙箱销毁方法
      await mod.bootstrap(props);
      nextTick(() => (statusRef.current = 'bootstrap'));
      await mod.mount(props);
      nextTick(() => (statusRef.current = 'mounted'));
    });
  });

  useUnmount(() => {
    if (loadedAppRef.current) {
      const app = loadedAppRef.current;
      const props = { ...appProps, container: domRef.current };
      app.unmount(props);
      nextTick(() => (statusRef.current = 'unmount'));
      loadedAppRef.current = null as any;
    }
  });

  /**
   * 这里走update，不需要Effct，前面已经对副作用做了处理，所以每次render 直接走更新处理
   */
  if (loadedAppRef.current && loadedAppRef.current.update && appProps.currentId === appProps.tabId) {
    const props = { ...appProps, container: domRef.current };
    loadedAppRef.current.update(props);
  }

  return (
    <main ref={domRef} className={`loaded-app-layout ${appConfig.routerPrefix}-${id}`} style={style}>
      <style ref={styleRef} />
      <div id={appConfig.routerPrefix} style={{ width: '100%', height: '100%' }}></div>
    </main>
  );
}

LoaderApp.defaultProps = {
  appConfig: {},
  appProps: {},
  style: {},
};

export default LoaderApp;
