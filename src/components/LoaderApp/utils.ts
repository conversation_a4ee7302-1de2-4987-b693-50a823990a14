import { AppItemType } from '@cloud-app-dev/vidc/es/Config/interface';

export const getMicroConfig = (appConfig = {} as AppItemType, appProps = {} as any, container: HTMLDivElement) => {
  const { routerPrefix, resource, name } = appConfig;

  const microAppEntry = { scripts: [resource[0]], styles: [resource[1]], html: `<div id="${routerPrefix}" style="height:100%"></div>` };

  return {
    title: name,
    routerPrefix: routerPrefix,
    container,
    props: appProps,
    entry: microAppEntry,
  };
};

const styleCache = {} as { [key: string]: string };

export async function fetchStyle(style: string) {
  if (styleCache[style]) {
    return styleCache[style];
  }
  const cssText = await fetch(style)
    .then((r) => r.text())
    .catch(() => '');

  styleCache[style] = cssText;
  return cssText;
}

const RuleType = {
  // type: rule will be rewrote
  STYLE: 1,
  MEDIA: 4,
  SUPPORTS: 12,

  // type: value will be kept
  IMPORT: 3,
  FONT_FACE: 5,
  PAGE: 6,
  KEYFRAMES: 7,
  KEYFRAME: 8,
};

export const arrayify = <T>(list: CSSRuleList | any[]) => {
  return [].slice.call(list, 0) as T[];
};

export function rewrite(rules: CSSRule[], prefix = '') {
  let css = '';

  rules.forEach((rule) => {
    switch (rule.type) {
      case RuleType.STYLE:
        css += ruleStyle(rule as CSSStyleRule, prefix);
        break;
      case RuleType.MEDIA:
        css += ruleMedia(rule as CSSMediaRule, prefix);
        break;
      case RuleType.SUPPORTS:
        css += ruleSupport(rule as CSSSupportsRule, prefix);
        break;
      default:
        css += `${rule.cssText}`;
        break;
    }
  });

  return css;
}

// handle case:
// .app-main {}
// html, body {}

// eslint-disable-next-line class-methods-use-this
function ruleStyle(rule: CSSStyleRule, prefix: string) {
  const rootSelectorRE = /((?:[^\w\-.#]|^)(body|html|:root))/gm;
  const rootCombinationRE = /(html[^\w{[]+)/gm;

  const selector = rule.selectorText.trim();

  let { cssText } = rule;
  // handle html { ... }
  // handle body { ... }
  // handle :root { ... }
  if (selector === 'html' || selector === 'body' || selector === ':root') {
    return ''; //微应用模式下清楚顶层样式
    // return cssText.replace(rootSelectorRE, prefix);
  }

  // handle html body { ... }
  // handle html > body { ... }
  if (rootCombinationRE.test(rule.selectorText)) {
    const siblingSelectorRE = /(html[^\w{]+)(\+|~)/gm;

    // since html + body is a non-standard rule for html
    // transformer will ignore it
    if (!siblingSelectorRE.test(rule.selectorText)) {
      cssText = cssText.replace(rootCombinationRE, '');
    }
  }

  // handle grouping selector, a,span,p,div { ... }
  cssText = cssText.replace(/^[\s\S]+{/, (selectors) =>
    selectors.replace(/(^|,\n?)([^,]+)/g, (item, p, s) => {
      // handle div,body,span { ... }
      if (rootSelectorRE.test(item)) {
        return item.replace(rootSelectorRE, (m) => {
          // do not discard valid previous character, such as body,html or *:not(:root)
          const whitePrevChars = [',', '('];

          if (m && whitePrevChars.includes(m[0])) {
            return `${m[0]}${prefix}`;
          }

          // replace root selector with prefix
          return prefix;
        });
      }

      return `${p}${prefix} ${s.replace(/^ */, '')}`;
    })
  );

  return cssText;
}

// handle case:
// @media screen and (max-width: 300px) {}
function ruleMedia(rule: CSSMediaRule, prefix: string) {
  const css = rewrite(arrayify(rule.cssRules), prefix);
  return `@media ${rule.conditionText} {${css}}`;
}
let _global = window as any;
// handle case:
// @supports (display: grid) {}
function ruleSupport(rule: CSSSupportsRule, prefix: string) {
  const css = rewrite(arrayify(rule.cssRules), prefix);
  return `@supports ${rule.conditionText} {${css}}`;
}

const cache = {} as { [key: string]: string };

export async function importHTML(url: string) {
  const html = cache[url] ? cache[url] : await fetch(url).then((res) => res.text());

  if (!cache[url]) {
    cache[url] = html;
  }

  const doc = new DOMParser().parseFromString(html, 'text/html');
  const scripts = Array.from(doc.querySelectorAll('script')).filter((v) => v.attributes.getNamedItem('src'));
  const srcs = scripts.map((v) => v.getAttribute('src'));
  const entryIndex = scripts.findIndex((v) => v.attributes.getNamedItem('entry'));
  const index = entryIndex === -1 ? 0 : entryIndex;
  const entry = scripts[index];
  if (!entry) {
    throw new Error('微应用没有入口脚本！');
  }

  //处理脚本，找到入口导出生命周期
  let appInstance: any;
  const res = await Promise.all(srcs.map((v) => _global.System.import(v)));
  appInstance = res[index];

  // 处理样式 link
  const links = Array.from(doc.querySelectorAll('link')).filter((v) => v.getAttribute('rel') === 'stylesheet');
  const cssHref = links.map((v) => v.getAttribute('href')) as string[];
  const cssTexts = await Promise.all(cssHref.map((v: string) => fetchStyle(v)));

  // 处理样式style
  const styles = Array.from(doc.querySelectorAll('style'));
  styles.forEach((style) => cssTexts.push(style.textContent || ''));

  return {
    cssTexts,
    appInstance,
  };
}
