.VideoLayout {
  height: 100%;
  box-sizing: border-box;
  padding: 16px;
  color: #333;
  background: #FFF;
  border-radius: 8px;
  .left {
    height: 100%;
    width: 344px;
    position: relative;
    .leftContent {
      height: 100%;
      background: #fff;
      // border-radius: 4px;
      overflow: hidden;
    }
  }
  .line{
    width: 1px ;
    height: 100%;
    background-color: #E7E7E7;
    margin: 0px 16px;
  }
  .switch {
    cursor: pointer;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    // width: 0;
    // height: 50px;
    // border-left: 16px solid #C0C5CA;
    // border-top: 10px solid transparent;
    // border-bottom: 10px solid transparent;
    width: 11px;
    height: 64px;
    background: #c0c5ca;
    border-radius: 6px;
    z-index: 0;
    .anticon {
      position: absolute;
      left: -4px;
      top: 23px;
      color: var(--gray6);
    }
  }
  .right {
    height: 100%;
    .rightContent {
      height: 100%;
      // background: var(--content-bg);
      // box-shadow: 0px 5px 14px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      border-radius: 6px;
    }
  }
}
