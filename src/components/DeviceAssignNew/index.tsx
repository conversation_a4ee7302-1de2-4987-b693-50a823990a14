import React, { useEffect, useMemo } from 'react';
import { useRequest, useUpdateEffect } from 'ahooks';
import { Tree, Input } from 'antd';
import { getGroups, getTreeIdWithKeyword, getTreeMap } from './utils';
import { IconFont, useSimpleState } from '@cloud-app-dev/vidc';
import { treeHelper } from '@cloud-app-dev/vidc';
import { ListItem, ITreeItem } from './utils';
import './index.less';

interface IDeviceSelectProps {
  selectItems?: ListItem[];
  onSelectChange?: (list: ListItem[]) => void;
}

function DeviceSelect({ selectItems, onSelectChange }: IDeviceSelectProps) {
  const [state, updateState, setState] = useSimpleState({
    expandedKeys: [] as string[],
    groupId: undefined as string | undefined,
    keyword: '' as string,
    selectItems: selectItems || ([] as ListItem[]),
    checkedKeys:[] as any
  });
  const { data: treeRes = [] } = useRequest<ITreeItem[], any>(() => getGroups());
  const treeDataTemp = useMemo(() => treeHelper.computTreeList(treeRes), [treeRes]);
  // 树数据处理，高亮关键字
  const treeData = useMemo(() => {
    const loop = (data: ITreeItem[]): ITreeItem[] =>
      data.map((item: any) => {
        const strName = item.groupName as string || '';
        const index = strName.indexOf(state.keyword);
        const beforeStr = strName.substring(0, index);
        const afterStr = strName.slice(index + state.keyword.length);
        const name =
          index > -1 ? (
            <span>
              {beforeStr}
              <span className="site-tree-search-value">{state.keyword}</span>
              {afterStr}
            </span>
          ) : (
            <span>{strName}</span>
          );
        const title = <div className="title">{name}</div>;

        if (item.children) {
          return { ...item, name: title, code: item.code, children: loop(item.children) };
        }

        return { ...item, name: title, code: item.code };
      });

    return loop(treeDataTemp);
  }, [state.keyword, treeDataTemp]);

  useUpdateEffect(() => onSelectChange?.([...state.checkedKeys]), [state.checkedKeys]);
  const treeMap = useMemo(() => getTreeMap(treeRes), [treeRes]);
  // 树搜索处理,匹配结果展开
  const onFilterChange = (name: string) => {
    const codes = getTreeIdWithKeyword(treeMap, name);
    updateState({ keyword: name, expandedKeys: codes });
  };
  useEffect(()=>{
    updateState({checkedKeys:selectItems})
  },[])
  return (
    <div className="device-select">
      <div className="org-tree-select select-content-part">
        <div className="tree-title">
          <span>
            <IconFont type="icon-renwupeizhi_shebeirenwu_shebeimulu" style={{ fontSize: 20, position: 'relative', top: 2 }} />
            <span>系统分组</span>
          </span>
        </div>
        <div className="tree-content">
          <div className="search-box">
            <Input
              prefixCls="cloudapp-input"
              prefix={<IconFont type="icon-renwupeizhi_shebeirenwu_sousuo" style={{ fontSize: '12px' }} />}
              placeholder="请输入分组名称"
              onChange={(v) => onFilterChange(v.target.value)}
            />
          </div>
          <Tree
            checkable
            prefixCls="cloudapp-tree"
            treeData={treeData}
            switcherIcon={<IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" style={{ transform: 'rotate(90deg)' }} />}
            fieldNames={{ title: 'name', key: 'id' }}
            onCheck={(treeKeys) =>{
              updateState({ checkedKeys: treeKeys })
            }}
            checkedKeys={state.checkedKeys}
          />
        </div>
      </div>
    </div>
  );
}

export default DeviceSelect;
