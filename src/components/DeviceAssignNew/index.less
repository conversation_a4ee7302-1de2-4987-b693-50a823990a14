.device-select {
    display: flex;
    min-height: 500px;
    .assign-tools {
      padding: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .cloudapp-btn {
        width: 48px;
        height: 52px;
        padding: 0;
        margin-bottom: 20px;
      }
    }
    .select-content-part {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      flex: 1;
      // border: 1px solid var(--bd);
      &.org-tree-select {
        .tree-content {
          padding: 16px 10px;
          overflow: auto;
        height: 100%;
          .site-tree-search-value {
            color: var(--danger);
          }
          .search-box {
            margin-bottom: 16px;
          }
        }
      }
      &.devive-list {
        border-left: none;
        display: flex;
        flex-direction: column;
        .lm-c-list-wrapper {
          border: none;
          flex: 1;
        }
      }
    }
    .l-c-dynamic-list-layout {
      border: none;
    }
    .tree-title {
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid var(--bd);
      & > span {
        cursor: pointer;
      }
    }
    .tree-content {
      .cloudapp-tree {
        background-color: transparent;
      }
      .cloudapp-tree-treenode {
        width: 100%;
        padding: 0;
      }
      .cloudapp-tree .cloudapp-tree-node-content-wrapper {
        flex: 1;
        &:hover {
          background-color: #E8F3FF;
        }
        &.cloudapp-tree-node-selected {
          background-color: rgba(255, 255, 255, 0.2);
        }
      }
    }
    .device-list-item {
      padding: 0 10px;
      line-height: 2.2em;
      .cloudapp-checkbox-wrapper {
        display: flex;
        align-items: center;
        overflow: hidden;
      }
      .cloudapp-checkbox {
        position: relative;
        top: -1px;
      }
      .cloudapp-checkbox + span {
        display: flex;
        align-items: center;
        flex: 1;
        overflow: hidden;
        & > span {
          white-space: nowrap;
          text-overflow: ellipsis;
          flex: 1;
          overflow: hidden;
        }
      }
    }
    .device-select-list-item {
      display: flex;
      overflow: hidden;
      align-items: center;
      padding: 0 10px;
      height: 30px;
      position: relative;
      span {
        white-space: nowrap;
        text-overflow: ellipsis;
        flex: 1;
        overflow: hidden;
      }
      .delete-icon {
        position: absolute;
        right: 10px;
        top: 10px;
        cursor: pointer;
        display: none;
      }
      &:hover {
        .delete-icon {
          display: inline-block;
        }
      }
    }
    .list-from {
      padding: 16px 10px;
      padding-bottom: 0;
      .all-check {
        margin-bottom: 8px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--gray8);
      }
      .search-box {
        margin-bottom: 16px;
      }
    }
  }
  