import React, { useState, useEffect } from 'react';
import { Config } from '@cloud-app-dev/vidc';
import { queryBSConfig, queryMicroApplicationList, queryDefaultTheme } from './utils';

export interface InitialConfigProps {
  children?: React.ReactNode;
  Spin?: React.ReactNode;
  Update?: React.ReactNode;
}

function InitialConfig({ children, Spin, Update }: InitialConfigProps) {
  const [state, setState] = useState({ isInit: false, isUpdate: false });
  useEffect(() => {
    const arr = [queryBSConfig(), queryMicroApplicationList(), queryDefaultTheme()];
    Promise.all(arr).then(([BSConfig, AppConfig, ThemeConfig]) => {
      Config.registerBSConfig(BSConfig);
      Config.registerAppConfig(AppConfig);
      Config.registerThemeConfig(ThemeConfig.content);
      setState(() => ({ isUpdate: BSConfig.isUpdate, isInit: true }));
    });
  }, []);

  return <>{state.isInit ? (state.isUpdate ? Update : children) : Spin}</>;
}

export default InitialConfig;
