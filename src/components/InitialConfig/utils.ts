import { Service } from '@cloud-app-dev/vidc';

/**
 *
 * @description 获取系统配置
 */
export async function queryBSConfig(): Promise<any> {
  return (await Service.http({ url: `/statics/config/web.conf.json?${Date.now()}` })).data;
}

/**
 *
 * @description 获取平台配色
 */
export async function queryDefaultTheme(): Promise<any> {
  return (await Service.http({ url: `/statics/config/theme-new.json?${Date.now()}` })).data;
}

/**
 *
 * @description 获取所有微应用
 */
export async function queryMicroApplicationList(): Promise<any> {
  return (await Service.http({ url: `/statics/config/app.conf.json?${Date.now()}` })).data;
}
