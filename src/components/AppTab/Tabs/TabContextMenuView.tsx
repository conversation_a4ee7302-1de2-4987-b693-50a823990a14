import React, { useMemo, useEffect, useCallback } from 'react';
import ReactDOM from 'react-dom';
import { TabItem } from '../interface';

interface ITabContextMenuViewProps {
  tabList: TabItem[];
  position: number[];
  visibel: boolean;
  tabIndex: number;
  deleteTab: (id: string) => void;
  closeCallback: () => void;
  closeOtherTab: (options: { includeIds: string[]; newCurrentId: string }) => void;
  refreshTab: (id: string) => void;
}

function TabContextMenuView({ tabList, position, visibel, tabIndex, ...props }: ITabContextMenuViewProps) {
  const ele = useMemo(() => document.createElement('div'), []);
  useEffect(() => {
    document.body.appendChild(ele);
    return () => {
      document.body.removeChild(ele);
    };
  }, [ele]);

  const closeCurrentTab = (e: React.MouseEvent) => {
    e.stopPropagation();
    const tabItem = tabList[tabIndex];
    props.deleteTab(tabItem.id);
    props.closeCallback();
  };
  const closeOtherTab = (e: React.MouseEvent) => {
    e.stopPropagation();
    const includeIds = tabList.filter((_, i) => i !== tabIndex).map((v) => v.id);
    props.closeOtherTab({ includeIds, newCurrentId: tabList[tabIndex].id });
    props.closeCallback();
  };
  const closeRightTab = (e: React.MouseEvent) => {
    e.stopPropagation();
    const startIndex = tabList.findIndex((v) => v.id === tabList[tabIndex].id);
    const includeIds = tabList.filter((_, i) => i > startIndex).map((v) => v.id);
    props.closeOtherTab({ includeIds, newCurrentId: tabList[tabIndex].id });
    props.closeCallback();
  };

  const refreshTab = (e: React.MouseEvent) => {
    e.stopPropagation();
    const tabItem = tabList[tabIndex];
    props.refreshTab(tabItem.id);
    props.closeCallback();
  };

  const content = useMemo(
    () => (
      <MenuActionView
        closeRightTab={closeRightTab}
        closeCurrentTab={closeCurrentTab}
        closeOtherTab={closeOtherTab}
        refreshTab={refreshTab}
        tabList={tabList}
        position={position}
        tabIndex={tabIndex}
      />
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [tabList, position, tabIndex]
  );

  return ReactDOM.createPortal(visibel ? content : null, ele);
}

interface IMenuActionViewProps {
  tabList: TabItem[];
  position: number[];
  tabIndex: number;
  closeCurrentTab: (e: React.MouseEvent) => void;
  closeRightTab: (e: React.MouseEvent) => void;
  closeOtherTab: (e: React.MouseEvent) => void;
  refreshTab: (e: React.MouseEvent) => void;
}

function MenuActionView({ tabList, position, tabIndex, closeCurrentTab, closeOtherTab, closeRightTab, refreshTab }: IMenuActionViewProps) {
  const canClose = useMemo(() => tabList.length > 1, [tabList.length]);
  const canCloseRight = useMemo(() => tabIndex + 1 !== tabList.length, [tabIndex, tabList.length]);
  const canCloseCurrent = useCallback((e: React.MouseEvent) => canClose && closeCurrentTab(e), [canClose, closeCurrentTab]);
  const canCloseOther = useCallback((e: React.MouseEvent) => canClose && closeOtherTab(e), [canClose, closeOtherTab]);
  const canCloseRightAction = useCallback((e: React.MouseEvent) => canCloseRight && closeRightTab(e), [canCloseRight, closeRightTab]);
  return (
    <div className="tab-context-menu-layout" style={{ left: position[0], top: position[1] }}>
      <ul>
        <li className={`${!canClose ? 'tab-menu-diable' : ''}`} onClick={canCloseCurrent}>
          关闭标签页
        </li>
        <li className={`${!canClose ? 'tab-menu-diable' : ''}`} onClick={canCloseOther}>
          关闭其他标签页
        </li>
        <li className={`${!canCloseRight ? 'tab-menu-diable' : ''}`} onClick={canCloseRightAction}>
          关闭右侧标签页
        </li>
        <li onClick={refreshTab}>重新加载</li>
      </ul>
    </div>
  );
}

export default TabContextMenuView;
