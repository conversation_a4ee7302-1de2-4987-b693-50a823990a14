import { useCallback, useRef, useState, useEffect } from "react";
import { IconFont } from "@cloud-app-dev/vidc";
import TabContextMenuView from "./TabContextMenuView";
import { TabItem } from "../interface";
import { useDebounceEffect, useSize } from "ahooks";
import { useLocation } from "react-router-dom";
import type { Location } from "history";

interface ITabBarsProps {
  tabList: TabItem[];
  currentId: string;
  changeTabPage: (id: string, isPush: boolean, location: Location) => void;
  deleteTab: (id: string) => void;
  closeOtherTab: (options: { includeIds: string[]; newCurrentId: string }) => void;
  refreshTab: (id: string) => void;
  menuType?: string;
}

const ITEM_SIZE = 166 + 2;

function TabBars({ tabList, closeOtherTab, deleteTab, currentId, changeTabPage, refreshTab, menuType }: ITabBarsProps) {
  const location = useLocation();
  const domRef = useRef<HTMLDivElement>(null);
  const [state, setState] = useState({ position: [-1000, -1000], tabIndex: 0, visibel: false, slice: false, offset: 0 });
  const hideContextMenu = useCallback(() => setState((old) => ({ ...old, visibel: false })), []);

  const size = useSize(domRef);

  useEffect(() => {
    const menuDom = domRef.current?.querySelector(".lm-tab-bar-box");
    const contextMenuAction = function (event: any) {
      event.preventDefault();
      const current = event.composedPath().find((v: HTMLElement) => {
        return !!v.className && v.className.toString().indexOf("tab-index-item") > -1;
      });
      if (!current) {
        setState((old) => ({ ...old, visibel: false }));
        return;
      }
      const index = current.dataset.index;
      setState((old) => ({ ...old, visibel: true, tabIndex: +index, position: [event.pageX, event.pageY] }));
    };

    const clickMenuAction = function (event: any) {
      event.preventDefault();
      setState((old) => ({ ...old, visibel: false }));
    };
    menuDom?.addEventListener("contextmenu", contextMenuAction, false);
    if (state.visibel) {
      document.addEventListener("click", clickMenuAction, false);
    }
    return () => {
      menuDom?.removeEventListener("contextmenu", contextMenuAction, false);
      document.removeEventListener("click", clickMenuAction, false);
    };
  }, [state.visibel]);

  useDebounceEffect(
    () => {
      const index = tabList.findIndex((v) => v.id === currentId);
      if (index < 0) {
        return;
      }
      const menuBox = domRef.current?.querySelector(".lm-tab-bar-box");
      const menuItem = menuBox?.querySelector(`.tab-item-box-${index}`) as HTMLDivElement;
      const menuSize = menuBox?.clientWidth as number;
      setState((old) => {
        const itemOffsetLeft = menuItem?.offsetLeft ?? 0;
        let offset = 0;
        if (itemOffsetLeft < old.offset) {
          offset = itemOffsetLeft;
          return { ...old, offset: offset < 0 ? 0 : offset };
        }
        if (itemOffsetLeft + ITEM_SIZE > menuSize + old.offset) {
          offset = itemOffsetLeft + ITEM_SIZE - menuSize;
          return { ...old, offset };
        }
        return old;
      });
    },
    [currentId, tabList, size?.width],
    { wait: 300 },
  );

  useDebounceEffect(
    () => {
      const menuLayout = domRef.current;
      const menuDom = domRef.current?.querySelector(".lm-tab-bar-content");
      const boxSize = menuLayout?.clientWidth as number;
      const menuSize = menuDom?.clientWidth as number;
      const flag = menuSize > boxSize;
      setState((old) => {
        if (flag !== old.slice) {
          return { ...old, slice: flag, offset: !flag ? 0 : old.offset };
        }
        return old;
      });
    },
    [tabList.length, menuType, size?.width],
    { wait: 400 },
  );

  const onNext = useCallback(
    function () {
      const menuDom = domRef.current?.querySelector(".lm-tab-bar-content");
      const menuBox = domRef.current?.querySelector(".lm-tab-bar-box");
      const menuSize = menuDom?.clientWidth as number;
      const menuBoxSize = menuBox?.clientWidth as number;

      if (menuSize < menuBoxSize) {
        return;
      }
      if (state.offset === menuSize - menuBoxSize) {
        return;
      }
      let offset = state.offset + ITEM_SIZE;
      if (menuBoxSize + offset > menuSize) {
        offset = menuSize - menuBoxSize;
      }
      setState((old) => ({ ...old, offset }));
    },
    [state.offset],
  );

  const onPrev = useCallback(
    function () {
      if (state.offset === 0) {
        return;
      }
      let offset = state.offset - ITEM_SIZE;
      if (offset < 0) {
        offset = 0;
      }
      setState((old) => ({ ...old, offset }));
    },
    [state.offset],
  );

  const onClose = useCallback(
    (e: React.MouseEvent, id: string) => {
      e.stopPropagation();
      deleteTab(id);
    },
    [deleteTab],
  );

  return (
    <>
      <div className="lm-tab-bar-wrapper" ref={domRef}>
        <div className="tab-control-btn" onClick={onPrev}>
          <IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" style={{ rotate: "180deg" }} />
        </div>
        <div className="lm-tab-bar-box">
          <div className="lm-tab-bar-content" style={{ transform: `translate3d(${-state.offset}px,0,0)` }}>
            {tabList.map((pane, idx) => (
              <div key={pane.id} className={`tab-item-box tab-item-box-${idx} ${pane.id === currentId ? "tab-active-item" : ""}`}>
                <div className="tab-index-item" data-index={idx} title={pane.title} onClick={() => changeTabPage(pane.id, true, location)}>
                  <span>
                    {pane.title}
                  </span>
                  <IconFont className="delete-tab-icon" type="icon-guanbi" title="关闭此页签" onClick={(e) => onClose(e, pane.id)} />
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="tab-control-btn" onClick={onNext}>
          <IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" />
        </div>
      </div>
      <TabContextMenuView
        visibel={state.visibel}
        tabIndex={state.tabIndex}
        closeOtherTab={closeOtherTab}
        deleteTab={deleteTab}
        position={state.position}
        tabList={tabList}
        key="TabContextMenuView"
        closeCallback={hideContextMenu}
        refreshTab={refreshTab}
      />
    </>
  );
}

export default TabBars;
