import TabBars from './TabBars';
import TabContents from './TabContents';
import { TabItem } from '../interface';
import './index.less';

interface ITabsProps {
  tabList: TabItem[];
  currentId: string;
  changeTabPage: (id: string) => void;
  deleteTab: (id: string) => void;
  closeOtherTab: (options: { includeIds: string[]; newCurrentId: string }) => void;
  refreshTab: (id: string) => void;
}

function Tabs({ tabList, currentId, changeTabPage, deleteTab, closeOtherTab, refreshTab }: ITabsProps) {
  return (
    <div className={`lm-tab-wrapper-component ${tabList.length === 1 ? 'hide-menu-tab' : ''}`}>
      <TabBars tabList={tabList} currentId={currentId} closeOtherTab={closeOtherTab} changeTabPage={changeTabPage} deleteTab={deleteTab} refreshTab={refreshTab} />
      <TabContents tabList={tabList} currentId={currentId} />
    </div>
  );
}

export default Tabs;
