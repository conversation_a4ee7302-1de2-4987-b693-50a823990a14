import React from 'react';
import { Config } from '@cloud-app-dev/vidc';
import LoaderApp from '../../LoaderApp';
import { getAuthByRouter } from '../../../utils';
import { AppItemType } from '../../LoaderApp/interface';

interface IRenderComponentProps {
  currentId: string;
  tabId: string;
  appName: string;
  pathname?: string;
  [key: string]: any;
}

class RenderComponent extends React.Component<IRenderComponentProps> {
  shouldComponentUpdate(props: IRenderComponentProps) {
    //TODO 目前看不需要无畏的更新，若需要在此判断页签id，获取其他参数
    return props.tabId === props.currentId && props.pathname === window.location.pathname; //防止 外部触发url变化导致的更新
  }

  render() {
    const { appName, pathname, ...subAppProps } = this.props;
    const routerAuth = getAuthByRouter(pathname);
    const config = (Config.app[appName] || {}) as AppItemType;
    return routerAuth && config ? <LoaderApp appConfig={config} appProps={{...subAppProps,pathname}} key={subAppProps.tabId} /> : <></>;
  }
}

export default RenderComponent;
