import { useEffect, useState } from "react";
import RenderComponent from "./RenderComponent";
import { TabItem } from "../interface";

import "./index.less";

function TabContents({ tabList = [], currentId = "" }: { tabList?: TabItem[]; currentId?: string }) {
  const [renderIds, setRenderIds] = useState<string[]>(currentId ? [currentId] : []);
  useEffect(() => setRenderIds((old) => (old.includes(currentId) ? old : [currentId, ...old])), [currentId]);
  return (
    <div className="lm-tab-content-box">
      {tabList.map((pane) =>
        renderIds.includes(pane.id) ? (
          <ItemContent
            key={`${pane.location.pathname}${pane.location.search || ""}`}
            className={`lm-tab-content-item ${currentId === pane.id ? "lm-tab-content-item-active" : ""}`}
            id={pane.id}
            currentId={currentId}
            appName={pane.appName}
            pathname={pane.location.pathname}
          />
        ) : null,
      )}
    </div>
  );
}

interface IItemContentProps {
  currentId: string;
  id: string;
  appName: string;
  className?: string;
  pathname?: string;
}

function ItemContent({ currentId, id, appName, className, pathname }: IItemContentProps) {
  return (
    <div className={className}>
      <RenderComponent key={id} appName={appName} currentId={currentId} tabId={id} pathname={pathname} />
    </div>
  );
}

export default TabContents;
