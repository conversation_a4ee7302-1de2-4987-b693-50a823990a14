.lm-tab-wrapper-component {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .lm-tab-content-box {
    flex: 1;
    position: relative;
    z-index: 99;
    .lm-tab-content-item {
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      opacity: 0;
      transition: opacity 0.3s ease-out;
      will-change: opacity;
      &.lm-tab-content-item-active {
        z-index: 2;
        opacity: 1;
      }
    }
  }
}

.lm-tab-bar-wrapper {
  display: flex;
  overflow: hidden;
  position: relative;
  background-color: var(--tab-bg);
  .tab-control-btn {
    font-size: 14px;
    width: 48px;
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
    color: var(--tab-item-color);
    cursor: pointer;
    background-color: #f3f3f3;
    border-right: 1px solid #e7e7e7;
    border-left: 1px solid #e7e7e7;
  }
}
.lm-tab-bar-content {
  display: flex;
  width: max-content;
  align-items: flex-end;
  height: 100%;
  transition: transform 0.2s;
  user-select: none;
}
.lm-tab-bar-box {
  flex: 1;
  height: var(--tab-height);
  color: var(--tab-item-color);
  overflow: hidden;
  .tab-item-box {
    position: relative;
    width: 155px;
    height: var(--tab-height);
    cursor: pointer;
    background-color: var(--tab-item-bg);
    color: var(--tab-item-color);
    border-right: 1px solid #e7e7e7;
    &:hover {
      color: var(--tab-item-selected-color);
      background-color: var(--tab-item-hover-bg);
    }
    &.tab-active-item {
      color: var(--tab-item-selected-color);
      background-color: var(--tab-item-selected-bg);
    }
  }
  .tab-index-item {
    font-size: var(--fs);
    width: 155px;
    height: var(--tab-height);
    position: relative;
    z-index: 2;
    line-height: var(--tab-height);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .anticon {
      font-size: 18px;
      position: relative;
      top: 1px;
      flex: none;
    }
    & > span {
      display: inline-block;
      width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-left: 20px;
    }
    .delete-tab-icon {
      padding-right: 8px;
      font-size: 16px;
      opacity: 0;
    }
    &:hover {
      .delete-tab-icon {
        opacity: 1;
      }
    }
  }
}

.app-menu-layout-for-tab-bar {
  width: 100%;
}

.tab-context-menu-layout {
  position: absolute;
  z-index: 501;
  background: var(--tab-bg);
  padding: 5px 0;
  border-radius: 4px;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
  ul {
    margin-bottom: 0;
  }
  li {
    padding: 5px 10px;
    cursor: pointer;
    color: var(--tab-item-color);
    &:hover {
      color: var(--primary);
    }
    &.tab-menu-diable {
      color: var(--tab-item-color) !important;
      opacity: 0.8;
      cursor: not-allowed;
    }
  }
}
