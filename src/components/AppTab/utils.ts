import { FeatureItemType } from '@cloud-app-dev/vidc/es/Config/interface';
import { Config } from '@cloud-app-dev/vidc';
import { getInfoByRoute } from '../AppLayout/utils/menu';
import { TabItem } from './interface';
import type { Location } from 'history';

export function findAuthExtByLocation(_location: Location, appName: string) {
  const item: FeatureItemType = getInfoByRoute(_location.pathname) || ({} as FeatureItemType);
  let title = item.name;
  const lstate = _location.state as any;
  if (lstate && lstate.pageTitle) {
    title = lstate.pageTitle;
    delete lstate.pageTitle;
  }
  return {
    icon: item.icon,
    title: title || (Config.app[appName] ? Config.app[appName].name : '404 Not Found'),
  };
}

export function findTabByLocation(location: Location, tabList: TabItem[]) {
  return tabList.find((v) => v.location.pathname + v.location.search === location.pathname + location.search);
}

export function createTab(id: string, appName: string, location: Location) {
  return {
    createTime: Date.now(),
    id,
    appName,
    location,
    ...findAuthExtByLocation(location, appName),
  };
}
