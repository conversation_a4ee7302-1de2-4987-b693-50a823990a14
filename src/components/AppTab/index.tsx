import { useEffect, useState } from "react";
import { Config, cache, SocketEmitter, uuid, nextTick } from "@cloud-app-dev/vidc";
import { TabItem } from "./interface";
import { useLatest, useMemoizedFn, useMount, useThrottleFn, useUnmount } from "ahooks";
import { cloneDeep } from "lodash-es";
import Tabs from "./Tabs";
import { createTab, findTabByLocation } from "./utils";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import type { Location } from "history";

const TAB_SIZE = Config.BSConfig?.tabSize || 15;

interface IAppTabState {
  currentTabKey: string;
  tabList: TabItem[];
  URL_CHANGE: boolean;
}

function AppTab() {
  const navigate = useNavigate();
  const location = useLocation();
  const match = useParams();
  const [state, setState] = useState<IAppTabState>({
    currentTabKey: cache.getCache("currentTabKey", "session"),
    tabList: cache.getCache("tabList", "session") || [],
    URL_CHANGE: false,
  });

  const tabListRef = useLatest(state.tabList);
  const currentIdRef = useLatest(state.currentTabKey);

  const stateChange = useMemoizedFn((options: IAppTabState) => {
    /**
     * @desc 更新select key 缓存
     */
    const { currentTabKey, tabList } = options;
    if (currentTabKey) {
      const tabIndex = tabList.findIndex((v) => v.id === currentTabKey);
      cache.setCache("currentTabKey", currentTabKey, "session");
      SocketEmitter.emit("TabChangeEvent", tabList[tabIndex]);
    }
    if (tabList) {
      cache.setCache("tabList", tabList, "session");
    }

    setState((old) => ({ ...old, ...options }));
  });

  /**
   * @desc 新增一个tab
   */
  const createNewTab = useMemoizedFn((appName: string, currentTabKey: string, location: Location) => {
    const tabList = tabListRef.current;
    const id = uuid();
    const tab = createTab(id, appName, location);
    let newTabList = [] as TabItem[];
    if (tabList.length >= TAB_SIZE) {
      newTabList = tabList;
      newTabList.splice(0, 1);
      newTabList.push(tab);
    } else {
      newTabList = [...tabList, tab];
    }
    //更新tab时间
    const oldIdx = tabList.findIndex((v) => v.id === currentTabKey);
    if (newTabList[oldIdx]) {
      newTabList[oldIdx].createTime = Date.now();
    }
    stateChange({ currentTabKey: id, tabList: newTabList } as IAppTabState);
  });

  /**
   * @desc 替换一个tab
   * @param {*} id tabId
   */
  const replaceTab = useMemoizedFn((id: string, appName: string, location: any) => {
    const tabList = tabListRef.current;
    const tabIndex = tabList.findIndex((v) => v.id === id);
    if (tabIndex === -1) {
      createNewTab(appName, id, location);
    } else {
      const appName = match.subApp as any;
      const newTab = createTab(id, appName, location);
      const repeatTab = findTabByLocation(location, tabList);
      repeatTab ? tabList.splice(tabIndex, 1) : tabList.splice(tabIndex, 1, newTab);
      const newId = repeatTab ? repeatTab.id : id;
      stateChange({ currentTabKey: newId, tabList } as IAppTabState);
    }
  });

  /**
   * @desc 删除tab
   */
  const deleteTab = useMemoizedFn((tabKeykey?: string) => {
    const isUrl = tabKeykey?.startsWith("/");
    const tabList = tabListRef.current;
    const key = tabKeykey && !isUrl ? tabKeykey : currentIdRef.current;
    const index = tabList.findIndex((v) => v.id === key);
    let item: TabItem | undefined = undefined;
    if (tabList.length === 1) {
      return;
    }


    if (!isUrl && key === currentIdRef.current) {
      item = tabList[index + 1] || tabList[index - 1];
    }

    if (isUrl) {
      item = tabList.find((item) => item.location.pathname === tabKeykey);
    }
    
    tabList.splice(index, 1);

    stateChange({ tabList: [...tabList] } as IAppTabState);

    if (item && item.location) {
      const url = `${item.location.pathname}${item.location.search}`;
      const state = cloneDeep(item.location.state);
      nextTick(() => navigate(url, { state }));
    }

    if (!item && isUrl && tabKeykey) {
      navigate(tabKeykey);
    }
  });

  /**
   * @desc 切换tab
   */
  const { run: changeTabPage } = useThrottleFn(
    (key: string, push = true, _location?: Location) => {
      const tabList = tabListRef.current;
      const tabItem = tabList.find((v) => v.id === key);
      if (!tabItem) {
        return;
      }
      const isSelf = _location && _location.pathname === tabItem.location.pathname && _location.search === tabItem.location.search;
      if (currentIdRef.current !== tabItem.id) {
        stateChange({ currentTabKey: key, tabList: tabList } as IAppTabState);
      }

      if (!isSelf && push) {
        const url = `${tabItem.location.pathname}${tabItem.location.search}`;
        const state = cloneDeep(tabItem.location.state);
        nextTick(() => navigate(url, { state }));
      }
    },
    { wait: 400 }
  );

  /**
   *
   * @desc 批量关闭页签
   */
  const closeTabs = useMemoizedFn(({ tabIds = [] as string[], routeUrls = [] as string[], names = [] as string[] }) => {
    const tabList = tabListRef.current;
    const currentTab = tabList.find((v) => v.id === currentIdRef.current) as TabItem;
    if (tabList.length === 0) {
      return;
    }
    const delTabIds = [] as string[];
    tabList.forEach((item) => {
      if (tabIds.includes(item.id) || routeUrls.includes(item.location.pathname) || names.includes(item.appName)) {
        delTabIds.push(item.id);
      }
    });
    let newCurrentId = currentIdRef.current;
    let newTabs = tabList.filter((v) => !delTabIds.includes(v.id));
    if (newTabs.length === 0) {
      newTabs = [currentTab];
    }

    if (!newTabs.map((v) => v.id).includes(currentIdRef.current)) {
      newCurrentId = newTabs[newTabs.length - 1].id;
    }
    stateChange({ tabList: newTabs, currentTabKey: newCurrentId } as IAppTabState);
    const tabItem = newTabs[newTabs.length - 1];
    const url = `${tabItem.location.pathname}${tabItem.location.search}`;
    const state = cloneDeep(tabItem.location.state);
    nextTick(() => navigate(url, { state }));
  });

  /**
   * @desc 批量关闭页签
   * @param {*} param0
   */
  const closeOtherTab = useMemoizedFn(({ includeIds = [], newCurrentId }: { includeIds: string[]; newCurrentId: String }) => {
    const tabList = tabListRef.current;
    const newList = tabList.filter((v) => !includeIds.includes(v.id));
    const currentTab = newList.find((v) => newCurrentId === v.id) || newList[0];
    stateChange({ currentTabKey: currentTab.id, tabList: [...newList] } as IAppTabState);
    const url = `${currentTab.location.pathname}${currentTab.location.search}`;
    const state = cloneDeep(currentTab.location.state);
    nextTick(() => navigate(url, { state }));
  });

  /**
   * @description 刷新页签
   */
  const refreshTab = useMemoizedFn((id: string) => {
    const newId = uuid();
    const list = tabListRef.current;
    const index = list.findIndex((v) => id === v.id);
    list[index].id = newId;
    stateChange({ currentTabKey: newId, tabList: [...list] } as IAppTabState);
    if (currentIdRef.current !== id) {
      const item = list[index];
      const url = `${item.location.pathname}${item.location.search}`;
      const state = cloneDeep(item.location.state);
      nextTick(() => navigate(url, { state }));
    }
  });

  const updateLocationState = useMemoizedFn((id, state) => {
    const list = tabListRef.current;
    const index = list.findIndex((v) => id === v.id);
    list[index].location.state = state;
    stateChange({ tabList: [...list] } as IAppTabState);
  });

  /**
   * 事件监听
   */
  useEffect(() => {
    SocketEmitter.on("closeTab", deleteTab);
    SocketEmitter.on("closeTabs", closeTabs);
    return () => {
      SocketEmitter.off("closeTab", deleteTab);
      SocketEmitter.off("closeTabs", closeTabs);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * @description 初始化
   */
  useMount(() => {
    const appName = match.subApp || "";
    const { tabList, currentTabKey } = state;
    const locationObj = {
      pathname: location.pathname,
      state: cloneDeep(location.state),
      search: location.search,
    } as Location;
    const currentTab = tabList.find((v) => v.id === currentTabKey) || findTabByLocation(locationObj, tabList);
    !currentTab ? createNewTab(appName, currentTabKey, locationObj) : changeTabPage(currentTab.id, false, locationObj);
  });

  /**
   * 销毁
   */
  useUnmount(() => {
    sessionStorage.removeItem("currentTabKey");
    //TODO 要求切换系统才删除
    sessionStorage.removeItem("tabList");
  });

  /**
   * @description 路由变化
   */
  useEffect(() => {
    const appName = match.subApp as string;
    const findTab = findTabByLocation(location, tabListRef.current);
    const lstate = location.state as any;
    const locationObj = {
      pathname: location.pathname,
      state: cloneDeep(lstate),
      search: location.search,
    } as Location;
    if (findTab?.id === currentIdRef.current) {
      return updateLocationState(currentIdRef.current, locationObj.state);
    }
    if (lstate && lstate.isReplace) {
      delete lstate.isReplace;
      return replaceTab(currentIdRef.current, appName, locationObj);
    }
    if (findTab) {
      return changeTabPage(findTab.id, false, locationObj);
    }
    createNewTab(appName, currentIdRef.current, locationObj);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname, location.search, match.subApp, location.state]);

  return (
    <Tabs
      tabList={state.tabList}
      currentId={state.currentTabKey}
      refreshTab={refreshTab}
      changeTabPage={changeTabPage}
      deleteTab={deleteTab}
      closeOtherTab={closeOtherTab}
    />
  );
}

export default AppTab;
