import { cache } from "@cloud-app-dev/vidc";

export default ({ children, code, other }: any) => {
  const _USER_SESSION_ = cache.getCache("_USER_SESSION_", "session") || {};
  const root = _USER_SESSION_?.loginName === _USER_SESSION_?.username && _USER_SESSION_?.username === "admin";
  const functionIds = _USER_SESSION_?.functionIds || [];
  const isTrue = root ? true : functionIds.includes(Number(code));
  return <>{isTrue ? children : other ? other : null}</>;
};
