import { Service ,cache} from '@cloud-app-dev/vidc';
import URL from './url';
import RSA from '@src/service/rsa';

const defaultHeaders = {
  Authorization: cache.getCache('token', 'session'),
};
class MainService {
  /**
   * 根据id查询用户信息
   * @param {*} id
   */
  queryConfigureList() {
    return Service.htttp({
      method: 'get',
      url: URL.queryConfigureList.value,
      requestId: URL.queryConfigureList.requestId,
    });
  }
  //通用类接口
  uploadImg(options) {
    return Service.htttp({
      headers: defaultHeaders,
      url: URL.uploadImg.value,
      method: 'post',
      data: options,
      requestId: URL.uploadImg.requestId,
    });
  }
  /**
   * 修改用户的头像
   * @param {*} data
   */
  changeUserAvatar(data) {
    return Service.htttp({
      headers: defaultHeaders,
      method: 'post',
      url: URL.changeUserAvatar.value,
      data,
      requestId: URL.changeUserAvatar.requestId,
    });
  }
  getPublicKey() {
    const headers = {
      Authorization: cache.getCache('token', 'session'),
    };
    return Service.htttp({
      headers,
      url: URL.getPublicKey.value,
      method: 'post',
      requestId: URL.getPublicKey.requestId,
    }).then((res) => res.data);
  }
  /**
   * @desc 修改密码
   * @param {*} options
   */
  async changePassword(options) {
    const headers = {
      Authorization: cache.getCache('token', 'session'),
    };
    const { publicKey } = await this.getPublicKey();
    options.newPassword = RSA.encrypt(options.newPassword, publicKey);
    options.oldPassword = RSA.encrypt(options.oldPassword, publicKey);
    return Service.htttp({
      url: URL.changePassword.value,
      headers,
      method: 'post',
      data: options,
      requestId: URL.changePassword.requestId,
    });
  }
  /**
   * @desc 用户登出
   */
  loginOut() {
    return Service.htttp({
      headers: defaultHeaders,
      url: URL.loginOut.value,
      method: 'post',
      requestId: URL.loginOut.requestId,
    });
  }

  getUserByToken() {
    return Service.htttp({
      url: '/api/user/v1/getUserByToken',
      method: 'post',
      headers: {
        Authorization: cache.getCache('token', 'session'),
      },
      requestId: 'getUserInfo',
    });
  }
  getKvStore(data) {
    return Service.htttp({
      url: '/api/user/userKvStore/v1/getKvStore/api/user/userKvStore/v1/getKvStore',
      method: 'post',
      headers: {
        Authorization: cache.getCache('token', 'session'),
      },
      data,
      requestId: 'getUserInfo',
    });
  }
  queryPictureFavorites(data) {
    return Service.htttp({
      url: '/api/user/favorite/v1/queryPictureFavorites',
      method: 'post',
      headers: {
        Authorization: cache.getCache('token', 'session'),
      },

      requestId: 'getUserInfo',
    });
  }
}

const main = new MainService();

export default main;
