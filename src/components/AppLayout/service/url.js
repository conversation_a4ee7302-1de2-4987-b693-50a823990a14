import config from './prefix';

const api = config.api;

const REQ_URL = {
  queryConfigureList: {
    value: `${api}/micro/configure/v1/queryConfigureList`,
    label: '配置列表',
    requestId: 'queryConfigureList',
  },
  uploadImg: {
    value: `${api}/user/v1/img/uploadImg`,
    label: '图片上传',
    requestId: 'uploadImg',
  },
  changeUserAvatar: {
    value: `${api}/user/v1/changeUserAvatar`,
    requestId: 'changeUserAvatar',
    label: '用户头像修改',
  },
  getPublicKey: {
    value: `${api}/user/v2/getPublicKey`,
    requestId: 'getPublicKey',
    label: '获取key',
  },
  changePassword: {
    value: `${api}/user/v2/changePassword`,
    requestId: 'changePassword',
    label: '修改密码',
  },
  loginOut: {
    value: `${api}/user/v1/loginOut`,
    label: '登出',
    requestId: 'loginOut',
  },
  getKvStore: {
    value: `${api}/user/userKvStore/v1/getKvStore/api/user/userKvStore/v1/getKvStore`,
    label: 'getKvStore',
    requestId: 'getKvStore',
  },
  queryPictureFavorites: {
    value: `${api}/user/favorite/v1/queryPictureFavorites`,
    label: 'queryPictureFavorites',
    requestId: 'queryPictureFavorites',
  }
};

export default REQ_URL;
