import { Layout } from "antd";
import Header from "./Header";
// import Background from "./Background";
import SocketModal from "./SocketModal";
import "./index.less";

interface IAppLayoutProps {
  content?: JSX.Element;
  children?: JSX.Element;
}

function AppLayout({ content, children }: IAppLayoutProps) {
  return (
    <div className="app-style-container">
      <SocketModal />
      <Layout className="app-layout-container">
        {/* <Background /> */}
        <Layout.Header className="app-layout-header">
          <Header />
        </Layout.Header>
        <Layout.Content className="app-layout-content">{children || content}</Layout.Content>
      </Layout>
    </div>
  );
}

export default AppLayout;
