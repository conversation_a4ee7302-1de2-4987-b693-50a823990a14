.app-style-container {
  width: 100%;
  height: 100%;
  display: flex;
  .app-layout-container {
    width: 100%;
    height: 100%;
    background-color: transparent;
  }
  .app-inline-layout {
    padding-left: 72px;
  }
  .app-layout-header {
    min-height: var(--header-height);
    height: max-content;
    padding: 0;
    line-height: var(--header-height);
    color: var(--header-color);
    background-color: var(--header-bg);
    width: 100%;
  }
}
.ant-notification-notice-close-x {
  .anticon-close {
    font-size: var(--fs-large);
    font-weight: 600;
    color: var(--gray6);
    position: relative;
    top: 5px;
  }
}
.ant-notification-notice-message {
  // height: 32px;
  line-height: 32px;
  // font-size: var(--fs-large);
  font-weight: 600;
  // padding: 0 12px;
  // background: linear-gradient(90deg, var(--primary-dark) -10%, var(--secondary1-dark) 100%);
  // color:  var(--gray2);
}
.ant-notification-notice {
  // background:var(--secondary1-dark)
  padding: 0px !important;
  box-shadow:
    0px 3px 14px 2px rgba(0, 0, 0, 0.05),
    0px 8px 10px 1px rgba(0, 0, 0, 0.06),
    0px 5px 5px -3px rgba(0, 0, 0, 0.1);
}
.item-Socket {
  width: 372px;
  height: 287px;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.9);
  padding: 16px;
  padding-top: 10px;
  padding-right: 0px;
  display: flex;
  position: relative;
  flex-direction: column;
  background: #fff;
  // cursor: pointer;

  .item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    .item-header-right {
      color: rgba(0, 0, 0, 0.6);
    }
    .item-header-left {
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }
  }
  .item-img {
    flex: 1;
    width: 100%;
    margin-bottom: 8px;
    height: 209px;
    border-radius: 2px;
    overflow: hidden;
    img {
      width: 100%;
      background-color: transparent;
    }
  }
  .item-name {
    padding: 0 12px;
    font-size: var(--fs-small);
    .anticon {
      margin-right: 8px;
    }
  }
  .item-adress {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // padding: 0 12px;
    // font-size: var(--fs-small);
    .anticon {
      margin-right: 8px;
    }
    .item-adress-deviceName {
      white-space: nowrap; /* 确保文本不换行 */
      overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
    }
  }
}
:where(.css-dev-only-do-not-override-1kuana8).ant-modal-confirm-warning .ant-modal-confirm-body>.anticon, :where(.css-dev-only-do-not-override-1kuana8).ant-modal-confirm-confirm .ant-modal-confirm-body>.anticon{
  color: var(--primary);
}
.promptTask-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 8px 0px;
  .promptTask-box-item {
    .promptTask-box-item-title {
      color: rgba(0, 0, 0, 0.6);
      /* Body/Medium */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-bottom: 10px;
    }
    .promptTask-box-item-data {
      color: rgba(0, 0, 0, 0.9);
      /* Title/Large */
      font-family: "PingFang SC";
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px;
    }
  }
}
