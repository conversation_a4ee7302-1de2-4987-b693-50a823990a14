import { getMenusByFeatures } from '../../../utils';

function comparisonPahtname(pathname1: string, pathname2: string) {
  if (!pathname1 || !pathname2) {
    return false;
  }
  if (pathname1 === pathname2) {
    return true;
  }
  const arr1 = pathname1.split('/');
  const arr2 = pathname2.split('/');
  if (arr1.length !== arr2.length) {
    return false;
  }

  return arr1.every((v, i) => {
    if (v === arr2[i]) {
      return true;
    }
    if (arr2[i].includes(':')) {
      return true;
    }
    return false;
  });
}


export function getInfoByRoute(pathname: string) {
  let item;
  const authArr = getMenusByFeatures();
  item = authArr.find((v: any) => v.routerUrl === pathname);
  if (!item) {
    item = authArr.find((v: any) => v.routerUrl && v.routerUrl.includes(pathname));
  }
  if (!item) {
    item = authArr.find((v: any) => comparisonPahtname(pathname, v.routerUrl));
  }
  return item;
}

export function getParentIds(id: string) {
  const list = getMenusByFeatures();
  const arr = [] as string[];
  const fn = function (fid: string) {
    const item = list.find((v) => v.id === fid);
    if (item) {
      arr.push(fid);
      if (item.parentId) {
        fn(item.parentId);
      }
    }
  };
  fn(id);
  return arr;
}
