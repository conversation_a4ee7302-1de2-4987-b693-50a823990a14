import React from 'react';
import { IconFont } from '@cloud-app-dev/vidc';
import { FeatureItemType } from '@cloud-app-dev/vidc/es/Config/interface';

interface ISubGroupProps {
  item: FeatureItemType & any;
  onClick: (e: React.MouseEvent, item: FeatureItemType) => void;
  currentMenuKeys: string[];
}

function SubGroup({ item, onClick, currentMenuKeys }: ISubGroupProps) {
  return (
    <div className="app-horizontal-submenu-pop">
      <div className="sub-menu-box">
        {item.children.map((v: FeatureItemType) => (
          <div key={v.id} className={`app-menu-item-lv3 ${currentMenuKeys.includes(v.id) ? 'app-menu-item-lv3-selected' : ''}`} onClick={(e) => onClick(e, v)}>
            {
              v.icon ? <IconFont type={v.icon || 'icon-L_Bar_Objectification1'} /> : null
            }
            
            {v.name}
          </div>
        ))}
      </div>
    </div>
  );
}

export default SubGroup;
