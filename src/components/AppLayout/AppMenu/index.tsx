import React, { useMemo, useCallback, useContext, useState, useEffect, useRef, useLayoutEffect } from "react";
import { treeHelper, uuid, IconFont } from "@cloud-app-dev/vidc";
import { Popover } from "antd";
import SubGroup from "./SubGroup";
import { useThrottleFn, useSize } from "ahooks";
import { FeatureItemType } from "@cloud-app-dev/vidc/es/Config/interface";
import Context from "../context";
import { getMenusByFeatures } from "../../../utils";
import { useNavigate } from "react-router-dom";
import "./index.less";

interface IAppHorizontalMenuProps {
  renderMenu?: boolean;
}

function AppHorizontalMenu({ renderMenu = true }: IAppHorizontalMenuProps) {
  const [state, setState] = useState({ visible: false, offset: 0 });
  const { menus } = useContext(Context);
  const navigate = useNavigate();
  const filterMenus = useMemo(() => getMenusByFeatures().filter((v) => +v.status === 1 && +v.type === 1), []);
  const menuList = useMemo(() => (renderMenu ? treeHelper.computTreeList(filterMenus || [], "id", "parentId") : []), [filterMenus, renderMenu]);
  const menuBoxRef = useRef<HTMLDivElement>(null);

  const size = useSize(menuBoxRef);

  //计算是够需要移动
  useEffect(() => {
    const menuBox = menuBoxRef.current?.querySelector(".menu-layout-box") as HTMLDivElement;
    const visible = (size?.width || 0) < menuBox.clientWidth;
    setState((old) => ({ visible, offset: visible ? old.offset : 0 }));
  }, [size?.width]);

  const onMenuSelect = useCallback(
    (e: React.MouseEvent, item: FeatureItemType & any) => {
      const menu = filterMenus.find((v) => v.id === item.id) as any;
      navigate(`${menu?.routerUrl}${e.ctrlKey || e.metaKey ? `?key=${uuid()}` : ""}`);
    },
    [filterMenus, navigate],
  );

  const onPrev = () => {
    const container = document.querySelector(".app-menu-layout")?.querySelector(".menu-layout-wrapper") as HTMLDivElement;
    let offset = container.scrollLeft - 150;
    if (offset < 0) {
      offset = 0;
    }
    setState((old) => ({ ...old, offset }));
  };

  const onNext = () => {
    const container = document.querySelector(".app-menu-layout")?.querySelector(".menu-layout-wrapper") as HTMLDivElement;
    const menuBox = container.querySelector(".menu-layout-box") as HTMLDivElement;
    const containerSize = container.clientWidth;
    let offset = container.scrollLeft + 150;
    if (offset + containerSize >= menuBox.clientWidth) {
      offset = menuBox.clientWidth - containerSize;
    }
    setState((old) => ({ ...old, offset }));
  };

  //选中菜单变化计算位置
  useEffect(() => {
    const index = menuList.findIndex((v) => menus.includes(v.id));
    if (index === -1 || !state.visible) {
      return;
    }

    const container = menuBoxRef.current?.querySelector(".menu-layout-wrapper") as HTMLDivElement;
    const menuBox = container.querySelector(".menu-layout-box") as HTMLDivElement;
    const menuItem = menuBox.querySelectorAll(".app-menu-item-top")[index] as HTMLDivElement;
    const item_rect = menuItem.getBoundingClientRect();
    const container_rect = container.getBoundingClientRect();
    let offset = container.scrollLeft;
    const currentArea = [offset, offset + container_rect.width];
    const item_offset = item_rect.x + offset - container_rect.x;

    if (item_offset < currentArea[0] || item_offset > currentArea[1]) {
      offset = item_offset;
    }
    setState((old) => ({ ...old, offset }));
  }, [menuList, menus, state.visible]);

  const { run } = useThrottleFn(onMenuSelect, { wait: 500 });

  useLayoutEffect(() => {
    const container = document.querySelector(".app-menu-layout")?.querySelector(".menu-layout-wrapper") as HTMLDivElement;
    container.scrollTo({ left: state.offset });
  }, [state.offset]);

  return (
    <div className="app-menu-layout app-horizontal-menu-layout" ref={menuBoxRef}>
      <div className="menu-control-btn" onClick={onPrev}>
        {state.visible && <IconFont type="icon-renwupeizhi_shebeirenwu_shouqi" />}
      </div>

      <div className="menu-layout-wrapper">
        <div className="menu-layout-box">
          {menuList.map((item: any) =>
            !item.children ? (
              <div
                className={`app-menu-item-top ${menus.includes(item.id) ? "app-menu-item-top-selected" : ""}`}
                onClick={(e) => run(e, item)}
                key={item.id}
              >
                <IconFont type={item.icon || "icon-L_Bar_Objectification1"} />
                {item.name}
              </div>
            ) : (
              <Popover
                key={item.id}
                overlayClassName="menu-sub-layout"
                arrowContent={null}
                trigger="hover"
                destroyTooltipOnHide={true}
                placement="bottomLeft"
                content={<SubGroup currentMenuKeys={menus || []} item={item} onClick={(e: React.MouseEvent, v: FeatureItemType) => run(e, v)} />}
              >
                <div className={`app-menu-item-top ${menus.includes(item.id) ? "app-menu-item-top-selected" : ""}`}>
                  <IconFont type={item.icon || "icon-L_Bar_Objectification1"} />
                  <span>{item.name}</span>
                  <IconFont
                    type="icon-renwupeizhi_shebeirenwu_shouqi-copy"
                    style={{ fontSize: 12, transformOrigin: "center", transform: "rotate(90deg) translate(5px,-5px)" }}
                  />
                </div>
              </Popover>
            ),
          )}
        </div>
      </div>
      <div className="menu-control-btn" onClick={onNext}>
        {state.visible && <IconFont type="icon-renwupeizhi_shebeirenwu_shouqi-copy" />}
      </div>
    </div>
  );
}

export default AppHorizontalMenu;
