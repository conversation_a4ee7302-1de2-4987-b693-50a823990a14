.app-horizontal-menu-layout {
  flex: 1;
  overflow: hidden;
  font-size: 14px;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;

  .app-menu-item-top {
    display: flex;
    height: 32px;
    min-width: 136px;
    padding: 0 16px;
    cursor: pointer;
    color: var(--nav-item-color);
    border-radius: 6px;
    justify-content: left;
    align-items: center;
    margin-right: 10px;
    transition: all 0.3s;
    font-family: "PingFang SC";
    font-size: 14px;
    font-weight: 400;
    &:last-child {
      margin-right: 0;
    }
    .anticon {
      font-size: 16px;
      padding-right: 10px;
      color: var(--nav-icon-color);
      position: relative;
    }
    &:hover,
    &.app-menu-item-top-selected {
      background-color: #165dff;
      color: #fff;
      font-weight: 500;
      .anticon {
        color: #fff;
      }
    }
  }
  .menu-control-btn {
    width: 48px;
    height: 2em;
    font-size: 16px;
    cursor: pointer;
    text-align: center;
    line-height: 2em;
    border-radius: var(--radius2);
    color: var(--nav-icon-color);
    &:hover {
      color: var(--nav-item-color);
    }
  }

  .menu-layout-wrapper {
    height: var(--nav-height);
    overflow-x: scroll;
    overflow-y: auto;
    white-space: nowrap;
    position: relative;
    flex: 1;
    &::-webkit-scrollbar {
      display: none;
    }
    .blur-mask {
      position: absolute;
      width: 129px;
      height: 100%;
      right: 0;
      top: 0;
      pointer-events: none;
    }
    .menu-layout-box {
      height: 100%;
      align-items: center;
      justify-content: space-around;
      width: max-content;
      // width: max-content;
      transition: transform 0.2s ease-in;
      display: flex;
    }
  }
}

.menu-sub-layout {
  padding-top: 6px;
  font-size: 16px;
  .ant-popover-inner-content {
    padding-left: 0;
    padding-right: 0;
  }
  .ant-popover-arrow {
    display: none;
  }
  .ant-popover-inner {
    border-radius: 8px;
  }
  .app-menu-item-lv3 {
    padding: 0 20px;
    height: 36px;
    line-height: 36px;
    margin-top: 5px;
    &:first-child {
      margin-top: 0;
    }
    cursor: pointer;
    color: #333;
    transition: all 0.3s;
    border-radius: 4px;
    min-width: 160px;
    .anticon {
      margin-right: 10px;
      font-size: 18px;
      position: relative;
      top: 2px;
    }
    &:hover,
    &.app-menu-item-lv3-selected {
      background-color: #e8f3ff;
      color: var(--primary);
    }
  }
}
