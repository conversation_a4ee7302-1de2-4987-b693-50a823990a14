import React from 'react';
import AppLogo from '../AppLogo';
import AppTools from '../AppTools';
import AppMenu from '../AppMenu';
import { UserInfoType } from '@cloud-app-dev/vidc/es/AppContext/interface';

import './index.less';

interface IAppHeaderProps {
  className?: string;
  systemLogo?: string;
  defaultSrc?: string;
  systemName?: string;
  appTools?: React.ReactNode;
  userInfo?: UserInfoType;
}

function AppHeader({ className = '', appTools }: IAppHeaderProps) {
  return (
    <div className={`app-header-content ${className}`} style={{ display: 'flex', width: '100%', height: '100%', overflow: 'hidden' }}>
      <AppLogo />
      <AppMenu />
      <AppTools>{appTools}</AppTools>
    </div>
  );
}

export default AppHeader;
