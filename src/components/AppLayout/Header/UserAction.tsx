import { useContext, useState } from "react";
import { Avatar, Modal } from "antd";
import ModifyPassword from "./ModifyPassword";
import { AppContext, Service, cache } from "@cloud-app-dev/vidc";
import { IconFont } from "@cloud-app-dev/vidc";
import IconSpan from "./IconSpan";
import atx from "../../../assets/image/atx.png";
import { UserOutlined } from "@ant-design/icons";

function UserAction() {
  const { user } = useContext(AppContext.Context as any) as any;
  const [state, setState] = useState({ changeAvatarVisible: false, changePwdVisible: false });

  //退出登录
  const loginout = () => {
    Modal.confirm({
      title: "提示",
      centered:true,
      content: "是否确认退出登录？",
      onOk: () => {
        const headers = { Authorization: cache.getCache("token", "session") };
        Service.http({ url: "/api/amc-user-server/userManage/logout", method: "post", headers });
        sessionStorage.clear();
        localStorage.clear();
        window.location.replace("/login");
      }
    })
   
  };

  return (
    <div className="user-popover-content">
      {/* <div className="user-info">
        <div className="user-info-left fl">
          <Avatar icon={<UserOutlined />} src={atx} />
        </div>
        <div className="user-info-right fl">
          <span className="current-user fl" title={user.realName ?? user.loginName}>
            {user.loginName}
          </span>
          <div>
            <IconSpan type="antd" mode="inline" className="fl" onClick={() => setState((old) => ({ ...old, changeAvatarVisible: true }))} icon="user" label="修改头像" />
            <IconSpan
              mode="inline"
              className="fl"
              onClick={() => setState((old) => ({ ...old, changePwdVisible: true }))}
              icon="icon-shebeiguanli_bianjimingcheng"
              label="修改密码"
            />
          </div>
        </div>
      </div>
      <IconSpan type="antd" mode="inline" className="login-out" onClick={loginout} icon="poweroff" label="退出登录" /> */}
      <div className="user-popover-content-item" onClick={() => setState((old) => ({ ...old, changePwdVisible: true }))} >
        <IconFont style={{fontSize:16,marginRight:4}}  type="icon-mima"/>
        修改密码
      </div>
      <div className="user-popover-content-item"  onClick={loginout}>
        <IconFont style={{fontSize:16,marginRight:4}} type="icon-chevron-right1" />
        退出登录
      </div>
      <ModifyPassword
        className="user-password-popup"
        title="修改密码"
        visible={state.changePwdVisible}
        onCancel={() => setState((old) => ({ ...old, changePwdVisible: false }))}
      />
      {/* <ModifyUserAvatar className="user-avatar-popup" visible={state.changeAvatarVisible} onCancel={() => setState((old) => ({ ...old, changeAvatarVisible: false }))} /> */}
    </div>
  );
}
export default UserAction;
