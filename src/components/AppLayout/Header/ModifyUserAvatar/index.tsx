import { message, Modal, Upload } from "antd";
import { useContext, useState } from "react";
import type { RcFile, UploadChangeParam, UploadFile, UploadProps } from "antd/es/upload/interface";
import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { AppContext } from "@cloud-app-dev/vidc";
import "./index.less";

const beforeUpload = (file: RcFile) => {
  const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
  if (!isJpgOrPng) {
    message.warning("You can only upload JPG/PNG file!");
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.warning("Image must smaller than 2MB!");
  }
  return isJpgOrPng && isLt2M;
};

function ModifyUserAvatar({ onCancel, visible }: any) {
  const { user } = useContext(AppContext.Context as any) as any;
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>(user.userAvatarUrl as string);
  const handleChange: UploadProps["onChange"] = (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === "uploading") {
      setLoading(true);
      return;
    }
    if (info.file.status === "done") {
      // Get this url from response in real world.
    }
  };
  return (
    <Modal open={visible} title="修改头像" width={400} onCancel={onCancel} okText="确定" cancelText="取消">
      <Upload
        name="avatar"
        listType="picture-card"
        className="avatar-uploader"
        showUploadList={false}
        style={{ display: "flex", alignItems: "center" }}
        action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
        beforeUpload={beforeUpload}
        onChange={handleChange}
      >
        {imageUrl ? (
          <img src={imageUrl} alt="avatar" style={{ width: "100%" }} />
        ) : (
          <div>
            {loading ? <LoadingOutlined rev={undefined} /> : <PlusOutlined rev={undefined} />}
            <div style={{ marginTop: 8 }}>Upload</div>
          </div>
        )}
      </Upload>
    </Modal>
  );
}

export default ModifyUserAvatar;
