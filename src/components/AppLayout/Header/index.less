.app-tools-layout {
  display: flex;
  align-items: center;
  line-height: var(--nav-height);
  height: var(--nav-height);
  justify-content: flex-end;
  .theme-select-layout {
    width: 150px;

    .ant-select-selector {
      background-color: transparent !important;
    }
  }
  .user-item {
    height: 100%;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: var(--fs-small);
    -webkit-transition: color 0.3s;
    transition: color 0.3s;
    white-space: nowrap;
    // &:hover {
    //   color: var(--primary-light);
    // }
    .anticon {
      font-size: 16px;
      padding-right: 4px;
    }
    .ant-avatar-lg {
      border-radius: var(--radius2);
    }
  }

  .user-info {
    padding: 0 8px;
    .ant-avatar {
      border-radius: 5px;
    }
    .user-name {
      padding: 0 5px;
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      line-height: 20px;
      .anticon {
        font-size: 10px;
      }
    }
    & > span {
      display: flex;
      align-items: center;
    }
  }
}
.goback-popover-box {
  .ant-popover-inner-content {
    padding: 3px 8px;
  }
}
.user-popover-box {
  .ant-popover-inner {
    background: var(--content-info-box-bg);
  }
  .ant-popover-inner-content {
    padding: 0;
  }
  .ant-popover-arrow {
    border-top-color: var(--content-info-box-bg) !important;
    border-left-color: var(--content-info-box-bg) !important;
  }
}

.user-popover-content {
  .icon-span:hover .anticon,
  .icon-span:hover span {
    color: var(--primary-dark);
  }
  // width: 260px;
  // width: 110px;
  .user-info {
    display: flex;
    padding: 10px;
    border-bottom: 1px solid var(--bd-color);
    .ant-avatar {
      width: 64px;
      height: 64px;
      border-radius: 50%;
    }
  }
  .user-info-right {
    color: var(--color);
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: flex-start;
    .current-user {
      color: var(--color-dark);
      font-size: var(--fs);
      font-weight: bold;
      padding-bottom: 10px;
    }
    .btn-content {
      display: flex;
      flex-wrap: wrap;
      .icon-span {
        font-size: 12px;
        line-height: 200%;
        margin-left: 10px;
        color: var(--primary);
        .anticon {
          color: inherit;
        }
        &:hover {
          color: var(--primary-dark);
        }
      }
    }
  }
  .login-out {
    padding: 8px;
    text-align: center;
    color: var(--disabled);
    font-size: var(--fs-small);
    .anticon {
      color: inherit;
    }
    &:hover {
      color: var(--icon);
    }
  }
  .worktable-page-btn {
    padding: 8px 8px;
    .page-item {
      line-height: 34px;
      font-size: 12px;
      color: var(--color);
      cursor: pointer;
      .anticon {
        font-size: 16px;
        margin-right: 2px;
        color: var(--icon);
        vertical-align: -3px;
      }
      &:hover {
        color: var(--primary);
        .anticon {
          color: var(--primary);
        }
      }
    }
  }
}

.login-put-popup {
  margin-top: -30px;
  inset: 80px 8px auto auto !important;
  .ant-popover-arrow {
    display: none !important;
  }
  .ant-popover-inner {
    border-radius: 3px;
  }
  .user-pop-tools {
    width: 100px;
    & > div {
      padding: 2px 5px;
    }
  }
}

.user-popover-content {
  // width: 260px;
  // width: 110px;
  .user-popover-content-item {
    display: flex;
    margin-bottom: 8px;
    color: rgba(0, 0, 0, 0.9);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    padding: 4px 8px;
    border-radius: 2px;
    cursor: pointer;
    &:last-child {
      margin-bottom: 0px;
    }
    &:hover{
      color: #165DFF;
      background: #E8F3FF;
    }
  }
  .user-info {
    width: 100%;
    height: 100px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    .user-info-left {
      .ant-avatar {
        width: 64px;
        height: 64px;
        border-radius: 50%;
      }
    }
    .user-info-right {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
      justify-content: space-around;
      align-items: flex-start;
      .current-user {
        align-items: flex-start;
        color: #333;
        font-size: 14px;
        font-weight: bold;
        padding-bottom: 10px;
      }
      & > div {
        width: 100%;
        .icon-span {
          justify-content: flex-start;
          align-items: center;
          line-height: 200%;
          margin-right: 10px;
        }
      }
    }
  }
  .login-out {
    padding-top: 10px;
  }
}

.login-put-popup {
  .ant-popover-inner-content {
    padding: 0;
  }
}
.user-popover-content {
  .user-info {
    border-bottom: 1px solid #eee;
    .user-info-right {
      .current-user {
        color: #333;
      }
      & > div {
        width: 100%;
        .icon-span {
          color: #ff8800;
        }
      }
    }
  }
  .login-out {
    color: #999;
  }
}
