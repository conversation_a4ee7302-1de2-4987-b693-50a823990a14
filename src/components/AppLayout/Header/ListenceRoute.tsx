import { useContext, useEffect } from 'react';
import { getInfoByRoute, getParentIds } from '../utils/menu';
import Context from '../context';
import { useHistory } from '@cloud-app-dev/vidc';

let timer: NodeJS.Timeout;
function ListenceRoute() {
  const history = useHistory();
  const { updateMenus } = useContext(Context);
  useEffect(() => {
    const fn = () => {
      const pathname = history.location.pathname;
      const item = getInfoByRoute(pathname);
      clearTimeout(timer);
      timer = setTimeout(() => {
        if (item) {
          const ids = getParentIds(item.id);
          updateMenus(ids);
        }
      }, 200);
    };
    fn();
    const unlisten = history.listen(fn);
    return () => unlisten();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return <></>;
}

export default ListenceRoute;
