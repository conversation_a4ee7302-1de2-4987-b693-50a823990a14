import { Service ,cache} from '@cloud-app-dev/vidc';
import RSA from '@src/service/rsa';

export const getPublicKey = async () => {
  const res = await Service.http({
    url: '/api/amc-user-server/authorization/getPulicKey',
  });
  return res.data as string;
};

export async function changePassword(values: any) {
  const headers = { Authorization: cache.getCache('token', 'session') };

  return getPublicKey()
    .then((publicKey) => {
      const data = { accountId: cache.getCache('userInfo', 'session')?.accountId, oldPassword: values.oldPassword, newPassword: values.newPassword };
      data.oldPassword = RSA.encrypt(data.oldPassword, publicKey);
      data.newPassword = RSA.encrypt(data.newPassword, publicKey);
      return data;
    })
    .then((data) => {
      return Service.http({
        method: 'post',
        url: '/api/amc-user-server/userManage/changePassword',
        headers,
        data,
      });
    });
}
