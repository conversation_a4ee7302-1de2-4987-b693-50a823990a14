import { useEffect, useState } from 'react';
import { Form, Input, message, Modal } from 'antd';
import { changePassword } from './utils';
import './index.less';


const FormItem = Form.Item;

function ModifyPassword({ onCancel, className = '', visible }: any) {
  const [form] = Form.useForm();
  const [state, setState] = useState({ confirmDirty: false, formKey: Math.random() });
  useEffect(()=>{
    form.resetFields()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[visible])
  function handleConfirmBlur(e: any) {
    const value = e.target.value;
    setState((old) => ({ ...old, confirmDirty: !!value }));
  }

  function compareToFirstPassword(_: any, value: any) {
    if (value && value !== form.getFieldValue('newPassword')) {
      return Promise.reject(new Error('密码不一致!'));
    } else {
      return Promise.resolve();
    }
  }

  async function handleSubmit() {
    try {
      const values = await form.validateFields();
      const res = await changePassword(values);
      if(res?.code !== 0){
        message.warning(res.message);
      }else{
        message.success('密码修改成功!');
      }
      onCancel();
    } catch (e) {
      console.error(e);
      message.warning('密码修改失败！');
    }
  }

  function validateToNextPassword(_: any, value: any) {
    const regs = [/[a-z]/g, /[A-Z]/g, /[0-9]/g, /[`~!@#$%^&*()_\-+=<>?:"{}|,.\\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/g];
    const flags = regs.map((v) => v.test(value));
    const flag = flags.filter((v) => v).length < 3 || value.length < 8 || value.length > 18;
    if (flag) {
      return Promise.reject(new Error('密码为8-18位，包含大写字母，小写字母，数字，特殊字符中的三种'));
    }
    return Promise.resolve();
  }

  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  };

  return (
    <Modal
      className={`change-psw-modal ${className}`}
      title="修改密码"
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      okText="修改密码"
      cancelText="取消"
      centered
      key={state.formKey}
    >
      <Form form={form} name="passoword-form">
        <FormItem {...formItemLayout} name="oldPassword" label="旧密码" rules={[{ required: true, message: '请输入旧密码!' }]}>
          <Input type="password" />
        </FormItem>
        <FormItem
          {...formItemLayout}
          label="新密码"
          name="newPassword"
          rules={[
            {
              required: true,
              message: '请输入新密码!',
            },
            {
              validator: validateToNextPassword,
            },
          ]}
        >
          <Input type="password" />
        </FormItem>
        <FormItem
          {...formItemLayout}
          name="confirm"
          label="重复新密码"
          dependencies={['newPassword']}
          rules={[
            {
              required: true,
              message: '请重复新密码!',
            },
            {
              validator: compareToFirstPassword,
            },
          ]}
        >
          <Input type="password" onBlur={handleConfirmBlur} />
        </FormItem>
      </Form>
    </Modal>
  );
}

export default ModifyPassword;
