import { useContext, useEffect, useRef } from 'react';
import { Popover, Avatar, Tooltip, Badge } from 'antd';
import ToolsItem from './ToolsItem';
import UserAction from './UserAction';
import atx from '../../../assets/image/atx.png';
import { AppContext, IconFont, useHistory ,SocketEmitter} from '@cloud-app-dev/vidc';
import { useFullscreen, useToggle } from 'ahooks';

function AppTools() {
  const { user } = useContext(AppContext.Context as any) as any;
  const histroy = useHistory();
  const ref = useRef(document.body);
  const [isFullscreen, { toggleFullscreen }] = useFullscreen(ref);
  const [alarm,{setLeft,setRight}] = useToggle(false)
  useEffect(()=>{
    SocketEmitter.on('alarmMessage',setRight)
    SocketEmitter.on('clearMessage',setLeft)
    return ()=>{
      SocketEmitter.on('alarmMessage',setRight)
      SocketEmitter.on('clearMessage',setLeft)
    }
  },[])
  return (
    <div className="app-tools-layout">
      <ToolsItem
        className="MyInfo"
        onClick={() => {
          // histroy.push('/aisApp/alarmCenter');
          histroy.push('/aisApp/eventAlarm');
        }}
      >
        <Tooltip title="我的消息">
          <Badge dot={alarm} offset={[-5, 0]}>
            <IconFont type="icon-tongzhi" style={{color:'var(--gray2)',fontSize:20}}/>
          </Badge>
        </Tooltip>
      </ToolsItem>
      <ToolsItem className="user-info">
        <Tooltip title={isFullscreen ? '退出全屏' : '全屏'}>
          {isFullscreen ? (
            <IconFont style={{ fontSize: '20px' }} type="icon-quanping_fuben" onClick={toggleFullscreen} />
          ) : (
            <IconFont style={{ fontSize: '16px' }} type="icon-quanping" onClick={toggleFullscreen} />
          )}
        </Tooltip>
      </ToolsItem>
      <ToolsItem className="user-info">
        <Popover trigger="hover" overlayClassName="login-put-popup" placement="bottomRight" content={<UserAction />}>
          {/* <Avatar src={atx}></Avatar> */}
          <IconFont type='icon-user-circle' style={{fontSize:20}}/>
          <span style={{ marginLeft: '8px', fontSize: '14px' }}>
            {user.loginName}
            <IconFont style={{ fontSize: '14px', marginLeft: '5px', color: '#fff' }} type="icon-chevron-down"></IconFont>
          </span>
        </Popover>
      </ToolsItem>
    </div>
  );
}

export default AppTools;
