.icon-span{
  font-size: 12px;
  cursor: pointer;
  &>i {
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &:hover {
    i, span {
      color: var(--primary-dark);
    }
  }
}
.icon-span.disabled{
  color: #ddd!important;
  cursor: not-allowed;
  &:hover {
    i, span {
      cursor: not-allowed;
      color: #ddd !important;
    }
  }
}
.icon-span.icon-span-vertical {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  &>i {
    margin-bottom: 6px;
  }
}
.icon-span.icon-span-horizontal {
  display: flex;
  align-items: center;
  justify-content: center;
  &>i {
    margin-right: 4px;
  }
}