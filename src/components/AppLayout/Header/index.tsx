import { useCallback, useContext, useMemo, useState } from 'react';
import { AppContext, Config } from '@cloud-app-dev/vidc';
import AppHeader from '../AppHeader';
import ListenceRoute from './ListenceRoute';
import AppTools from './Tools';
import Context from '../context';
import './index.less';

function Header() {
  const { user } = useContext(AppContext.Context as any) as any;
  const [state, setState] = useState({ menus: [] as string[] });
  const updateMenus = useCallback((menus: string[]) => setState((old) => ({ ...old, menus })), []);
  const value = useMemo(() => ({ menus: state.menus, updateMenus }), [state.menus, updateMenus]);
  return (
    <Context.Provider value={value}>
      <AppHeader userInfo={user} systemLogo={Config.bs.systemLogo} systemName={Config.bs.systemName} appTools={<AppTools />} />
      <ListenceRoute />
    </Context.Provider>
  );
}

export default Header;
