import { useEffect, useMemo, useRef } from "react";
import { useWebSocket } from "ahooks";
import { notification, Button, Modal, message } from "antd";
import { useHistory, IconFont, cache, SocketEmitter } from "@cloud-app-dev/vidc";
import { updateAlarmTaskState } from "@src/service/ais_app";
import radioUrl from "./alarm.mp3";
import dayjs from "dayjs";
const callPathStyle:any = {
  'doctorsAppearFrequently':'/aisApp/frequentDoctorAppearancesDetails',
  'monitoringRoomLeftPost':'/aisApp/surveillanceRoomPoliceOffCutyDetails',
  'enterRoomRestTime':'/aisApp/restTimePolicePrisonDetails',
  'detainInSsolation':'/aisApp/solitaryDetentionDetails',
  3100000000000:'/aisApp/identityLibraryDetails',
}
const Content = ({ data }: { data: any }) => {
  const history = useHistory();
  return (
    <div className="item-Socket">
      <div className="item-header">
        <div className="item-header-left">{data.algorithmName}</div>
        <div className="item-header-right">{data.alarmTime ? dayjs(data.alarmTime).format("YYYY-MM-DD HH:mm:ss") : "-"}</div>
      </div>
      <div className="item-img">
        <img src={data?.storageImageUrl} />
      </div>
      <span className="item-adress">
        <div className="item-adress-deviceName" title={data?.deviceName || "-"}>
          {data?.deviceName || "-"}
        </div>
        <Button
          onClick={() => {
            if(callPathStyle[data?.algorithmId]){
              history.push(callPathStyle[data?.algorithmId], { id: data?.id });
            }else{
              history.push(`/aisApp/eventDetails`, { id: data?.id });
            }
          }}
          style={{ fontSize: 12, background: "var(--primary)" }}
          type="primary"
        >
          查看详情
        </Button>
      </span>
      {/* <span className="item-name" title={dayjs(data?.alarmTime).format("YYYY-MM-DD HH:mm:ss")}>
        <IconFont type="icon-gaojingzhongxin_shijian" />
        {dayjs(data?.alarmTime).format("YYYY-MM-DD HH:mm:ss")}
      </span> */}
    </div>
  );
};

export default function SocketModal() {
  const audioRef = useRef<HTMLAudioElement>(null);
  const timer = useRef<any>(null);
  const history = useHistory();
  const path = useMemo(() => (process.env.NODE_ENV === "development" ? "*************:2236" : window.location.host), []);
  const { latestMessage } = useWebSocket(
    `${process.env.NODE_ENV === "development" ? "ws" : "ws"}://${path}/ws/socket?userId=${cache.getCache("userId", "session")}&authorization=${cache.getCache("token", "session")}`,
  );

  const taskStart = (data: any) => {
    Modal[data?.taskIds.length ? 'confirm' : 'warning']({
      title: "任务处理提示",
      centered: true,
      width: 488,
      content: data?.taskIds.length
        ? "批量创建任务失败，请重新创建任务"
        : `已配置任务${data?.taskIds.length || 0}个，请及时框选ROI区域，是否启动所有设备任务？`,
      onOk: () => {
        if (data?.taskIds.length) {
          updateAlarmTaskState({
            ids: data?.taskIds || [],
            taskState: "QUEUEING",
          }).then((res) => {
            if (res.code === 0) {
              message.success("操作成功");
            } else {
              message.warning(res.message);
            }
          });
        }
      },
    });
  };
  useEffect(() => {
    if (latestMessage?.data) {
      const wsData = JSON.parse(latestMessage?.data);
      if (wsData.type === "alarm") {
        openNotification(wsData.data);
        SocketEmitter.emit("alarmMessage");
        clearTimeout(timer.current);
        audioRef.current && audioRef.current.play();
        timer.current = setTimeout(() => {
          audioRef.current && audioRef.current.pause();
        }, 5000);
      }
      if (wsData.type === "createAlarmTask") {
        taskStart(wsData.data);
      }
    }
  }, [latestMessage]);
  const openNotification = (data: any) => {
    notification.open({
      message: ``,
      // message: `告警-${data?.algorithmName}`,
      description: <Content data={data} />,
      onClick: () => {
        if(callPathStyle[data?.algorithmId]){
          history.push(callPathStyle[data?.algorithmId], { id: data?.id });
        }else{
          history.push(`/aisApp/eventDetails`, { id: data?.id });
        }
      },
      className: "myAlarm",
      style: {},
      // closable: false,
      closeIcon: false,
      // duration: 30000,
      duration: 3,
      placement: "bottomRight",
    });
  };
  return <audio src={radioUrl} ref={audioRef} autoPlay={false} loop={false} />;
}
