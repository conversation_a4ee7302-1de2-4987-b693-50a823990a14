import { Spin } from 'antd';

export default function Loading({ error }: { error?: boolean }) {
  return (
    <div className="app-loading" style={{ width: '100%', height: '100%', paddingTop: '12%', textAlign: 'center' }}>
      <Spin spinning={!error} />
      <span style={{ paddingLeft: 10,color:'#000' }}>
        {!error ? '正在加载...' : '加载失败， '}
        {error && (
          <i
            style={{ fontStyle: 'normal', cursor: 'pointer', color: 'var(--primary-light)' }}
            onClick={() => {
              sessionStorage.clear();
              window.location.replace('/login');
            }}
          >
            刷新！
          </i>
        )}
      </span>
    </div>
  );
}
