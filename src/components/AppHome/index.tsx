import React from "react";
import AutoExit from "../AutoExit";
import Loading from "./Loading";
import AppLayout from "../AppLayout";
import InitialRequest from "../InitialRequest";
import AppTab from "../AppTab";
import "./index.less";

function AppHome() {
  return (
    <AutoExit>
      <InitialRequest Spin={<Loading error={false} />}>
        <AppLayout>
           <React.Suspense>
          <AppTab />
          </React.Suspense>
        </AppLayout>
      </InitialRequest>
    </AutoExit>
  );
}

export default AppHome;
