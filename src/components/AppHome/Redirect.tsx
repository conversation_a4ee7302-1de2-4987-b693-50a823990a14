import { useEffect } from "react";
import { cache, Config } from "@cloud-app-dev/vidc";
import AutoExit from "../AutoExit";
import { useLocation, useNavigate } from "react-router-dom";
import InitialRequest from "../InitialRequest";
import Loading from "./Loading";
import { Modal } from "antd";

function Redirect() {
  const location = useLocation();
  const navigate = useNavigate();
  useEffect(() => {
    if (location.pathname === "/") {
      const features = Config.features.filter((v) => v.type === 1 && Config.featureIds.includes(v.id) && v.routerUrl);
      // const defailtMenu = features[0] as any;
      const defailtMenu = {
        id: 30000310,
        functionName: "实时视频",
        status: 1,
        type: 1,
        apiIds: ["106001001"],
        routerUrl: "/aisApp/rtVideo",
        sort: 310,
        parentId: 30000322,
      };
      if (defailtMenu && defailtMenu.routerUrl) {
        cache.setCache("selectMenuKeys", [defailtMenu.id], "session");
        navigate(defailtMenu.routerUrl, { replace: true });
      } else {
        Modal.warning({
          title: "提示",
          content: "当前用户的角色没有有效的功能权限，请联系管理员分配！",
          okText: "退出",
          closable: false,
          onOk: () => {
            sessionStorage.clear();
            window.location.href = "/login";
          },
        });
      }
    }
  }, [location.pathname]);
  return <AutoExit />;
}

function AppRedirect() {
  return (
    <AutoExit>
      <InitialRequest Spin={<Loading error={false} />}>
        <Redirect />
      </InitialRequest>
    </AutoExit>
  );
}

export default AppRedirect;
