import React, { useContext } from 'react';
import { initialization, InitDataType } from './utils';
import { AppContext, Config, useSimpleState } from '@cloud-app-dev/vidc';
import { nextTick } from '@cloud-app-dev/vidc';
import { useMount } from 'ahooks';

let _BASE_DATA_INIT = false;

interface IInitialRequestProps {
  children: React.ReactNode;
  Spin?: any;
  customInitialization?: () => Promise<InitDataType>;
}
function InitialRequest({ children, Spin, customInitialization }: IInitialRequestProps) {
  const { updateUser } = useContext(AppContext.Context as any) as any;
  const [state, updateState] = useSimpleState({ isInit: false, error: false });

  useMount(async () => {
    if (_BASE_DATA_INIT) {
      updateState({ isInit: true });
      return;
    }
    try {
      const fetchInitData = customInitialization || initialization;
      const { userFeatures, systemFeatures, userInfo } = await fetchInitData();
      userInfo && updateUser(userInfo);
      userFeatures && Config.registerFeatrueIds(userFeatures);
      systemFeatures && Config.registerPlatformFeature(systemFeatures);
      nextTick(() => updateState({ isInit: true }));
      _BASE_DATA_INIT = true;
    } catch (e) {
      updateState({ error: true });
      console.error('接口初始化失败！', e);
    }
  });
  return <>{state.isInit ? children : React.cloneElement(Spin, { error: state.error })}</>;
}

export default InitialRequest;
