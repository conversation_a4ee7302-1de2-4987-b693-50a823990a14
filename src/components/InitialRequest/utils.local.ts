import { Service,cache } from '@cloud-app-dev/vidc';
import { UserInfoType } from '@cloud-app-dev/vidc/es/AppContext/interface';
import { FeatureItemType } from '@cloud-app-dev/vidc/es/Config/interface';
import { orderBy } from 'lodash-es';

export type InitDataType = {
  userInfo: UserInfoType;
  userFeatures: string[];
  systemFeatures: FeatureItemType[];
};

export async function initialization(): Promise<InitDataType> {
  const headers = { Authorization: cache.getCache('token', 'session') };
  const userId = cache.getCache('userId', 'session');
  const result = await Service.http({ url: `/statics/config/user.info.json`, method: 'get', headers, data: { id: userId } });
  const userInfo = result.data as UserInfoType;
  let featureIds = [];

  const systemFeatureUrl = `/statics/config/feature.list.json`;
  // const localoFeatureUrl = `/statics/config/local.feature.list.json?${Date.now()}`;
  // const arr = [Service.http({ url: systemFeatureUrl, headers, method: 'get' }), Service.http({ url: localoFeatureUrl, headers, method: 'get' })];
  // const systemFeatures = await Promise.all(arr).then(([sysFeature, locFeature]) => [...sysFeature.data.map((v: any) => ({ ...v, name: v.functionName })), ...locFeature.data]);
  const systemFeatures = await Service.http({ url: systemFeatureUrl, headers, method: 'get' }).then((res) => res.data.map((v: any) => ({ ...v, routerUrl: v.routeUrl })));
  if (+userInfo.userType === 1) {
    featureIds = systemFeatures.map((v: any) => v.id);
  } else {
    const roleInfo = await await Service.http({ url: `/api/user/role/v3/queryFunctionsByRoleIdList`, method: 'post', headers, data: userInfo.roleId });
    featureIds = roleInfo.data.map((v: any) => v.id);
  }
  return {
    userInfo,
    userFeatures: featureIds,
    systemFeatures: orderBy(systemFeatures, 'sort', 'asc') as FeatureItemType[],
  };
}
