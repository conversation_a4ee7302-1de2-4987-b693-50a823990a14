{"code": 0, "codeRemark": "操作成功", "message": null, "data": {"list": [{"id": 30000314, "functionName": "云台控制", "status": 1, "type": 2, "apiIds": ["102001001", "102001002"], "sort": 300, "parentId": 30000310}, {"id": 30000411, "functionName": "新增用户", "status": 1, "type": 2, "parentId": 30000410}, {"id": 30000412, "functionName": "编辑用户", "status": 1, "type": 2, "parentId": 30000410}, {"id": 30000413, "functionName": "删除用户", "status": 1, "type": 2, "parentId": 30000410}, {"id": 30000440, "functionName": "局点管理", "status": 1, "type": 1, "apiIds": [], "routerUrl": "/systemManagement/gamePointEdit", "sort": 430, "parentId": 30000400}, {"id": 30000441, "functionName": "新增局点", "status": 1, "type": 2, "parentId": 30000440}, {"id": 30000442, "functionName": "编辑", "status": 1, "type": 2, "parentId": 30000440}, {"id": 30000443, "functionName": "删除局点", "status": 1, "type": 2, "parentId": 30000440}, {"id": 30000450, "functionName": "时间规则", "status": 1, "type": 1, "apiIds": [], "routerUrl": "/systemManagement/timeRule", "sort": 430, "parentId": 30000400}, {"id": 30000451, "functionName": "新增", "status": 1, "type": 2, "parentId": 30000450}, {"id": 30000452, "functionName": "编辑", "status": 1, "type": 2, "parentId": 30000450}, {"id": 30000420, "functionName": "角色管理", "status": 1, "type": 1, "apiIds": ["101005001"], "routerUrl": "/systemManagement/role", "sort": 420, "parentId": 30000400}, {"id": 30000410, "functionName": "用户管理", "status": 1, "type": 1, "apiIds": ["101007001"], "routerUrl": "/systemManagement/user", "sort": 410, "parentId": 30000400}, {"id": 30000400, "functionName": "系统管理", "status": 1, "type": 1, "icon": "icon-xitongguanli", "sort": 400}, {"id": 30000421, "functionName": "编辑角色", "status": 1, "type": 2, "parentId": 30000420}, {"id": 30000422, "functionName": "新增角色", "status": 1, "type": 2, "parentId": 30000420}, {"id": 30000423, "functionName": "删除角色", "status": 1, "type": 2, "parentId": 30000420}, {"id": 30000211, "functionName": "任务详情", "status": 1, "type": 2, "apiIds": ["301001003"], "routerUrl": "/aisApp/testDetails", "parentId": 30000210}, {"id": 30000212, "functionName": "任务配置", "status": 1, "type": 2, "apiIds": ["301001003"], "sort": 330, "parentId": 30000210}, {"id": 30000213, "functionName": "任务处理（取消、启动、暂停）", "status": 1, "type": 2, "apiIds": ["301001003"], "sort": 330, "parentId": 30000210}, {"id": 30000101, "functionName": "告警处理", "status": 1, "type": 2, "apiIds": ["301003004"], "parentId": 30000324}, {"id": 30000103, "functionName": "告警详情", "status": 1, "type": 2, "apiIds": ["301003002"], "routerUrl": "/aisApp/alarmDetails", "parentId": 30000324}, {"id": 30000102, "functionName": "告警隐藏/取消隐藏", "status": 1, "type": 2, "apiIds": ["301003003"], "parentId": 30000324}, {"id": 30000200, "functionName": "任务中心", "status": 1, "type": 1, "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 200}, {"id": 30000324, "functionName": "事件告警", "status": 1, "type": 1, "routerUrl": "/aisApp/eventAlarm", "sort": 210, "parentId": 30000200}, {"id": 30000330, "functionName": "事件告警详情", "status": 1, "type": 2, "routerUrl": "/aisApp/eventDetails", "parentId": 30000324}, {"id": 30000325, "functionName": "身份识别库", "status": 1, "type": 1, "routerUrl": "/aisApp/identityLibrary", "sort": 210, "parentId": 30000200}, {"id": 30000329, "functionName": "单独羁押", "status": 1, "type": 1, "routerUrl": "/aisApp/solitaryDetention", "sort": 210, "parentId": 30000200}, {"id": 30000345, "functionName": "告警处理", "status": 1, "type": 2, "apiIds": ["301003004"], "parentId": 30000329}, {"id": 30000346, "functionName": "告警详情", "status": 1, "type": 2, "apiIds": ["301003002"], "routerUrl": "/aisApp/alarmDetails", "parentId": 30000329}, {"id": 30000347, "functionName": "告警隐藏/取消隐藏", "status": 1, "type": 2, "apiIds": ["301003003"], "parentId": 30000329}, {"id": 30000331, "functionName": "医生频繁出现", "status": 1, "type": 1, "routerUrl": "/aisApp/frequentDoctorAppearances", "sort": 210, "parentId": 30000200}, {"id": 30000342, "functionName": "告警处理", "status": 1, "type": 2, "apiIds": ["301003004"], "parentId": 30000331}, {"id": 30000343, "functionName": "告警详情", "status": 1, "type": 2, "apiIds": ["301003002"], "routerUrl": "/aisApp/alarmDetails", "parentId": 30000331}, {"id": 30000344, "functionName": "告警隐藏/取消隐藏", "status": 1, "type": 2, "apiIds": ["301003003"], "parentId": 30000331}, {"id": 30000332, "functionName": "休息时间民警进入监舍", "status": 1, "type": 1, "routerUrl": "/aisApp/restTimePolicePrison", "sort": 210, "parentId": 30000200}, {"id": 30000339, "functionName": "告警处理", "status": 1, "type": 2, "apiIds": ["301003004"], "parentId": 30000332}, {"id": 30000340, "functionName": "告警详情", "status": 1, "type": 2, "apiIds": ["301003002"], "routerUrl": "/aisApp/alarmDetails", "parentId": 30000332}, {"id": 30000341, "functionName": "告警隐藏/取消隐藏", "status": 1, "type": 2, "apiIds": ["301003003"], "parentId": 30000332}, {"id": 30000333, "functionName": "监控室民警脱岗", "status": 1, "type": 1, "routerUrl": "/aisApp/surveillanceRoomPoliceOffCuty", "sort": 210, "parentId": 30000200}, {"id": 30000334, "functionName": "识别详情", "status": 1, "type": 2, "routerUrl": "/aisApp/identityLibraryDetails", "sort": 210, "parentId": 30000325}, {"id": 30000336, "functionName": "告警处理", "status": 1, "type": 2, "apiIds": ["301003004"], "parentId": 30000333}, {"id": 30000337, "functionName": "告警详情", "status": 1, "type": 2, "apiIds": ["301003002"], "routerUrl": "/aisApp/alarmDetails", "parentId": 30000333}, {"id": 30000338, "functionName": "告警隐藏/取消隐藏", "status": 1, "type": 2, "apiIds": ["301003003"], "parentId": 30000333}, {"id": 30000321, "functionName": "下载录像", "status": 1, "type": 2, "apiIds": ["106001005"], "sort": 330, "parentId": 30000320}, {"id": 30000320, "functionName": "录像回放", "status": 1, "type": 1, "apiIds": ["106001005"], "routerUrl": "/aisApp/videoReplay", "sort": 320, "parentId": 30000322}, {"id": 30000310, "functionName": "实时视频", "status": 1, "type": 1, "apiIds": ["106001001"], "routerUrl": "/aisApp/rtVideo", "sort": 310, "parentId": 30000322}, {"id": 30000312, "functionName": "服务管理", "status": 1, "type": 1, "apiIds": ["106001001"], "routerUrl": "/aisApp/server", "sort": 340, "parentId": 30000323}, {"id": 30000326, "functionName": "注册服务", "status": 1, "type": 2, "apiIds": ["106001001"], "sort": 340, "parentId": 30000312}, {"id": 30000327, "functionName": "编辑服务", "status": 1, "type": 2, "apiIds": ["106001001"], "sort": 340, "parentId": 30000312}, {"id": 30000328, "functionName": "删除服务", "status": 1, "type": 2, "apiIds": ["106001001"], "sort": 340, "parentId": 30000312}, {"id": 30000313, "functionName": "平台管理", "status": 1, "type": 1, "apiIds": ["106001001"], "routerUrl": "/aisApp/platform", "sort": 350, "parentId": 30000323}, {"id": 30000316, "functionName": "平台接入", "status": 1, "type": 2, "apiIds": ["106001001"], "sort": 350, "parentId": 30000313}, {"id": 30000317, "functionName": "平台编辑", "status": 1, "type": 2, "apiIds": ["106001001"], "sort": 350, "parentId": 30000313}, {"id": 30000318, "functionName": "平台处理（启用、暂停、删除）", "status": 1, "type": 2, "apiIds": ["106001001"], "sort": 350, "parentId": 30000313}, {"id": 30000319, "functionName": "平台访问", "status": 1, "type": 2, "apiIds": ["106001001"], "sort": 350, "parentId": 30000313}, {"id": 30000322, "functionName": "视频监控", "status": 1, "type": 1, "icon": "icon-shipinjiankong", "apiIds": ["106001005"], "sort": 330}, {"id": 30000323, "functionName": "接入管理", "status": 1, "type": 1, "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "apiIds": ["106001005"], "sort": 330}, {"id": 30000460, "functionName": "场所标签管理", "status": 1, "type": 1, "routerUrl": "/systemManagement/device/labelEdit", "sort": 350, "parentId": 30000400}, {"id": 30000461, "functionName": "新增场所", "status": 1, "type": 2, "parentId": 30000460}, {"id": 30000462, "functionName": "编辑", "status": 1, "type": 2, "parentId": 30000460}, {"id": 30000470, "functionName": "算法规则", "status": 1, "type": 1, "routerUrl": "/systemManagement/algorithmicRule", "sort": 360, "parentId": 30000400}, {"id": 30000430, "functionName": "设备管理", "status": 1, "type": 1, "apiIds": ["302001001", "302001002", "302001003", "302001004", "302001005", "302002001", "302002002", "302002003"], "routerUrl": "/systemManagement/device", "sort": 430, "parentId": 30000400}, {"id": 30000431, "functionName": "标签管理", "status": 1, "type": 2, "parentId": 30000430}, {"id": 30000432, "functionName": "移动设备", "status": 1, "type": 2, "parentId": 30000430}, {"id": 30000433, "functionName": "标定", "status": 1, "type": 2, "parentId": 30000430}, {"id": 30000210, "functionName": "设备任务管理", "status": 1, "type": 1, "apiIds": ["301002001", "301001001", "301001002", "301001003", "301001004", "301001005", "301001006", "301001007", "301001008", "301004001", "301004002", "301004003", "301001007"], "routerUrl": "/aisApp/testView", "sort": 210, "parentId": 30000200}, {"id": 30000348, "functionName": "告警详情", "status": 1, "type": 2, "apiIds": ["301003002"], "routerUrl": "/aisApp/frequentDoctorAppearancesDetails", "parentId": 30000331}, {"id": 30000349, "functionName": "告警详情", "status": 1, "type": 2, "apiIds": ["301003002"], "routerUrl": "/aisApp/restTimePolicePrisonDetails", "parentId": 30000332}, {"id": 30000350, "functionName": "告警详情", "status": 1, "type": 2, "apiIds": ["301003002"], "routerUrl": "/aisApp/solitaryDetentionDetails", "parentId": 30000329}, {"id": 30000351, "functionName": "告警详情", "status": 1, "type": 2, "apiIds": ["301003002"], "routerUrl": "/aisApp/surveillanceRoomPoliceOffCutyDetails", "parentId": 30000333}], "totalCount": 34, "totalPage": 4}}