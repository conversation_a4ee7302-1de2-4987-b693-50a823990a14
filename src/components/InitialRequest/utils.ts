import { Service ,cache} from '@cloud-app-dev/vidc';
import { UserInfoType } from '@cloud-app-dev/vidc/es/AppContext/interface';
import { FeatureItemType } from '@cloud-app-dev/vidc/es/Config/interface';
import { orderBy } from 'lodash-es';
import acc from "./acc.json";

export type InitDataType = {
  userInfo: UserInfoType;
  userFeatures: string[];
  systemFeatures: FeatureItemType[];
};

export async function initialization(): Promise<InitDataType> {
  const headers = { Authorization: cache.getCache('token', 'session') };
  const result = await Service.http({ url: `/api/amc-user-server/userManage/getSelfUserInfo`, headers });
  const userInfo = result.data as UserInfoType;
  let featureIds = [];
  const platformType = `/api/amc-user-server/platformInfo/info`;
  const platformTypes = await Service.http({ url: platformType, headers })
  window._PLATFORM_TYPE = platformTypes.data.platformType
  console.log('平台类型', window._PLATFORM_TYPE) // 1:主平台、2:子平台
  const systemFeatureUrl = `/api/amc-user-server/function/list/query/3`;
  const systemFeatures = await Service.http({ url: systemFeatureUrl, headers })
    // .then((res) => acc.data.list.map((v: any) => ({ ...v, name: v.functionName })))
    .then((res) => res.data.list.map((v: any) => ({ ...v, name: v.functionName })))
    .then((list) => list.filter((v: any) => +v.status === 1));

  if (+userInfo.userType === 1) {
    featureIds = systemFeatures.map((v: any) => v.id);
  } else {
    featureIds = result.data.functionIds;
  }
  return {
    userInfo,
    userFeatures: featureIds,
    systemFeatures: orderBy(systemFeatures, 'sort', 'asc') as FeatureItemType[],
  };
}
