import { useToggle } from 'ahooks';
import { Switch } from 'antd';
import { IconFont } from '@cloud-app-dev/vidc';
import React, { ReactElement } from 'react';
import './index.less';
interface GradientTitleType {
  margin?: string;
  checked?: boolean;
  title: string;
  onChange?: (v: boolean) => void;
  children?: ReactElement;
  headerStyle?: React.CSSProperties;
}
const GradientTitle = ({ margin, title, onChange, checked, children, headerStyle }: GradientTitleType) => {
  const [isShow, { toggle }] = useToggle(true);
  return (
    <div className="GradientTitle" style={{ margin: margin || '0' }}>
      <div className="header" style={headerStyle}>
        {children && <IconFont type="icon-renwupeizhi_lunxunrenwu_qiyong" style={{ marginRight: '16px', transform: isShow ? 'rotate(90deg)' : 'rotate(0deg)' }} onClick={toggle} />}
        <div className="titlebox">{title}</div>
        {onChange && (
          <div className="switchbox">
            <Switch checked={checked} onChange={onChange} />
          </div>
        )}
      </div>
      {isShow && children && <div className="content">{children || <></>}</div>}
    </div>
  );
}
export default GradientTitle