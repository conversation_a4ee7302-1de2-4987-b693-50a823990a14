import React, { useContext, useEffect } from "react";
import { useHistory, nextTick, AppContext } from "@cloud-app-dev/vidc";

export interface IAutoExitProps {
  children?: React.ReactNode;
}

function AutoExit({ children }: IAutoExitProps) {
  const { loginStatus } = useContext(AppContext.Context as any) as any;
  const history = useHistory();

  useEffect(() => {
    if (!loginStatus) {
      nextTick(() => history.replace("/login"));
    }
  }, [loginStatus]);

  return <>{loginStatus ? children : null}</>;
}

export default AutoExit;
