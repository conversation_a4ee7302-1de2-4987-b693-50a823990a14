import type { DrawerProps } from 'antd';
import React from 'react';

export interface IDrawerProps extends DrawerProps {
  cancelText?: string;

  onOk?: (e: React.MouseEvent) => void;

  /**
   * @description 确认按钮文本
   * @default 确定
   * @type string
   */
  okText?: string;
}

export interface IFooterProps {
  onClose: (e: React.MouseEvent) => void;
  cancelText: string;
  okText: string;
  onOk: (e: React.MouseEvent) => void;
}
