import { Drawer as AntdDraw<PERSON> } from "antd";
import Footer from "./Footer";
import { IDrawerProps } from "./interface";

import "./index.less";

function Drawer({ style, onClose, className, onOk, cancelText, okText, children, ...props }: IDrawerProps) {
  return (
    <AntdDrawer
      style={{ ...style, textAlign: "left" }}
      className={`lm-drawer-container ${className}`}
      onClose={onClose}
      footer={<Footer onClose={onClose as any} onOk={onOk as any} cancelText={cancelText as string} okText={okText as string} />}
      {...props}
    >
      {children}
    </AntdDrawer>
  );
}
Drawer.defaultProps = {
  getContainer: false,
  className: "",
  cancelText: "取消",
  okText: "确定",
};

export default Drawer;
