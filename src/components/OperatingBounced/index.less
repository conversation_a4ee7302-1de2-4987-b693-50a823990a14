.myModal {
  .ant-modal-content {
    .ant-modal-footer {
      border: none;
      padding: 0px;
      margin: 0px;
    }
    .ant-modal-body {
      padding: 0px;
      .topicon {
        width: 135px;
        height: 4px;
        margin: 0 auto;
      }
      .mycontent {
        height: max-content;
        position: relative;
        .modal-close {
          position: absolute;
          right: 0px;
          top: 0px;
          color: var(--gray6);
          cursor: pointer;
        }
        .iconbox {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          margin: 32px auto 22px;
          display: flex;
          justify-content: center;
          align-items: center;
          box-sizing: border-box;
        }
        .Bouncedcontent,
        .small-info {
          width: 100%;
          margin-top: 16px !important;
          font-weight: 400;
          font-size: 16px;
          line-height: 26px;
          color: rgba(0, 0, 0, 0.6);
          margin: 0 auto 0px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
        .color-text {
          color: rgba(255, 173, 74, 1);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .info {
          font-size: 14px;
        }
        .footer {
          width: 100%;
          margin-top: 24px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .btns {
            width: max-content;
            display: flex;
            align-items: center;

            .ant-btn-primary {
              margin-left: 16px;
            }
          }
        }
      }
    }
  }
}
