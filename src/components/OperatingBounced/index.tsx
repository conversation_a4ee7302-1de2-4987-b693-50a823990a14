import React from "react";
import { Mo<PERSON> ,Button} from 'antd';
import './index.less'
import GradientTitle from "../GradientTitle";
import { useUpdateEffect } from "ahooks";
import { IconFont } from "@cloud-app-dev/vidc";
interface Bounced{
    isShow:boolean;
    cancel?:boolean;
    title?:string;
    icon:any;
    content:string;
    colortext?:string;
    info?:string;
    symbol?:string;
    loading?:boolean;
    onOk:()=>void;
    onCancel:()=>void;
}
const OperatingBounced = ({loading=false,isShow,cancel=true,title,symbol,icon,content,colortext,info,onOk,onCancel}:Bounced) => {
    useUpdateEffect(()=>{
        if(!loading){
            onCancel()
        }
    },[loading])
    return (
        <Modal footer={false} width={500} open={isShow} onOk={onOk} onCancel={onCancel} closable={false} className="myModal" destroyOnClose={true}>
            {/* <div className="topicon"></div> */}
            <div className="mycontent">
                <div className="modal-close"><IconFont type="icon-guanbi" onClick={onCancel}/></div>
                {
                    title && <GradientTitle title={title}></GradientTitle>
                }
                {/* <div className='iconbox'>{icon}</div> */}
                <div className='Bouncedcontent' title={content}>{content} <span className="color-text" title={colortext}>{colortext}</span> <span>{symbol}</span></div>
                <div className='small-info' title={info}>{info}</div>
                <div className="footer">
                    <div className="btns">
                    {
                        cancel && <Button key="back" onClick={onCancel}>取消</Button>
                    }
                    <Button key="submit" type="primary" onClick={onOk} loading={loading} style={{width:'max-content'}}>确定</Button>
                    </div>
                </div>
            </div>
        </Modal>  
    )
}
export default OperatingBounced