import { useMemo } from 'react';
import { Config , cache, uuid } from '@cloud-app-dev/vidc';
import { uniq, compact } from 'lodash-es';
import { useLocation, useParams } from 'react-router-dom';
import LoaderApp from '../LoaderApp';
import { getMenusByFeatures } from '../../utils';

const mockId = uuid();

/**
 * @description 无多页签时提供的渲染容器
 * @param param0ß  ß
 * @returns
 */
function AppRender() {
  const params = useParams();
  const location = useLocation();
  cache.setCache('currentTabKey', mockId, 'session');
  const config = useMemo(() => Config.app[params.subApp as string] ?? {}, [params.subApp]);
  const authAppIds = useMemo(() => uniq(compact(getMenusByFeatures().map((v) => v.appId))), []);
  const flag = useMemo(() => config.status === 1 && authAppIds.includes(config.id), [authAppIds, config.id, config.status]);
  return flag ? <LoaderApp appConfig={config as any} key={location.pathname} appProps={{ currentId: mockId, tabId: mockId }} /> : <></>;
}

export default AppRender;
