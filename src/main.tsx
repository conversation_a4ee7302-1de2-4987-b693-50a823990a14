import ReactDOM from "react-dom/client";
import App from "./App";
import {IconFont} from '@cloud-app-dev/vidc'
import { injectServiceMiddleware } from "./middleware";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import isToday from "dayjs/plugin/isToday";
import duration from "dayjs/plugin/duration";
import isYesterday from "dayjs/plugin/isYesterday";
import customParseFormat from "dayjs/plugin/customParseFormat";
import "./index.less";

dayjs.locale("zh-cn");
dayjs.extend(isToday);
dayjs.extend(duration);
dayjs.extend(isYesterday);
dayjs.extend(customParseFormat);
injectServiceMiddleware()
IconFont.registerIconFont("/statics/font/icon-font_old.js")
IconFont.registerIconFont("/statics/font/iconfont_v2.js")

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(<App />);
