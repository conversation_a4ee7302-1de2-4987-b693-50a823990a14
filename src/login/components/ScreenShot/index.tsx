
import { useState } from 'react';
import { useCountDown } from 'ahooks';
import { message } from 'antd';
import { getLoginCode } from '@src/service/login';
import './index.less';

function ScreenShot() {
  const [targetDate, setTargetDate] = useState<number>();

  const [countdown] = useCountDown({
    targetDate,
    // onEnd: () => {
    //   message.warning(`验证码已过期`)
    // },
  });
  const goGet = () => {
    //请求接口发验证码
    getLoginCode().then((res:any)=>{
      if(res.code===0){
        message.success(`已发送验证码到${res.data.mobile}`)
        setTargetDate(Date.now() + 60000);
      }else{
        message.warning(`验证码发送失败，请稍后重试`)
      }
    }).catch((err:any)=>{
      message.warning(`验证码发送失败，请稍后重试`)
    })
    
  }
  return (
    <div
      onClick={goGet}
      className="getcode"
      style={{pointerEvents: countdown === 0 ?'all':'none'}}
    >
      {countdown === 0 ? '获取验证码' : `${Math.round(countdown / 1000)}s`}
    </div>
  );
}

export default ScreenShot;
