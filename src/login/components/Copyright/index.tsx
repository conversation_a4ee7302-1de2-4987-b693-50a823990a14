import google from '@src/assets/image/google.png';
import './index.less';

const Copyright = () => {
	const chromeUrl = "/micro-apps/micro-static-file/resource/file/Chrome.exe"
	return (
		
		<div className="copyright-box">
			{/* <p className='company'>江西省云眼大视界科技有限公司</p> */}
			<div className="row-infos">
				<p>为获得最佳使用体验，建议使用谷歌浏览器最新版，并在分辨率为1920x1080的显示器上显示</p>
				<div className="a-infos" style={{ marginLeft: '16px', cursor: 'pointer' }}>
					{/* {chromeUrl && (
						<>
							<div className="img-logo">
								<img src={google} alt='' />
							</div>
							<a className="down-load" target="_blank" href={chromeUrl} download="Chrome.exe" rel="noopener noreferrer">
								下载Chrome浏览器
							</a>
						</>
					)} */}
				</div>
			</div>
		</div>
	);
};

export default Copyright;