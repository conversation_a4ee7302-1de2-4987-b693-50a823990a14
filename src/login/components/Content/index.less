.login-content {
  width: 448px;
  height: max-content;
  background-color: #fff;
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px 40px;
  .login-content-title {
    color: #165dff;
    font-family: "PingFang SC";
    font-size: 36px;
    font-style: normal;
    font-weight: 600;
    line-height: 44px;
    margin-bottom: 53px;
  }
  input{
    background: #e8e8e8 !important;
  }
  .wrap {
    width: 366px;
    height: 40px;
    border-radius: 3px;
    overflow: hidden;
    background-color: #e8e8e8;
    margin: 8px auto 0px;
    text-align: center;
    line-height: 48px;
    /*border-radius: 7px;*/
    position: relative;
    border: 1px solid var(--bd);
    user-select: none;
  }
  .rect {
    position: relative;
    width: 365px;
    height: 100%;
    border-radius: 3px;
    overflow: hidden;
    color: rgba(0, 0, 0, 0.4);
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 400;
  }
  .rec {
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    z-index: 2;
    background: #2ba471;
  }
  .silde {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    z-index: 11;
    border-radius: 4px;
    /*在这里面，当设置长宽为40px时在加上边框1px就会超出 40px。
    可以使用怪异盒模型，怪异盒模型会使盒子的宽高包括边框，操持40px；*/
    box-sizing: border-box;
    width: 48px;
    height: 44px;
    background: #fff;
    box-shadow: var(--shadow2);
    overflow: hidden;
    img {
      width: 50%;
      height: 50%;
      position: relative;
      top: 5px;
      pointer-events: none;
      left: 1px;
    }
  }
  .getcode {
    color: #248ffa;
    line-height: 30px;
  }
  .forget {
    // color: rgba(0, 0, 0, 0.6);
    color: #165DFF;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }
  .form-btn-tools {
    padding-top: 32px;
    button {
      width: 100%;
      height: 50px;
      border: none;
      background-color: #165dff;
      color: #fff;
      border-radius: 4px;
      font-weight: 500;
      font-size: 18px;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 50px;
      color: #fff;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
    }
  }
  .ant-form-item {
    margin-bottom: 32px;
    color: #333;
    label {
      color: #333;
    }
  }
  .ant-input {
    border-radius: 3px;
  }

  .ant-input-affix-wrapper {
    border-radius: 3px;
    background-color: #eee;
    // border-color: transparent;
    // border-color: #ddd;
    padding: 0;
    padding-right: 13px;
    box-sizing: border-box;
    .ant-input {
      background-color: transparent;
      color: #333;
      &::placeholder {
        color: #999;
      }
    }
  }
  .ant-input-prefix {
    color: #999;
    .anticon {
      width: 24px;
      position: relative;
      top: 2px;
      svg {
        width: 100%;
      }
    }
  }
  .ant-input-suffix {
    img {
      height: 32px;
      cursor: pointer;
    }
  }
  .ant-form-item-control-input {
    min-height: 40px;
    * {
      // height: 100%;
      height: 46px;
    }
    input {
      // border: 1px solid #d9d9d9;
      padding-left: 10px !important;
    }
  }
}
