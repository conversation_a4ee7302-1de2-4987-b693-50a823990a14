import { useContext, useRef, useState } from 'react';
import { Input, Form, message } from 'antd';
import { AppContext } from '@cloud-app-dev/vidc';
import { useNavigate } from 'react-router-dom';
import { cache } from '@cloud-app-dev/vidc';
import { IconFont } from '@cloud-app-dev/vidc';
import { login } from '@src/service/login';
import LoadButton from './LoadButton';

import passimg from '@src/assets/image/pass.png';
import goPass from '@src/assets/image/gopass.png';
import './index.less';
function Content() {
  const [form] = Form.useForm();
  const loginName = Form.useWatch('loginName', form);
  const userPassword = Form.useWatch('userPassword', form);
  const navigate = useNavigate();
  const { updateLoginStatus } = useContext(AppContext.Context as any) as any;
  //登录
  const [pass, setPass] = useState(false);
  const [loading, setLoading] = useState(false);
  //获取事件
  const silde = useRef<any>();
  const rec = useRef<any>();
  const rect = useRef<any>();
  const img = useRef<any>();
  var minusX, initX: any; //保存变化的 X坐标（全局变量）
  //注册事件
  const onmousedown = function (e: any) {
    //鼠标点击事件，点击之后执行函数，获得点击位置的X坐标
    if (!pass) {
      initX = e.clientX || e.changedTouches[0].clientX; //保存初始按下位置的 X坐标；
      document.onmousemove = onmousemove;
      document.onmouseup = onmouseup;
      document.ontouchmove = onmousemove;
      document.ontouchend = onmouseup;
    }
  };
  const onmousemove = function (e: any) {
    //鼠标移动事件
    var moveX = e.clientX || e.changedTouches[0].clientX;
    // var minusX = moveX - initX;    //变化的坐标（要注意作用域的问题，在这里面定义变量，在这个函数之外的函数就没法使用，所以要将minusX变成全局变量）
    minusX = moveX - initX;
    //这里注意一下，获得的minusX只是一个差值，没有单位想让 滑块的位置改变还需要加上 单位px
    //这个时候滑块会跟随鼠标整个页面一行的跑，价格条件判段，限制 滑块移动的区域不可以超过边框，保持left<=0。
    if (minusX < 0) {
      minusX = 0;
    }
    if (minusX > 316) {
      //判断最大值
      minusX = 316;
    }
    silde.current.style.left = minusX + 'px';
    rec.current.style.width = minusX + 20 + 'px';
    if (minusX >= 316) {
      setPass(true);
      document.onmousemove = null;
      document.onmouseup = null;
      document.ontouchmove = null;
      document.ontouchend = null;
      silde.current.onTouchStart = null;
      silde.current.onmousedown = null;
    }
  };
  const onmouseup = function () {
    //鼠标抬起事件
    document.onmouseup = null;
    document.onmousemove = null; //不允许鼠标移动事件发生
    document.ontouchmove = null;
    document.ontouchend = null;
    silde.current.onTouchStart = null;
    silde.current.onMouseDown = null;
    if (!pass) {
      silde.current.style.left = '0px';
      rec.current.style.width = '0px';
    }
  };
  const onFinish = function (): any {
    if (pass) {
      setLoading(true);
      return form
        .validateFields()
        .then(login)
        .then((res: any) => {
          if (res.code === 0) {
            message.success('登录成功');
            cache.setCache('token', res.data.access_token, 'session');
            cache.setCache('userId', res.data.additionalInformation.data.userId, 'session');
            cache.setCache('userInfo', res.data.additionalInformation.data, 'session');
            updateLoginStatus(true);
            navigate('/');
            return res;
          } else {
            message.warning(res.message);
            Promise.reject(res);
          }
          setLoading(false);
        })
        .catch((error) => {
          if(error?.errorFields){
           return error?.errorFields.forEach((item:any)=>{
              message.warning(item.errors[0])
            })
          }
          console.error(error);
          message.warning(error?.data?.message ?? error.message);
          setLoading(false);
        });
    } else {
      message.warning('验证未通过');
    }
  };

  return (
    <div className="login-content">
      <div className="login-content-title">外部视频监督系统</div>
      <Form form={form}>
        <Form.Item name="loginName"  labelCol={{ span: 24 }} rules={[{ required: true, message: '用户名不能为空' }]}>
          <Input variant="filled"  style={{height:48,background:'#e8e8e8'}} placeholder="请输入您的账号" />
        </Form.Item>
        <Form.Item name="userPassword"  labelCol={{ span: 24 }} rules={[{ required: true, message: '密码不能为空' }]}>
          <Input.Password variant="filled"  style={{height:48,background:'#e8e8e8'}} placeholder="请输入您的密码" />
        </Form.Item>
        <Form.Item name="identifyCode" labelCol={{ span: 24 }} wrapperCol={{span:24}}> 
          <div className="wrap">
            <div className="rec" ref={rec}>
              <div className="rect" ref={rect}>
                {pass ? <span style={{ color: '#fff' }}>验证通过</span> : '请按住滑块，拖动到最右边'}
                <div ref={silde} className="silde" onTouchStart={onmousedown} onMouseDown={onmousedown}>
                  {pass ? <img ref={img} src={passimg} alt="" /> : <img ref={img} src={goPass} alt="" />}
                </div>
              </div>
            </div>
          </div>
        </Form.Item>
        <Form.Item className="form-btn-tools" colon={false} labelCol={{ span: 24 }}>
          <LoadButton style={loginName && userPassword && pass ? {} :{ background: '#e8e8e8', color: '#999'}} disabled={loginName && userPassword && pass ? false : true} type="primary" htmlType="submit" loading={loading} onClick={onFinish}>
            登录
          </LoadButton>
        </Form.Item>
      </Form>
      <span className="forget" onClick={()=>{
        message.warning('请联系管理员重置密码')
      }}>忘记密码?</span>
    </div>
  );
}

export default Content;
