import { Button } from 'antd';
import React, { useCallback, useState } from 'react';
import { ButtonProps } from 'antd/lib/button/button';

interface ILoadButton extends Exclude<ButtonProps, ['onClick', 'MouseEvent']> {
  withLoading?: boolean;
  onClick: (e: React.MouseEvent) => Promise<any>;
}

function LoadButton({ withLoading, onClick, children, ...props }: ILoadButton) {
  const [state, setState] = useState({ loading: false });
  const clickMerge = useCallback(
    (e: React.MouseEvent) =>
      Promise.resolve()
        .then(() => setState({ loading: true }))
        .then(() => onClick(e))
        .finally(() => setState({ loading: false })),
    [onClick]
  );
  return (
    <Button {...props} loading={state.loading} onClick={clickMerge}>
      {children}
    </Button>
  );
}

export default LoadButton;
