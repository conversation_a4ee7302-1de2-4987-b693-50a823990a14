import { Service } from "@cloud-app-dev/vidc";
import { Modal } from "antd";

export function injectServiceMiddleware() {
  const BodyElement = document.body;
  Service.registerResponseErrorMiddleware((error: any) => {
    if (!error.response) {
      return error;
    }
    if (error.response.status === 401) {
      Modal.warning({
        title: "提示",
        content: "登录状态失效，请重新登录！",
        okText: "重新登录",
        getContainer: () => BodyElement,
        onOk: () => {
          sessionStorage.clear();
          window.location.href = "/login";
        },
      });
      return { message: "登录状态失效，请重新登录！", code: 401 };
    }
    if (error.response.status === 504) {
      return { message: "网络异常，请稍后重试！", code: 504 };
    }
    return error.response;
  });

  Service.registerResponseMiddleware((config) => {
    // if (config.data?.code === 403) {
    //   return Promise.reject(config);
    // }
    // return config.data;
    if(config.data?.code === 0){
      return config.data;
    }
   return Promise.reject(config)
  });
}
