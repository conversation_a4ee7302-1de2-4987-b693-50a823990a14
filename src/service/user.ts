import { Service , cache } from '@cloud-app-dev/vidc';
import { HttpResult, queryUsersParams, queryUsersType, queryAlgorithmListType } from './interface.d';
import URL from './url';

const defaultHeaders = {
  Authorization: cache.getCache('token', 'session'),
};

class UserService {
  /**
   *
   * @param options
   * @returns
   */
   updateUserStatus = (options: any): Promise<HttpResult<any>> => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.updateUserStatus.value,
      method: 'post',
      data: options,
      requestId: URL.updateUserStatus.requestId,
    });
  };
  queryUsers = (options: any): Promise<HttpResult<queryUsersType>> => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.queryUsers.value,
      method: 'post',
      data: options,
      requestId: URL.queryUsers.requestId,
    });
  };
  uploadImg = (data: any) => {
    return Service.httpMultiPartInstance({
      method: 'post',
      headers: defaultHeaders,
      url: URL.uploadImg.value,
      data,
    });
  };
  resetPassword = (id: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.resetPassword.value.replace('<id>', id),
      method: 'put',
      data: { id },
      requestId: URL.resetPassword.requestId,
    });
  };
  addUser = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.addUser.value,
      method: 'post',
      data: options,
      requestId: URL.addUser.requestId,
    });
  };
  changeUser = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.changeUser.value,
      method: 'post',
      data: options,
      requestId: URL.changeUser.requestId,
    });
  };
  deleteUser = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.deleteUser.value.replace('<id>', options.id),
      method: 'delete',
      data: options,
      requestId: URL.deleteUser.requestId,
    });
  };
  queryUserInfo = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.queryUserInfo.value.replace('<id>', options.id),
      method: 'post',
      data: options,
      requestId: URL.queryUserInfo.requestId,
    });
  };

  changeUserStatus = (accountId: any, isLocked: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.changeStatus.value,
      method: 'post',
      data: {
        accountId: accountId,
        isLocked: isLocked,
      },
      requestId: URL.changeStatus.requestId,
    });
  };

  queryOrganizations = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.queryOrganizations.value,
      method: 'post',
      data: options,
      requestId: URL.queryOrganizations.requestId,
    });
  };
  queryChildOrganizationsById = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.queryChildOrganizationsById.value,
      method: 'post',
      data: { selectChildOrganization: true, ...options },
      requestId: URL.queryChildOrganizationsById.requestId,
    });
  };
  addOrganization = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.addOrganization.value,
      method: 'post',
      data: options,
      requestId: URL.addOrganization.requestId,
    });
  };
  deleteOrganization = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.deleteOrganization.value,
      method: 'delete',
      data: options,
      requestId: URL.deleteOrganization.requestId,
    });
  };
  updateOrganization = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.updateOrganization.value,
      method: 'post',
      data: options,
      requestId: URL.updateOrganization.requestId,
    });
  };
  distributeDeviceGroups = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.distributeDeviceGroups.value,
      method: 'post',
      data: options,
      requestId: URL.distributeDeviceGroups.requestId,
    });
  };
  getUserDevicesNew = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.getUserDevicesNew.value +options.userId,
      method: 'get',
      requestId: URL.getUserDevicesNew.requestId,
    });
  };
  distributeAlgorithm = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.distributeAlgorithm.value,
      method: 'post',
      data: options,
      requestId: URL.distributeAlgorithm.requestId,
    });
  };
  noCachequeryAlgorithmList = (data: any): Promise<queryAlgorithmListType[]> =>
    Service.http({
      url: URL.queryAlgorithmList.value,
      data,
      method: 'post',
      headers: defaultHeaders,
      requestId: URL.queryAlgorithmList.requestId,
    }).then((result) => {
      return result.code === 0 ? result?.data?.list ?? [] : [];
    });
    getUserAlgorithm = (userId: number) => {
      return Service.http({
        headers: defaultHeaders,
        url: `${URL.getUserAlgorithm.value}/${userId}`,
        method: 'get',
        requestId: URL.getUserAlgorithm.requestId,
      });
    };
}
export default new UserService();
