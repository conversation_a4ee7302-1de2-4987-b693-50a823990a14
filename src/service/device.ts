import { Service,cache } from '@cloud-app-dev/vidc';
import { HttpResult, DeviceGroupType } from './interface';
import URL from './url';
import { OrderItem } from '@src/system/device/MultifunctionTree/index.d';

const defaultHeaders = {
  Authorization: cache.getCache('token', 'session'),
  // RouterIp:'***************'
};

class deviceService {
  /**
   *
   * @param options
   * @returns
   */
  addDeviceGroup = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: URL.saveDeviceBuzGroup.value,
    method: 'post',
    data,
    requestId: URL.saveDeviceBuzGroup.requestId,
  });
  moveDeviceBuzGroup = (data:OrderItem[]): Promise<HttpResult<DeviceGroupType>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.moveDeviceBuzGroup.value}`,
    method: 'post',
    data,
    requestId: URL.moveDeviceBuzGroup.requestId,
  });
  updateDeviceGroup = (data:{id:string,parentId:string}): Promise<HttpResult<DeviceGroupType>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.updateDeviceBuzGroup.value}`,
    method: 'post',
    data,
    requestId: URL.updateDeviceBuzGroup.requestId,
  });
  deleteDeviceGroup = (data:any): Promise<HttpResult<object>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.deleteDeviceBuzGroup.value}`,
    method: 'delete',
    data,
    requestId: URL.deleteDeviceBuzGroup.requestId,
  });
  queryDeviceList = (code:string): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.queryDeviceList2.value}/${code}`,
    method: 'get',
    requestId: URL.queryDeviceList2.requestId,
  });
  queryDevice = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.queryDevice2.value}`,
    method: 'post',
    data,
    requestId: URL.queryDevice2.requestId,
  });
  dispenseDevice = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.dispenseDevice.value}`,
    method: 'post',
    data,
    requestId: URL.dispenseDevice.requestId,
  });
  queryTree = (): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.queryTree.value}`,
    method: 'get',
    requestId: URL.queryTree.requestId,
  });
  grant = (data:any,code:string): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.grant.value}/${code}`,
    method: 'put',
    data,
    requestId: URL.grant.requestId,
  });
  queryTreeNew = (): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.queryTreeNew.value}`,
    method: 'get',
    requestId: URL.queryTreeNew.requestId,
  });
  moveToGroup = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.moveToGroup.value}`,
    method: 'post',
    data,
    requestId: URL.moveToGroup.requestId,
  });
  deviceTagList = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.deviceTagList.value}`,
    method: 'post',
    data,
    requestId: URL.deviceTagList.requestId,
  });

  orgListTree = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.orgListTree.value}`,
    method: 'post',
    data,
    requestId: URL.orgListTree.requestId,
  });
  userOrgSave = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.userOrgSave.value}`,
    method: 'post',
    data,
    requestId: URL.userOrgSave.requestId,
  });
  userOrgUpdate = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.userOrgUpdate.value}`,
    method: 'post',
    data,
    requestId: URL.userOrgUpdate.requestId,
  });


  timeRuleList = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.timeRuleList.value}`,
    method: 'post',
    data,
    requestId: URL.timeRuleList.requestId,
  });
  timeSaveRule = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.timeSaveRule.value}`,
    method: 'post',
    data,
    requestId: URL.timeSaveRule.requestId,
  });
  timeUpdateRule = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.timeUpdateRule.value}`,
    method: 'post',
    data,
    requestId: URL.timeUpdateRule.requestId,
  });

  updateDeviceOrgId = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.updateDeviceOrgId.value}/${data.cid}/${data.orgId}`,
    method: 'get',
    requestId: URL.updateDeviceOrgId.requestId,
  });

  saveDeviceTag = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.saveDeviceTag.value}`,
    method: 'post',
    data,
    requestId: URL.saveDeviceTag.requestId,
  });
  updateDeviceTag = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.updateDeviceTag.value}`,
    method: 'post',
    data,
    requestId: URL.updateDeviceTag.requestId,
  });

  queryAlgorithmList = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.queryAlgorithmList.value}`,
    method: 'post',
    data,
    requestId: URL.queryAlgorithmList.requestId,
  });

  algorithmRuleInfo = (data:any): Promise<HttpResult<any>> => {
    let obj = {
      3100000000001: 'detainInSsolation',
      3100000000002: 'doctorsAppearFrequently',
      3100000000003: 'enterRoomRestTime',
      3100000000004: 'monitoringRoomLeftPost',
    }
    // @ts-ignore
    let val = obj[data.algorithmId] || data.algorithmId
    return Service.http({
      headers: defaultHeaders,
      url: `${URL.algorithmRuleInfo.value}/${val}`,
      method: 'get',
      requestId: URL.algorithmRuleInfo.requestId,
    })
  };

  serviceRegistryList = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.serviceRegistryList.value}`,
    method: 'post',
    data,
    requestId: URL.serviceRegistryList.requestId,
  });

  userOrgDelete = (data:any): Promise<HttpResult<any>> => Service.http({
    headers: defaultHeaders,
    url: `${URL.userOrgDelete.value}/${data.id}`,
    method: 'get',
    requestId: URL.userOrgDelete.requestId,
  });
}
export default new deviceService();
