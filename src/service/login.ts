import { Service , cache } from '@cloud-app-dev/vidc';
import URL from './url';
import RSA from './rsa';

const getHeader = () => ({ Authorization: cache.getCache('token', 'session') });
// let publicKey: string;
export const login = async (data: any) => {
  // if (!publicKey) {
  //   publicKey = await getPublicKey();
  // }
  return getPublicKey()
    .then((publicKey) => {
      const params = {
        authUserLogin: {
          code: data.identifyCode,
          loginType: 1,
          password: RSA.encrypt(data.userPassword, publicKey),
          productType: "3",
          username: data.loginName,
          sign: RSA.encrypt(Date.now() + data.userPassword, publicKey),
        },
        systemId: 100100000002
      }
      return params;
    })
    .then((data) => {
      return Service.http({
        url: URL.login.value,
        method: 'post',
        data,
        requestId: URL.login.requestId,
      });
    });
};

export const getPublicKey = async () => {
  const res = await Service.http({
    url: URL.getPublicKey.value,
    requestId: URL.login.requestId,
  });
  return res.data as string;
};
export const getLoginCode = () => {
  return Service.http({
    headers: getHeader(),
    url: URL.getLoginCode.value,
    method: 'post',
    requestId: URL.getLoginCode.requestId
  });
};
