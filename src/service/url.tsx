const url = {
  queryOperationCenterMenuAndPrivileges: {
    value: `/api/user/operationCenter/v1/queryOperationCenterMenuAndPrivileges/<id>`,
    label: "查询运营中心资源权限",
    requestId: "queryOperationCenterMenuAndPrivileges",
  },
  queryRoles: {
    value: `/api/amc-user-server/role/list/query`,
    label: "角色列表",
    requestId: "queryRoles",
  },
  roleDetail: {
    value: `/api/user/role/v3/info/<id>`,
    label: "角色详情",
    requestId: "roleDetail",
  },
  addRole: {
    value: `/api/amc-user-server/role/save`,
    label: "新增角色",
    requestId: "addRole",
  },
  changeRole: {
    value: `/api/amc-user-server/role/update`,
    label: "编辑角色",
    requestId: "changeRole",
  },
  deleteRole: {
    value: `/api/amc-user-server/role/delete`,
    label: "移除角色",
    requestId: "deleteRole",
  },
  getUserMsgList: {
    value: `/api/cms/v1/getUserMsgList`,
    label: "我的消息",
    requestId: "getUserMsgList",
  },
  liveFlvAddress: {
    value: `/api/staticResource/v2/video/live.flv`,
    label: "获取FLV实时视频地址",
    requestId: "liveFlvAddress",
    logInfo: [
      {
        code: 103901,
        parent: 103900,
        text: "实时视频",
      },
    ],
  },
  updateUserStatus: {
    value: `/api/amc-user-server/userManage/userIsLocked`,
    label: "修改用户状态",
    requestId: "updateUserStatus",
  },
  queryUsers: {
    value: `/api/amc-user-server/userManage/list/query`,
    label: "用户列表",
    requestId: "queryUsers",
  },
  queryUserInfo: {
    value: `/api/user/v3/users/<id>`,
    label: "用户信息",
    requestId: "queryUserInfo",
  },
  uploadImg: {
    value: `/api/user/v3/img/uploadImg`,
    requestId: "userUploadImg",
    label: "用户模块图片上传",
  },
  resetPassword: {
    value: `/api/amc-user-server/userManage/resetPassword/<id>`,
    label: "密码重置",
    requestId: "resetPassword",
  },
  addUser: {
    value: `/api/amc-user-server/userManage/createUser`,
    label: "新增用户",
    requestId: "addUser",
  },
  deleteUser: {
    value: `/api/amc-user-server/userManage/deleteUser/<id>`,
    label: "删除用户",
    requestId: "deleteUser",
  },
  changeUser: {
    value: `/api/amc-user-server/userManage/updateUser`,
    label: "编辑用户",
    requestId: "changeUser",
  },
  changeStatus: {
    value: `/api/user/v3/changeStatus`,
    label: "停用/启用用户",
    requestId: "changeUserStatus",
  },
  queryOrganizations: {
    value: `/api/org/v3/queryList`,
    requestId: "queryOrganizations",
    label: "查询子组织列表（组织管理用到）",
  },
  queryChildOrganizationsById: {
    value: `/api/user/v3/organization/queryChildOrganizationsById`,
    requestId: "queryChildOrganizationsById",
    label: "按条件查询子组织列表(当前运营中心下)(获取当前用户下的所有组织，全局用到)",
  },
  deleteOrganization: {
    value: `/api/org/v3/delete`,
    requestId: "deleteOrganization",
    label: "删除组织",
  },
  updateOrganization: {
    value: `/api/org/v3/update`,
    requestId: "updateOrganization",
    label: "编辑组织",
  },
  addOrganization: {
    value: `/api/org/v3/save`,
    requestId: "addOrganization",
    label: "新增组织",
  },
  ptzControlAll: {
    value: `/api/baseDevice/baseCloud/v1/ptzControlAll`,
    label: "ptz控制",
    requestId: "ptzControlAll",
  },
  focusControl: {
    value: `/api/baseDevice/baseCloud/v1/focusControl`,
    label: "ptz聚焦",
    requestId: "focusControl",
  },
  queryDeviceGroup: {
    value: `/api/device/group/v2`,
    label: "操作设备分组目录",
    requestId: "queryDeviceGroup",
  },
  queryDevice: {
    value: `/api/device/v2/group/video`,
    label: "请求设备",
    requestId: "queryDevice",
  },
  queryDeviceCount: {
    value: `/api/device/v2/group/video/count`,
    label: "请求设备数量",
    requestId: "queryDeviceCount",
  },
  queryMapping: {
    value: `/api/device/user/mapping/tree`,
    label: "获取设备树",
    requestId: "queryMapping",
  },
  queryLocalVideo: {
    value: `/api/staticResource/v2/queryLocalVideo`,
    label: "获取前端录像",
    requestId: "queryLocalVideo",
  },
  queryHistoryAddress: {
    value: `/api/staticResource/v2/video/queryHistoryAddress`,
    label: "获取云录像",
    requestId: "queryHistoryAddress",
  },
  queryMapDevice: {
    value: `/api/device/v2/deviceGather`,
    label: "地图获取设备",
    requestId: "queryMapDevice",
  },
  queryOriginalPiclist: {
    value: `/api/alarm/v3/originalPic/list`,
    label: "原图列表",
    requestId: "queryOriginalPiclist",
  },
  //ais新接口
  queryAlgorithmType: {
    value: `/api/dvia-app-scene-server/alarm/aisAlgorithm/queryAlgorithmType`,
    label: "算法分类列表",
    requestId: "queryAlgorithmType",
  },
  queryAlgorithmList: {
    value: `/api/dvia-app-scene-server/alarm/aisAlgorithm/queryAlgorithmList`,
    label: "算法列表",
    requestId: "queryAlgorithmList",
  },
  hideMergeAlarmInfo: {
    value: `/api/dvia-app-scene-server/alarm/aisMergeAlarmInfo/hideMergeAlarmInfo`,
    label: "隐藏告警",
    requestId: "hideMergeAlarmInfo",
  },
  processMergeAlarmInfo: {
    value: `/api/dvia-app-scene-server/alarm/aisMergeAlarmInfo/processMergeAlarmInfo`,
    label: "告警处置",
    requestId: "processMergeAlarmInfo",
  },
  queryMergeAlarmInfoList: {
    value: `/api/dvia-app-scene-server/alarm/aisMergeAlarmInfo/queryMergeAlarmInfoList`,
    label: "告警列表",
    requestId: "queryMergeAlarmInfoList",
  },
  queryMergeAlarmInfo: {
    value: `/api/dvia-app-scene-server/alarm/aisMergeAlarmInfo/queryMergeAlarmInfo`,
    label: "告警详情",
    requestId: "queryMergeAlarmInfo",
  },
  alarmTaskInfo: {
    value: `/api/dvia-app-scene-server/alarm/aisAlarmTask/alarmTaskInfo`,
    label: "根据设备cid和算法id查询单个设备单算法配置详情",
    requestId: "alarmTaskInfo",
  },
  deleteAlarmTask: {
    value: `/api/dvia-app-scene-server/alarm/aisAlarmTask/deleteAlarmTask`,
    label: "删除AIS告警任务表",
    requestId: "deleteAlarmTask",
  },
  queryAisAlarmTaskList: {
    value: `/api/dvia-app-scene-server/alarm/aisAlarmTask/queryAisAlarmTaskList`,
    label: "条件查询AIS告警任务表，分页",
    requestId: "queryAisAlarmTaskList",
  },
  saveAlarmTask: {
    value: `/api/dvia-app-scene-server/alarm/aisAlarmTask/saveAlarmTask`,
    label: "保存AIS告警任务表",
    requestId: "saveAlarmTask",
  },
  updateAlarmTask: {
    value: `/api/dvia-app-scene-server/alarm/aisAlarmTask/updateAlarmTask`,
    label: "修改AIS告警任务表",
    requestId: "updateAlarmTask",
  },
  updateAlarmTaskState: {
    value: `/api/dvia-app-scene-server/alarm/aisAlarmTask/updateAlarmTaskState`,
    label: "单个-批量暂停、启动任务",
    requestId: "updateAlarmTaskState",
  },
  //轮巡
  queryPollingTask: {
    value: `/api/dvia-app-scene-server/alarm/aisPollingTask/queryPollingTask`,
    label: "轮巡任务详情",
    requestId: "queryPollingTask",
  },
  savePollingTask: {
    value: `/api/dvia-app-scene-server/alarm/aisPollingTask/savePollingTask`,
    label: "保存轮巡任务",
    requestId: "savePollingTask",
  },
  triggerPollingTask: {
    value: `/api/dvia-app-scene-server/alarm/aisPollingTask/triggerPollingTask`,
    label: "手动触发轮巡任务",
    requestId: "triggerPollingTask",
  },
  updatePollingTask: {
    value: `/api/dvia-app-scene-server/alarm/aisPollingTask/updatePollingTask`,
    label: "修改轮巡任务",
    requestId: "updatePollingTask",
  },
  updatePollingTaskState: {
    value: `/api/dvia-app-scene-server/alarm/aisPollingTask/updatePollingTaskState`,
    label: "启用停用轮巡任务",
    requestId: "updatePollingTaskState",
  },
  queryPollingAlgorithmInfoList: {
    value: `/api/dvia-app-scene-server/alarm/aisPollingExecution/queryPollingAlgorithmInfoList`,
    label: "查询轮巡记录算法详情表",
    requestId: "queryPollingAlgorithmInfoList",
  },
  queryPollingDeviceInfoList: {
    value: `/api/dvia-app-scene-server/alarm/aisPollingExecution/queryPollingDeviceInfoList`,
    label: "查询轮巡记录设备详情表",
    requestId: "queryPollingDeviceInfoList",
  },
  queryPollingExecutionList: {
    value: `/api/dvia-app-scene-server/alarm/aisPollingExecution/queryPollingExecutionList`,
    label: "查询轮巡记录执行记录表",
    requestId: "queryPollingExecutionList",
  },
  //设备
  queryDeviceList: {
    value: `/api/dvia-app-scene-server/device/systemDevice/deviceList/query`,
    label: "设备查询",
    requestId: "queryDeviceList",
  },
  coverImage: {
    value: `/api/dvia-app-scene-server/alarm/aisAlgorithm/coverImage`,
    label: "设备封面查询",
    requestId: "coverImage",
  },
  queryDevice2: {
    value: `/api/dvia-app-scene-server/device/systemDevice/deviceList/query`,
    label: "请求设备",
    requestId: "queryDevice2",
  },
  saveDeviceBuzGroup: {
    value: `/api/dvia-app-scene-server/device/deviceBuzGroup/saveDeviceBuzGroup`,
    label: "保存",
    requestId: "saveDeviceBuzGroup",
  },
  moveDeviceBuzGroup: {
    value: `/api/dvia-app-scene-server/device/deviceBuzGroup/moveDeviceBuzGroup`,
    label: "移动",
    requestId: "moveDeviceBuzGroup",
  },
  updateDeviceBuzGroup: {
    value: `/api/dvia-app-scene-server/device/deviceBuzGroup/updateDeviceBuzGroup`,
    label: "编辑",
    requestId: "updateDeviceBuzGroup",
  },
  deleteDeviceBuzGroup: {
    value: `/api/dvia-app-scene-server/device/deviceBuzGroup/deleteDeviceBuzGroup`,
    label: "操作设备分组目录",
    requestId: "deleteDeviceBuzGroup",
  },
  queryDeviceList2: {
    value: `/api/dvia-app-scene-server/device/device/group/v2/list`,
    label: "获取用户分组列表",
    requestId: "queryDeviceGroup",
  },
  dispenseDevice: {
    value: `/api/dvia-app-scene-server/device/systemDevice/dispenseDevice`,
    label: "分配设备到分组",
    requestId: "dispenseDevice",
  },
  queryTree: {
    value: `/api/dvia-app-scene-server/device/deviceBuzGroup/deviceBuzGroupList/query`,
    label: "获取设备分组树",
    requestId: "queryTree",
  },
  grant: {
    value: `/api/dvia-app-scene-server/device/device/group/v2/grant`,
    label: "分配",
    requestId: "grant",
  },
  login: {
    value: `/api/amc-user-server/authorization/login`,
    label: "用户登录",
    requestId: "login",
  },
  getPublicKey: {
    value: `/api/amc-user-server/authorization/getPulicKey`,
    label: "获取登陆key",
    requestId: "getPublicKey",
  },
  userInfo: {
    value: `/api/amc-user-server/authorization/v3/users/<userId>`,
    label: "用户信息",
    requestId: "userinfo",
  },
  getLoginCode: {
    value: `/api/amc-user-server/authorization/v3/users/<userId>`,
    label: "获取验证码",
    requestId: "userinfo",
  },
  distributeDeviceGroups: {
    value: `/api/dvia-app-scene-server/device/systemDevice/distributeDeviceGroups`,
    requestId: "distributeDeviceGroups",
    label: "用户分配设备分组",
  },
  getUserDevicesNew: {
    value: `/api/dvia-app-scene-server/device/systemDevice/getUserDevices/`,
    requestId: "getUserDevicesNew",
    label: "获取用户已分配的设备",
  },
  distributeAlgorithm: {
    value: `/api/dvia-app-scene-server/alarm/aisAlgorithm/distributeAlgorithm`,
    requestId: "distributeAlgorithm",
    label: "用户分算法",
  },
  getUserAlgorithm: {
    value: `/api/dvia-app-scene-server/alarm/aisAlgorithm/getUserAlgorithm`,
    requestId: "getUserAlgorithm",
    label: "用户查算法",
  },
  queryTreeNew: {
    value: `/api/dvia-app-scene-server/device/deviceOriginGroup/list/query`,
    label: "原始目录获取设备分组树",
    requestId: "queryTreeNew",
  },
  moveToGroup: {
    value: `/api/dvia-app-scene-server/device/deviceBuzGroup/moveToGroup`,
    label: "将一个节点搬至另外一个节点之下",
    requestId: "moveToGroup",
  },
  devicePlatform: {
    value: `/api/dvia-ap-server/device/platform`,
    label: "2.1 添加接入平台",
    requestId: "devicePlatform",
  },
  platformPage: {
    value: `/api/dvia-ap-server/device/platform/page`,
    label: "2.5 分页查询接入平台信息",
    requestId: "platformPage",
  },
  platformEdit: {
    value: `/api/dvia-ap-server/device/platform/`,
    label: "2.2 修改入平台",
    requestId: "platformEdit",
  },
  platformDelect: {
    value: `/api/dvia-ap-server/device/platform/`,
    label: "2.4 删除入平台",
    requestId: "platformDelect",
  },
  apPlatformInfoQuery: {
    value: `/api/dvia-ap-server/apPlatformInfo/list/query`,
    label: "12.1 条件查询接入平台信息，分页",
    requestId: "apPlatformInfoQuery",
  },
  apPlatformInfoSave: {
    value: `/api/dvia-ap-server/apPlatformInfo/save`,
    label: "12.3 保存接入平台信息",
    requestId: "apPlatformInfoSave",
  },
  apPlatformInfoEnabled: {
    value: `/api/dvia-ap-server/apPlatformInfo/enabled`,
    label: "12.6 启用或关闭接入平台信息",
    requestId: "apPlatformInfoEnabled",
  },
  apPlatformInfoDelete: {
    value: `/api/dvia-ap-server/apPlatformInfo/delete/`,
    label: "12.5 删除接入平台信息",
    requestId: "apPlatformInfoDelete",
  },
  apPlatformInfoInfo: {
    value: `/api/dvia-ap-server/apPlatformInfo/info/`,
    label: "12.2 接入平台信息详情",
    requestId: "apPlatformInfoInfo",
  },
  apPlatformUpdate: {
    value: `/api/dvia-ap-server/apPlatformInfo/update`,
    label: "12.4 修改接入平台信息",
    requestId: "apPlatformUpdate",
  },
  userInfoAll: {
    value: `/api/dvia-app-scene-server/user/info/`,
    label: "9.3 获取用户信息",
    requestId: "userInfoAll",
  },
  updateUser: {
    value: `/api/dvia-app-scene-server/user/update`,
    label: "9.2 更新用户",
    requestId: "updateUser",
  },
  apPlatformInfoCardList: {
    value: `/api/dvia-ap-server/apPlatformInfo/cardList`,
    label: "12.7 卡片列表",
    requestId: "apPlatformInfoCardList",
  },
  apPlatformInfoTest: {
    value: `/api/dvia-ap-server/apPlatformInfo/test`,
    label: "12.8 测试",
    requestId: "apPlatformInfoTest",
  },
  userCreate: {
    value: `/api/dvia-app-scene-server/user/create`,
    label: "9.1 新增用户",
    requestId: "userCreate",
  },
  noconfigDeviceDataQuery:{
    value: `/api/dvia-app-scene-server/noconfigDeviceData/list/query`,
    label: "条件查询未配置算法的设备数据，分页",
    requestId: "noconfigDeviceDataQuery",
  },
  systemDeviceInfo:{
    value: `/api/dvia-app-scene-server/device/systemDevice/info/`,
    label: "根据cid查询设备",
    requestId: "systemDeviceInfo",
  },
  noconfigDeviceDataInfo:{
    value: `/api/dvia-app-scene-server/noconfigDeviceData/info/`,
    label: "获取未配置详情数据，分页",
    requestId: "noconfigDeviceDataInfo",
  },
  batchSaveAlarmTask:{
    value: `/api/dvia-app-scene-server/alarm/aisAlarmTask/batchSaveAlarmTask`,
    label: "批量创建任务",
    requestId: "batchSaveAlarmTask",
  },
  getTimeDimensionStatistics:{
    value: `/api/dvia-app-scene-server/alarm/aisMergeAlarmInfo/getTimeDimensionStatistics`,
    label: "获取时间维度和告警数量统计",
    requestId: "getTimeDimensionStatistics",
  },
  hideMergeAlarmInfoBatch:{
    value: `/api/dvia-app-scene-server/alarm/aisMergeAlarmInfo/hideMergeAlarmInfoBatch`,
    label: "批量隐藏告警",
    requestId: "hideMergeAlarmInfoBatch",
  },
  distributeDevicesCopy:{
    value: `/api/dvia-app-scene-server/device/systemDevice/copy/distributeDevices`,
    label: "复制用户设备权限",
    requestId: "distributeDevicesCopy",
  },
  deviceTagList:{
    value: `/api/dvia-app-scene-server/device/tag/deviceTagList`,
    label: "设备标签列表",
    requestId: "deviceTagList",
  },

  orgListTree:{
    value: `/api/amc-user-server/userOrg/orgListTree`,
    label: "局点树形结构",
    requestId: "orgListTree",
  },
  userOrgSave:{
    value: `/api/amc-user-server/userOrg/save`,
    label: "新增局点",
    requestId: "userOrgSave",
  },
  userOrgUpdate:{
    value: `/api/amc-user-server/userOrg/update`,
    label: "编辑局点",
    requestId: "userOrgUpdate",
  },
  tagDeviceTree:{
    value: `/api/dvia-app-scene-server/device/systemDevice/tagDeviceTree`,
    label: "标签设备树",
    requestId: "tagDeviceTree",
  },
  groupDeviceTree:{
    value: `/api/dvia-app-scene-server/device/systemDevice/groupDeviceTree`,
    label: "分组设备树",
    requestId: "groupDeviceTree",
  },


  timeRuleList:{
    value: `/api/dvia-app-scene-server/rule/time/ruleList`,
    label: "时间规则列表",
    requestId: "timeRuleList",
  },
  timeSaveRule:{
    value: `/api/dvia-app-scene-server/rule/time/saveRule`,
    label: "保存时间规则",
    requestId: "timeSaveRule",
  },
  timeUpdateRule:{
    value: `/api/dvia-app-scene-server/rule/time/updateRule`,
    label: "修改时间规则",
    requestId: "timeUpdateRule",
  },
  updateDeviceOrgId:{
    value: `/api/dvia-app-scene-server/device/systemDevice/updateDeviceOrgId`,
    label: "设备分配局点",
    requestId: "updateDeviceOrgId",
  },

  saveDeviceTag:{
    value: `/api/dvia-app-scene-server/device/tag/saveDeviceTag`,
    label: "保存设备标签",
    requestId: "saveDeviceTag",
  },
  updateDeviceTag:{
    value: `/api/dvia-app-scene-server/device/tag/updateDeviceTag`,
    label: "修改设备标签",
    requestId: "updateDeviceTag",
  },
  batchProcessMergeAlarmInfo:{
    value: `/api/dvia-app-scene-server/alarm/aisMergeAlarmInfo/batchProcessMergeAlarmInfo`,
    label: "批量(忽略)告警处置",
    requestId: "batchProcessMergeAlarmInfo",
  },

  algorithmRuleInfo:{
    value: `/api/dvia-app-scene-server/rule/algorithm/algorithmRuleInfo`,
    label: "算法规则详情",
    requestId: "algorithmRuleInfo",
  },

  serviceRegistryList:{
    value: `/api/amc-user-server/serviceRegistry/list`,
    label: "查询服务授权列表",
    requestId: "serviceRegistryList",
  },
  userOrgDelete:{
    value: `/api/amc-user-server/userOrg/delete`,
    label: "根据id删除用户组织（用户分组）",
    requestId: "userOrgDelete",
  },
  identityLibraryList:{
    value: `/api/dvia-app-scene-server/alarm/aisMergeAlarmInfo/identityLibraryList`,
    label: "身份识别库列表",
    requestId: "identityLibraryList",
  },
  identityLibraryInfo:{
    value: `/api/dvia-app-scene-server/alarm/aisMergeAlarmInfo/identityLibraryInfo/`,
    label: "身份识别库详情",
    requestId: "identityLibraryInfo",
  },
  getCategory:{
    value: `/api/amc-user-server/dictionary/item/`,
    label: "字典分类表详情",
    requestId: "getCategory",
  },
};
export default url;
