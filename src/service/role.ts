import { Service,cache } from '@cloud-app-dev/vidc';
import {  queryRolesParams } from './interface';
import URL from './url';

const defaultHeaders = {
  Authorization: cache.getCache('token', 'session'),
};

class RoleService {
  queryOperationCenterMenuAndPrivileges = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.queryOperationCenterMenuAndPrivileges.value.replace('<id>', options.id),
      method: 'post',
      data: options,
      requestId: URL.queryOperationCenterMenuAndPrivileges.requestId,
    });
  };
  /**
   *
   *
   * @param options
   * @returns
   */
  queryRoles = (options: queryRolesParams): Promise<any> => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.queryRoles.value,
      method: 'post',
      data: options,
      requestId: URL.queryRoles.requestId,
    }).then((result) => (result.code === 0 ? result?.data?.list ?? [] : []));
  }
  queryRolesTable = (options: queryRolesParams): Promise<any> => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.queryRoles.value,
      method: 'post',
      data: options,
      requestId: URL.queryRoles.requestId,
    });
  }
  roleDetail = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.roleDetail.value.replace('<id>', options.id),
      method: 'get',
      data: options,
      requestId: URL.roleDetail.requestId,
    });
  };
  addRole = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.addRole.value,
      method: 'post',
      data: options,
      requestId: URL.addRole.requestId,
    });
  };
  deleteRole = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.deleteRole.value,
      method: 'delete',
      data: options,
      requestId: URL.deleteRole.requestId,
    });
  };
  changeRole = (options: any) => {
    return Service.http({
      headers: defaultHeaders,
      url: URL.changeRole.value,
      method: 'post',
      data: options,
      requestId: URL.changeRole.requestId,
    });
  };
}
export default new RoleService();
