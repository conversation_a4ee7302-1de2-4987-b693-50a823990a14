export interface HttpResult<T> {
  code: number;
  codeRemark: string;
  data: T;
  message?: string;
}

export interface deviceType {
  cid: number;
  name: string;
  ptz: number;
  state: number;
}
interface controlType {
  operation?: string;
  direction?: string;
  speed?: number;
}
export interface ptzControlAllParams {
  cid: string;
  cmd: string;
  control: controlType;
}
export interface ListType {
  reUserName: string;
  contentId: string;
  auditType: string;
  type: number;
  userName: string;
  userId: string;
  content: string;
  adminIsread: 1;
  reUserId: string;
  createTime: string;
  isread: number;
  replyId: string;
  createUserType: string;
  id: string;
  reply: string;
  operationCenterId: string;
  createUserAvatar: string;
}
export interface getUserMsgListType {
  list: ListType[];
  total: string;
}

export interface PageParams {
  limit: number;
  offset: number;
  isPagination?: boolean;
}
export interface queryRolesParams extends PageParams {
  modules?: string[];
  functions?: string[];
  centerIds?: string[];
  userAgent?: string[];
  startTime?: number;
  endTime?: number;
  rangeTime?: string;
  keywords?: string;
}
export interface DataType {
  id: string;
  roleName: string;
  privilegeCodes: string[];
  functionIds: (string | number)[];
}
export interface RoleDataType {
  id: string;
  roleName: string;
  privilegeCodes: string[];
  functionIds: (string | number)[];
}
export interface queryRolesType {
  list: RoleDataType[];
  total: string;
  offset: string;
  limit: string;
}


export interface queryUsersParams extends PageParams {
  modules?: string[];
  functions?: string[];
  centerIds?: string[];
  userAgent?: string[];
  startTime?: number;
  endTime?: number;
  rangeTime?: string;
  keywords?: string;
}
export interface UserDataType {
  id: string;
  realName: string;
  loginName: string;
  userGrade: string;
  roleIds: string[];
  validStartTime: string;
  organizationId: string;
  systemLogo: string;
  operationCenterId: string;
  userType: string;
  validState: string;
  idCardNotNull: boolean;
  phoneNum: string;
}
export interface queryUsersType {
  list: UserDataType[];
  total?: string;
  totalCount?: number;
  offset: string;
  limit: string;
}

export interface deviceDirType {
  code: string;
  name: string;
  parentCode: string;
  parentName: string;
  userId: string;
}

export interface DeviceGroupType {
  children?: [];
  code?: string;
  devices?: deviceType[];
  name?: string;
  offLine?: number;
  onLine?: number;
  parentCode?: string;
  parentName?: string;
  userId?: number;
}
export interface queryAlgorithmListType {
  id: number;
  algorithmName: string;
  algorithmNoteName: string;
  algorithmManufacturer: string;
  algorithmType: number;
  algorithmVersion: string;
  icon: string;
}