import { Service, cache } from "@cloud-app-dev/vidc";

import { HttpResult, PageParams, getUserMsgListType, ptzControlAllParams } from "./interface.d";
import URL from "./url";
import { size } from "lodash-es";

const defaultHeaders = {
  Authorization: cache.getCache("token", "session"),
  // RouterIp:'***************'
};
export const getUserMsgList = (data: PageParams): Promise<HttpResult<getUserMsgListType>> =>
  Service.http({
    headers: defaultHeaders,
    url: URL.getUserMsgList.value,
    method: "post",
    data,
    requestId: URL.getUserMsgList.requestId,
  });
//实时预览拉流
export const liveFlvAddress = (cid: string): any =>
  Service.http({
    headers: defaultHeaders,
    url: `${URL.liveFlvAddress.value}/${cid}?Authorization=${cache.getCache("token", "session")}`,
    method: "get",
    requestId: URL.liveFlvAddress.requestId,
  });
export const ptzControlAll = (data: ptzControlAllParams): Promise<HttpResult<any>> =>
  Service.http({
    headers: defaultHeaders,
    url: URL.ptzControlAll.value,
    method: "post",
    data,
    requestId: URL.ptzControlAll.requestId,
  });

export const ptzControl = ({ cid, direction, speed }: { cid: string; direction: string; speed: number }): Promise<HttpResult<any>> => {
  const moveparams = { cid, cmd: "move", control: { direction, speed } };
  return Service.http({
    headers: defaultHeaders,
    url: "/api/dvia-device-server/baseDevice/baseCloud/v1/ptzControlAll",
    method: "post",
    data: moveparams,
    requestId: URL.ptzControlAll.requestId,
  }).then(() => ptzStopControl({ cid, direction, speed }));
};

export const ptzStopControl = ({ cid, direction, speed }: { cid: string; direction: string; speed: number }): Promise<HttpResult<any>> => {
  const stopparams = { cid, cmd: "stop", control: { direction, speed } };
  return Service.http({
    headers: defaultHeaders,
    url: "/api/dvia-device-server/baseDevice/baseCloud/v1/ptzControlAll",
    method: "post",
    data: stopparams,
    requestId: URL.ptzControlAll.requestId,
  });
};

export const focusControl = (data: { cid: string; speed: number; operation: string }): Promise<HttpResult<any>> => {
  const params = { cid: data.cid, cmd: "manual", control: { operation: data.operation, speed: data.speed } };

  return Service.http({
    headers: defaultHeaders,
    url: "/api/dvia-device-server/baseDevice/baseCloud/v1/focusControl",
    method: "post",
    data: params,
    requestId: URL.focusControl.requestId,
  }).then(() => focusStopControl(data));
};

export const focusStopControl = (data: { cid: string; speed: number; operation: string }): Promise<HttpResult<any>> => {
  const stopparams = { cid: data.cid, cmd: "stop", control: { operation: data.operation, speed: data.speed } };
  return Service.http({
    headers: defaultHeaders,
    url: "/api/dvia-device-server/baseDevice/baseCloud/v1/focusControl",
    method: "post",
    data: stopparams,
    requestId: URL.focusControl.requestId,
  });
};

export const queryMapping = (code: string): Promise<HttpResult<any>> =>
  Service.http({
    headers: defaultHeaders,
    url: `${URL.queryMapping.value}/${code}`,
    method: "get",
    requestId: URL.queryMapping.requestId,
  });

export const queryMappingNew = (code: string): Promise<{ [key: string]: any }> => {
  if (!code) {
    return Promise.resolve([]);
  }
  return Service.http({
    headers: defaultHeaders,
    url: `${URL.queryMapping.value}/${code}`,
    method: "get",
    requestId: URL.queryMapping.requestId,
  }).then((result) => (result.code === 0 ? result?.data ?? {} : {}));
};
export const queryDeviceGroup = (): Promise<HttpResult<any[]>> =>
  Service.http({
    headers: defaultHeaders,
    url: URL.queryDeviceGroup.value,
    method: "get",
    requestId: URL.queryDeviceGroup.requestId,
  });

export const queryDeviceGroupNew = (): Promise<any[]> =>
  Service.http({
    headers: defaultHeaders,
    url: URL.queryDeviceGroup.value,
    method: "get",
    requestId: URL.queryDeviceGroup.requestId,
  }).then((result) => {
    if (result.code === 0) {
      return result.data;
    }
    return [];
  });
export const queryDevice = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    headers: defaultHeaders,
    url: `/api/dvia-app-scene-server/device/systemDevice/deviceList/query`,
    method: "post",
    data,
    requestId: URL.queryDevice.requestId,
  });
const TransData = (data: any[][]) => {
  let arr: any[] = [];
  data?.forEach((v) => {
    v?.forEach((item) => {
      if (size(item?.list)) {
        arr = [...arr, ...(item?.list ?? [])];
      }
    });
  });
  const newArr = arr.map((v) => ({
    ...v,
    deviceName: v.name,
    deviceStatus: v.state,
    latitude: +v.lat,
    longitude: +v.lng,
  }));
  return newArr;
};
export const queryMapDevice = (params: any): Promise<any> =>
  Service.http({
    headers: defaultHeaders,
    url: `${URL.queryMapDevice.value}`,
    method: "get",
    params: params,
    requestId: URL.queryMapDevice.requestId,
  }).then((res) => {
    if (res.code === 0) {
      return TransData(res.data);
    } else {
      return [];
    }
  });
export const queryDeviceCount = (params: any): Promise<HttpResult<any>> =>
  Service.http({
    headers: defaultHeaders,
    url: `${URL.queryDeviceCount.value}`,
    method: "get",
    params: params,
    requestId: URL.queryDeviceCount.requestId,
  });

const cacheRecord = {} as any;
export const queryLocalVideo = (data: { cid: string; beginTime: number; endTime: number }): Promise<HttpResult<any>> => {
  const key = `${data.cid}-${data.beginTime}-${data.endTime}`;
  if (cacheRecord[key]) {
    return Promise.resolve(cacheRecord[key]);
  }
  return Service.http({
    url: "/api/dvia-device-server/baseDevice/video/v1/queryLocalVideo",
    data,
    method: "post",
    headers: defaultHeaders,
  }).then((res) => {
    cacheRecord[key] = res;
    return res;
  });
};
export const queryHistoryAddress = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: "/api/dvia-device-server/baseDevice/video/v1/historyVideo",
    params: defaultHeaders,
    data,
    method: "post",
    headers: defaultHeaders,
  });

export const queryOriginalPiclist = (options: any): Promise<any> => {
  return Service.http({
    headers: defaultHeaders,
    url: URL.queryOriginalPiclist.value,
    method: "post",
    data: options,
    requestId: URL.queryOriginalPiclist.requestId,
  });
};

//ais 新接口
interface queryAlgorithmTypeType {
  algorithmType: number;
  algorithmTypeName: string;
  algorithmTypeOrder: number;
}
export const queryAlgorithmType = (): Promise<queryAlgorithmTypeType[]> => {
  return Service.http({
    headers: defaultHeaders,
    url: URL.queryAlgorithmType.value,
    method: "get",
    requestId: URL.queryAlgorithmType.requestId,
  })
    .then((result) => (result.code === 0 ? result?.data ?? [] : []))
    .then((res) => {
      res.unshift({
        algorithmType: 0,
        algorithmTypeName: "全部",
        algorithmTypeOrder: 0,
      });
      return res;
    });
};

export interface queryAlgorithmListType {
  id: number;
  algorithmName: string;
  algorithmNoteName: string;
  algorithmManufacturer: string;
  algorithmType: number;
  algorithmVersion: string;
  icon: string;
}

let cacheList: queryAlgorithmListType[] = [];
export const queryAlgorithmList = (data: any): Promise<queryAlgorithmListType[]> => {
  if (cacheList?.length !== 0) {
    return Promise.resolve(cacheList);
  }
  return Service.http({
    url: URL.queryAlgorithmList.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.queryAlgorithmList.requestId,
  }).then((result) => {
    cacheList = result.code === 0 ? result?.data?.list ?? [] : [];
    return cacheList;
  });
};

export const noCachequeryAlgorithmList = (data: any): Promise<queryAlgorithmListType[]> =>
  Service.http({
    url: URL.queryAlgorithmList.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.queryAlgorithmList.requestId,
  }).then((result) => {
    return result.code === 0 ? result?.data?.list ?? [] : [];
  });

export const hideMergeAlarmInfo = (id: number, hideFlag: number): Promise<HttpResult<any>> => {
  return Service.http({
    headers: defaultHeaders,
    url: `${URL.hideMergeAlarmInfo.value}/${id}/${hideFlag}`,
    method: "get",
    requestId: URL.hideMergeAlarmInfo.requestId,
  });
};
interface processMergeAlarmInfoParams {
  accurateFlag: number;
  id: any;
  processDescribe: string;
  processUserId?: number;
  processUserName?: string;
}
export const processMergeAlarmInfo = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.processMergeAlarmInfo.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.processMergeAlarmInfo.requestId,
  });

export const queryMergeAlarmInfo = (id: number): Promise<HttpResult<any>> => {
  return Service.http({
    headers: defaultHeaders,
    url: `${URL.queryMergeAlarmInfo.value}/${id}`,
    method: "get",
    requestId: URL.queryMergeAlarmInfo.requestId,
  });
};
interface queryMergeAlarmInfoListParams {
  alarmEndTime?: number;
  alarmStartTime?: number;
  algorithmIds?: number[];
  cid?: number[];
  isPagination?: boolean;
  keyWords?: string;
  limit: number;
  offset: number;
  processFlags?: boolean;
  systemId?: number;
}
export const queryMergeAlarmInfoList = (data: queryMergeAlarmInfoListParams): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.queryMergeAlarmInfoList.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.queryMergeAlarmInfoList.requestId,
  });

export const alarmTaskInfoById = (id: number): Promise<any> => {
  return Service.http({
    headers: defaultHeaders,
    url: `${URL.alarmTaskInfo.value}/${id}`,
    method: "get",
    requestId: URL.alarmTaskInfo.requestId,
  }).then((result) => (result.code === 0 ? result?.data ?? {} : {}));
};
export const alarmTaskInfo = (cid: number, algorithmId: number): Promise<HttpResult<any>> => {
  return Service.http({
    headers: defaultHeaders,
    url: `${URL.alarmTaskInfo.value}/${cid}/${algorithmId}`,
    method: "get",
    requestId: URL.alarmTaskInfo.requestId,
  });
};

export const deleteAlarmTask = (id: number): Promise<HttpResult<any>> => {
  return Service.http({
    headers: defaultHeaders,
    url: `${URL.deleteAlarmTask.value}/${id}`,
    method: "delete",
    requestId: URL.deleteAlarmTask.requestId,
  });
};
interface queryAisAlarmTaskListParams {
  algorithmIds?: number[];
  cids?: (number | string)[];
  isPagination?: boolean;
  taskName?: string;
  taskState?: string[];
  limit: number;
  offset: number;
  processFlags?: boolean;
  systemId?: number;
}
export const queryAisAlarmTaskList = (data: queryAisAlarmTaskListParams): Promise<any> =>
  Service.http({
    url: URL.queryAisAlarmTaskList.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.queryAisAlarmTaskList.requestId,
  })
    .then((result) => (result.code === 0 ? result?.data ?? {} : {}))
    .then((res) => {
      return {
        list: res.list || [],
        total: res.totalCount ?? 0,
      };
    });

export const saveAlarmTask = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.saveAlarmTask.value,
    data,
    method: "put",
    headers: defaultHeaders,
    requestId: URL.saveAlarmTask.requestId,
  });
export const updateAlarmTask = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.updateAlarmTask.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.updateAlarmTask.requestId,
  });
interface updateAlarmTaskStateParams {
  ids: number[];
  taskState: string;
}
export const updateAlarmTaskState = (data: updateAlarmTaskStateParams): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.updateAlarmTaskState.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.updateAlarmTaskState.requestId,
  });

export const queryPollingTask = (): Promise<any> => {
  return Service.http({
    headers: defaultHeaders,
    url: URL.queryPollingTask.value,
    method: "get",
    requestId: URL.queryPollingTask.requestId,
  }).then((result) => (result.code === 0 ? result?.data ?? {} : {}));
};
export const savePollingTask = (data: updateAlarmTaskStateParams): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.savePollingTask.value,
    data,
    method: "put",
    headers: defaultHeaders,
    requestId: URL.savePollingTask.requestId,
  });

export const triggerPollingTask = (id: number): Promise<HttpResult<any>> => {
  return Service.http({
    headers: defaultHeaders,
    url: `${URL.triggerPollingTask.value}/${id}`,
    method: "get",
    requestId: URL.triggerPollingTask.requestId,
  });
};
export const updatePollingTask = (data: updateAlarmTaskStateParams): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.updatePollingTask.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.updatePollingTask.requestId,
  });
interface updatePollingTaskStateParams {
  id: number;
  taskState: number;
}
export const updatePollingTaskState = (params: updatePollingTaskStateParams): Promise<HttpResult<any>> => {
  return Service.http({
    headers: defaultHeaders,
    url: `${URL.updatePollingTaskState.value}/${params.id}`,
    params,
    method: "get",
    requestId: URL.updatePollingTaskState.requestId,
  });
};
export const coverImage = (cid: string): Promise<HttpResult<any>> => {
  return Service.http({
    headers: defaultHeaders,
    url: `${URL.coverImage.value}/${cid}`,
    method: "get",
    requestId: URL.coverImage.requestId,
  });
};
export const queryPollingAlgorithmInfoList = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.queryPollingAlgorithmInfoList.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.queryPollingAlgorithmInfoList.requestId,
  });

export const queryPollingDeviceInfoList = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.queryPollingDeviceInfoList.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.queryPollingDeviceInfoList.requestId,
  });
export const queryPollingExecutionList = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.queryPollingExecutionList.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.queryPollingExecutionList.requestId,
  });
export const queryDeviceList = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.queryDeviceList.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.queryDeviceList.requestId,
  });
export const devicePlatform = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.devicePlatform.value,
    data,
    method: "post",
    headers: defaultHeaders,
    requestId: URL.devicePlatform.requestId,
  });

export const platformPage = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.platformPage.value,
    params: data,
    method: "get",
    headers: defaultHeaders,
    requestId: URL.platformPage.requestId,
  });
export const platformEdit = (data: any, id: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.platformEdit.value + id,
    data: data,
    method: "put",
    headers: defaultHeaders,
    requestId: URL.platformEdit.requestId,
  });

export const platformDelect = (id: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.platformDelect.value + id,
    method: "DELETE",
    headers: defaultHeaders,
    requestId: URL.platformDelect.requestId,
  });

export const apPlatformInfoQuery = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.apPlatformInfoQuery.value,
    data,
    method: "POST",
    headers: defaultHeaders,
    requestId: URL.apPlatformInfoQuery.requestId,
  });

export const apPlatformInfoSave = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.apPlatformInfoSave.value,
    data,
    method: "POST",
    headers: defaultHeaders,
    requestId: URL.apPlatformInfoSave.requestId,
  });
export const apPlatformInfoEnabled = ({ id, enabled }: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.apPlatformInfoEnabled.value + `/${id}/${enabled}`,
    method: "PUT",
    headers: defaultHeaders,
    requestId: URL.apPlatformInfoEnabled.requestId,
  });

export const apPlatformInfoDelete = ({ id }: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.apPlatformInfoDelete.value + `${id}`,
    method: "DELETE",
    headers: defaultHeaders,
    requestId: URL.apPlatformInfoDelete.requestId,
  });

export const apPlatformInfoInfo = ({ id }: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.apPlatformInfoInfo.value + `${id}`,
    method: "GET",
    headers: defaultHeaders,
    requestId: URL.apPlatformInfoInfo.requestId,
  });

export const apPlatformUpdate = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.apPlatformUpdate.value,
    method: "POST",
    headers: defaultHeaders,
    data,
    requestId: URL.apPlatformUpdate.requestId,
  });

export const userInfoAll = (userId: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.userInfoAll.value + userId,
    method: "GET",
    headers: defaultHeaders,
    requestId: URL.userInfoAll.requestId,
  });

export const updateUser = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.updateUser.value,
    method: "PUT",
    headers: defaultHeaders,
    data,
    requestId: URL.updateUser.requestId,
  });

export const apPlatformInfoCardList = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.apPlatformInfoCardList.value,
    method: "POST",
    headers: defaultHeaders,
    data,
    requestId: URL.apPlatformInfoCardList.requestId,
  });

export const apPlatformInfoTest = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.apPlatformInfoTest.value,
    method: "POST",
    headers: defaultHeaders,
    data,
    requestId: URL.apPlatformInfoTest.requestId,
  });

export const userCreate = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.userCreate.value,
    method: "POST",
    headers: defaultHeaders,
    data,
    requestId: URL.userCreate.requestId,
  });

export const noconfigDeviceDataQuery = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.noconfigDeviceDataQuery.value,
    method: "POST",
    headers: defaultHeaders,
    data,
    requestId: URL.noconfigDeviceDataQuery.requestId,
  });

export const systemDeviceInfo = (cid: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.systemDeviceInfo.value + cid,
    method: "GET",
    headers: defaultHeaders,
    requestId: URL.systemDeviceInfo.requestId,
  }).then((res) => {
    if (res.code === 0) {
      return res.data;
    }
  });

export const noconfigDeviceDataInfo = (id: any): Promise<HttpResult<any>> => {
  if (!id) return Promise.resolve({ code: 0, data: {}, codeRemark: "" });
  return Service.http({
    url: URL.noconfigDeviceDataInfo.value + `${id}`,
    method: "GET",
    headers: defaultHeaders,
    requestId: URL.noconfigDeviceDataInfo.requestId,
  }).then((res) => {
    if (res.code === 0) {
      return res.data;
    }
  });
};

export const batchSaveAlarmTask = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.batchSaveAlarmTask.value,
    method: "POST",
    headers: defaultHeaders,
    requestId: URL.batchSaveAlarmTask.requestId,
    data,
  });

export const getTimeDimensionStatistics = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.getTimeDimensionStatistics.value,
    method: "POST",
    headers: defaultHeaders,
    requestId: URL.getTimeDimensionStatistics.requestId,
    data,
  });

export const hideMergeAlarmInfoBatch = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.hideMergeAlarmInfoBatch.value,
    method: "POST",
    headers: defaultHeaders,
    requestId: URL.hideMergeAlarmInfoBatch.requestId,
    data,
  });

export const distributeDevicesCopy = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.distributeDevicesCopy.value,
    method: "POST",
    headers: defaultHeaders,
    requestId: URL.distributeDevicesCopy.requestId,
    data,
  });

export const tagDeviceTree = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.tagDeviceTree.value,
    method: "POST",
    headers: defaultHeaders,
    requestId: URL.tagDeviceTree.requestId,
    data,
  });

export const groupDeviceTree = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.groupDeviceTree.value,
    method: "POST",
    headers: defaultHeaders,
    requestId: URL.groupDeviceTree.requestId,
    data,
  });

export const batchProcessMergeAlarmInfo = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.batchProcessMergeAlarmInfo.value,
    method: "POST",
    headers: defaultHeaders,
    requestId: URL.batchProcessMergeAlarmInfo.requestId,
    data,
  });

export const identityLibraryList = (data: any): Promise<HttpResult<any>> =>
  Service.http({
    url: URL.identityLibraryList.value,
    method: "POST",
    headers: defaultHeaders,
    requestId: URL.identityLibraryList.requestId,
    data,
  });
  
  export const identityLibraryInfo = (data: any): Promise<HttpResult<any>> =>
    Service.http({
      url: URL.identityLibraryInfo.value+data,
      method: "GET",
      headers: defaultHeaders,
      requestId: URL.identityLibraryInfo.requestId,
    });
    
    export const getCategory = (data: any): Promise<HttpResult<any>> =>
      Service.http({
        url: URL.getCategory.value+data,
        method: "GET",
        headers: defaultHeaders,
        requestId: URL.getCategory.requestId,
      });