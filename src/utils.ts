import { Config } from '@cloud-app-dev/vidc';
import { FeatureItemType } from '@cloud-app-dev/vidc/es/Config/interface';
import { compact, uniq } from 'lodash-es';

let authFeatures = [] as FeatureItemType[];

export function getMenusByFeatures() {
  if (authFeatures && authFeatures.length > 0) {
    return authFeatures;
  }
  const ids = Config.featureIds;
  const features = Config.features;
  authFeatures = features.filter((v) => ids.includes(v.id));
  return authFeatures;
}

export function getAuthAppIds() {
  const features = getMenusByFeatures();
  const appIds = uniq(compact(features.map((v) => v.appId)));
  return appIds;
}

export function getAuthByRouter(url?: string) {
  if (!url) {
    return false;
  }
  const ids = Config.featureIds;
  const features = Config.features;
  const item = features.find((v: any) => v.routerUrl === url);
  if (!item) {
    return false;
  }
  return ids.includes(item.id);
}
const colorJson = [
  {
    background: "#FFF0ED",
    color: "#D54941",
  },
  {
    background: "#E3F9E9",
    color: "#2BA471",
  },
  {
    background: "#E8F3FF",
    color: "#165DFF",
  },
  {
    background: "#FFF1E9",
    color: "#E37318",
  },
  {
    background: "#E5F7FF",
    color: "#00B2FF",
  },
  {
    background: "#F9E9FF",
    color: "#BD16FF",
  },
  {
    background: "#FFFBE2",
    color: "#AC971F",
  },
  {
    background: "#E3E1FF",
    color: "#2F3BAB",
  },
  {
    background: "#FFE8F7",
    color: "#E851B3",
  },
  {
    background: "#E8FDFF",
    color: "#31747C",
  },
];
function convertToTenOrLess(testNumber: any) {
  let result = testNumber % colorJson.length;
  // 如果结果是0，我们可以选择返回10（这是非典型的）
  // 或者保持为0，取决于你的需求
  if (result === 0) {
    result = colorJson.length; // 或者保持为0
  }
  return result;
}
export function getModelCol(modelStyle: any, modelIdCurrent: any,key='id') {
  let currentData: any = {};
  // modelList.forEach((item: any, index: number) => {
  //   if (item[key] === modelIdCurrent) {
      currentData.style = {
        color: modelStyle[modelIdCurrent]?.color,
        background: modelStyle[modelIdCurrent]?.background,
      };
      // currentData.title = item.modelNameCn;
    // }
  // });
  return currentData;
}
export function renameDuplicates(arr: any, modelList = {}) {
  const nameCounts: any = {};
  arr?.forEach((item: any, index: any) => {
    const name = getModelCol(modelList, item.modelId).title||item.name_cn;
    if (!nameCounts[name]) {
      nameCounts[name] = 1;
      // arr[index].name = name;
      arr[index].name_cn = name;
    } else {
      nameCounts[name]++;
      const suffix = nameCounts[name];
      const newName = `${name}${suffix}`;
      // arr[index].name = newName;
      arr[index].name_cn = newName;
      if(suffix === 2){
        arr[index - 1].name_cn = name + '1';
      }
    }
  });
  return arr;
}