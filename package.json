{"name": "dvia-video-synthesis-system", "version": "1.0.4", "description": "基于pnpm的工程管理，结构化存储服务的原生应用", "main": "index.js", "type": "module", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite", "build": "vite build", "preview": "vite preview"}, "author": "<PERSON>", "license": "ISC", "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^5.2.5", "@cloud-app-dev/mpegts.js": "^1.7.3", "@cloud-app-dev/vidc": "^4.1.0", "@emotion/css": "^11.11.2", "@turf/center-of-mass": "^6.5.0", "@turf/helpers": "^6.5.0", "@types/lodash-es": "^4.17.8", "@types/node": "^20.5.0", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "ahooks": "^3.7.10", "antd": "^5.8.3", "dayjs": "^1.11.10", "history": "^5.3.0", "hls.js": "^1.5.7", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hotkeys-hook": "^4.5.0", "react-router-dom": "^6.22.2"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "6.0.0", "@vitejs/plugin-react": "^4.2.1", "eslint-plugin-import": "^2.29.1", "less": "^4.2.0", "prettier": "^3.2.5", "tslib": "^2.6.2", "typescript": "^5.3.3", "vite": "^5.1.5"}}