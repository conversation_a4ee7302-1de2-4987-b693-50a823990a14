
underscores_in_headers on;
server {
  listen 2234;

  root /opt/frontend/basic-frontend;
  index index.php index.html index.htm;
  error_page 404 = /404.html;
  try_files $uri /index.html;
  charset utf-8;
  add_header Access-Control-Allow-Origin *;
  client_max_body_size 70m;

  gzip on;
  gzip_min_length 1000;
  gzip_buffers 4 8k;
  gzip_comp_level 1;
  gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss;
  # 利用etag 走协商缓存

  location ^~ /api/ {
    proxy_pass http://**************:9703/api/;
    proxy_redirect default;
    proxy_set_header Host $host:$server_port;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-Host $host:$server_port;
    proxy_set_header X-Forwarded-Server $host:$server_port;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location ^~ /statics {
    alias /opt/frontend/statics;

    #禁用浏览器缓存，走协商缓存304
    expires -1;
  }

  location ^~ /micro-apps {
    alias /opt/frontend;

    #禁用浏览器缓存，走协商缓存304
    expires -1;
  }
}