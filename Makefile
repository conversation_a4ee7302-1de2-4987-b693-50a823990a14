# app name should be overridden.
# ex) production-stage: make build APP_NAME=<APP_NAME>
# ex) development-stage: make build-dev APP_NAME=<APP_NAME>

SHELL := /bin/bash

NAME=ais-frontend
VERSION=1.0.0

.PHONY: help start clean db test

help:
	@grep -E '^[1-9a-zA-Z_-]+:.*?## .*$$|(^#--)' $(MAKEFILE_LIST) \
	| awk 'BEGIN {FS = ":.*?## "}; {printf "\033[32m %-43s\033[0m %s\n", $$1, $$2}' \
	| sed -e 's/\[32m #-- /[33m/'

build: ## Build the container image - Production
	docker build -t ${NAME}:${VERSION}\
		-f Dockerfile .

run: ## Run the container image
	docker run -d -it -p 2234:2234 --name ${NAME} ${NAME}:${VERSION}

pause: ## Pause the containers
	docker container rm -f $(shell docker ps -aq)

clean: ## Clean the images
	docker rmi -f $(shell docker images -a -q)

remove: ## Remove the volumes
	docker volume rm -f $(shell docker volume ls -q)

