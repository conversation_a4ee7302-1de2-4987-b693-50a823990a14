#! /bin/sh

echo "execute the installation script..."

bash ./install.sh

echo "execution complete."

echo "execute build script..."
# 执行编译,并发执行可能内存溢出，所以单个微应用执行
pnpm build

echo "execution complete."

echo "execute the compiled files of the aggregation subproject and generate the tar package..."

# 清除缓存
# rm -r ./build
# mkdir build
# cp -r packages/basic-frontend/build ./build/basic-frontend
# cp -r packages/login/build ./build/login
# cp -r packages/ais-app/build ./build/ais-app
# cp -r packages/system-management/build ./build/system-management
# cp -r packages/statics ./build/statics

# 生成压缩包
# bash ./compress.sh

echo "execution complete."

# 完成
echo "build script execution success!"
